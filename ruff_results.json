[{"code": "ARG002", "name": "unused-method-argument", "count": 47, "fixable": false}, {"code": "B904", "name": "raise-without-from-inside-except", "count": 44, "fixable": false}, {"code": "PLC0415", "name": "import-outside-top-level", "count": 21, "fixable": false}, {"code": "RUF012", "name": "mutable-class-default", "count": 7, "fixable": false}, {"code": "SIM102", "name": "collapsible-if", "count": 6, "fixable": false}, {"code": "PLR0912", "name": "too-many-branches", "count": 4, "fixable": false}, {"code": "PLW2901", "name": "redefined-loop-name", "count": 4, "fixable": false}, {"code": "S110", "name": "try-except-pass", "count": 4, "fixable": false}, {"code": "E722", "name": "bare-except", "count": 3, "fixable": false}, {"code": "PLW0603", "name": "global-statement", "count": 3, "fixable": false}, {"code": "ARG001", "name": "unused-function-argument", "count": 2, "fixable": false}, {"code": "SIM103", "name": "needless-bool", "count": 2, "fixable": false}, {"code": "SIM108", "name": "if-else-block-instead-of-if-exp", "count": 2, "fixable": false}, {"code": "PLR0124", "name": "comparison-with-itself", "count": 1, "fixable": false}, {"code": "PLR0911", "name": "too-many-return-statements", "count": 1, "fixable": false}, {"code": "RUF001", "name": "ambiguous-unicode-character-string", "count": 1, "fixable": false}, {"code": "RUF005", "name": "collection-literal-concatenation", "count": 1, "fixable": false}, {"code": "RUF006", "name": "asyncio-dangling-task", "count": 1, "fixable": false}, {"code": "S324", "name": "hashlib-insecure-hash-function", "count": 1, "fixable": false}, {"code": "SIM105", "name": "suppressible-exception", "count": 1, "fixable": false}]