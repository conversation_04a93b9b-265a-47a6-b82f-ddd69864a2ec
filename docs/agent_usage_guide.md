# Agent Usage Guide

**Multi-Agent Repository RAG System - User Guide**

This guide provides comprehensive instructions for interacting with the multi-agent system, understanding agent behaviors, and optimizing query submission for best results.

---

## Overview

The system consists of four specialized agents coordinated by an intelligent orchestrator:

- **Orchestrator Agent**: Routes queries and coordinates multi-agent responses
- **Technical Design Architect**: Provides architecture analysis and design recommendations
- **Task Planner Agent**: Breaks down requirements into actionable tasks with timelines
- **RAG Retrieval Agent**: Performs direct factual lookups and code searches

---

## Query Submission

### API Endpoint
```bash
POST /api/query
Content-Type: application/json

{
  "query": "Your question here",
  "session_id": "optional-session-id",
  "context": "optional-additional-context"
}
```

### Response Format
```json
{
  "response": "Formatted markdown response",
  "agent_type": "ORCHESTRATOR",
  "confidence": 0.85,
  "sources": ["file1.py:10-20", "docs/design.md:45-60"],
  "processing_time": 2.3,
  "session_id": "session-123"
}
```

---

## Agent Behavior Patterns

### 1. Technical Design Architect Agent

**Triggers**: Architecture, design, SOLID principles, patterns, refactoring queries

**Example Queries**:
- "Analyze the current architecture and identify design patterns"
- "How can we refactor this code to follow SOLID principles?"
- "Design an API for user authentication"
- "What architectural improvements do you recommend?"

**Response Format**:
- Architecture analysis with pattern identification
- SOLID principles compliance scoring
- Design recommendations with implementation guidance
- Compliance notes against project standards

**Confidence Scoring**: 90%+ for architecture-related queries

### 2. Task Planner Agent

**Triggers**: Planning, breakdown, timeline, tasks, implementation, dependencies

**Example Queries**:
- "Break down implementing OAuth authentication into tasks"
- "Create a timeline for the user management feature"
- "What are the dependencies for the API redesign?"
- "Plan the implementation of the caching system"

**Response Format**:
- Structured task breakdown with priorities
- Timeline with dependency analysis
- Risk assessment and mitigation strategies
- 5-phase methodology compliance

**Confidence Scoring**: 85%+ for planning-related queries

### 3. RAG Retrieval Agent

**Triggers**: Factual questions, code lookups, "what is", "how does", "show me"

**Example Queries**:
- "What is OAuth and how is it implemented?"
- "Show me the user authentication code"
- "How does the caching system work?"
- "Find examples of error handling patterns"

**Response Format**:
- Direct factual responses with source citations
- Code examples with file references
- Contextual explanations with documentation links

**Confidence Scoring**: 80%+ for factual/lookup queries

---

## Query Optimization Tips

### 1. Be Specific About Intent

**Good**: "Design a caching system for the LLM responses"
**Better**: "Design a Redis-based caching system for LLM responses with fallback to in-memory cache"

### 2. Provide Context When Needed

**Good**: "How should we implement authentication?"
**Better**: "How should we implement OAuth authentication for our FastAPI backend with JWT tokens?"

### 3. Use Agent-Specific Language

**For Architecture**: Use terms like "design", "architecture", "patterns", "SOLID"
**For Planning**: Use terms like "plan", "breakdown", "tasks", "timeline", "dependencies"
**For Retrieval**: Use terms like "what is", "show me", "how does", "find"

### 4. Multi-Turn Conversations

The system preserves context across conversation turns:

```
Turn 1: "Analyze the current authentication system"
Turn 2: "Now create a plan to migrate it to OAuth"
Turn 3: "What are the security risks in this migration?"
```

---

## Performance Characteristics

### Response Times
- **Simple queries**: <1 second (with caching)
- **Complex analysis**: 2-3 seconds
- **Multi-agent queries**: 3-5 seconds (with parallel processing)

### Caching Behavior
- **Cache hit rate**: 80-90% for repeated queries
- **Cache duration**: 1 hour (configurable)
- **Fallback**: Automatic in-memory cache if Redis unavailable

### Model Selection
- **Simple queries**: GPT-3.5-turbo (faster, cost-effective)
- **Complex analysis**: GPT-4 (higher quality, comprehensive)
- **Automatic selection**: Based on 14 complexity factors

---

## Error Handling

### Common Error Scenarios

1. **No relevant results found**:
   ```json
   {
     "response": "I could not find relevant information for your query. Try rephrasing or being more specific.",
     "confidence": 0.1
   }
   ```

2. **Agent timeout**:
   ```json
   {
     "error": "Agent processing timeout",
     "fallback_response": "Partial response from available agents"
   }
   ```

3. **Invalid query format**:
   ```json
   {
     "error": "Query validation failed",
     "details": "Query must be between 10-1000 characters"
   }
   ```

### Retry Strategies
- **Automatic retries**: 3 attempts with exponential backoff
- **Fallback agents**: Secondary agents activated if primary fails
- **Graceful degradation**: Partial responses when possible

---

## Best Practices

### 1. Session Management
- Use consistent `session_id` for related queries
- Sessions automatically expire after 1 hour of inactivity
- Context preserved across up to 10 conversation turns

### 2. Query Structuring
- Start with broad questions, then narrow down
- Reference previous responses: "Based on the previous analysis..."
- Combine multiple intents: "Analyze the architecture and create an improvement plan"

### 3. Source Citation Usage
- All responses include source file references
- Click through to view full context in original files
- Sources ranked by relevance and authority (docs/ folder prioritized)

### 4. Performance Optimization
- Cache frequently asked questions
- Use specific technical terms for better routing
- Batch related questions in single session

---

## Troubleshooting

### Low Confidence Responses
- **Cause**: Query ambiguity or insufficient context
- **Solution**: Rephrase with more specific terms or provide additional context

### Slow Response Times
- **Cause**: Complex multi-agent processing or cache miss
- **Solution**: Break complex queries into smaller parts

### Missing Sources
- **Cause**: Query too general or no relevant code found
- **Solution**: Be more specific about what you're looking for

### Inconsistent Routing
- **Cause**: Query contains mixed intents
- **Solution**: Separate different types of questions into multiple queries

---

## Advanced Features

### 1. Multi-Agent Coordination
- Orchestrator automatically determines when multiple agents are needed
- Responses synthesized from multiple perspectives
- Confidence scoring aggregated across agents

### 2. Context Preservation
- Conversation history maintained across turns
- Previous responses inform subsequent queries
- Session isolation prevents cross-contamination

### 3. Intelligent Fallbacks
- Primary agent failure triggers secondary agents
- Sequential processing fallback if parallel fails
- Graceful degradation maintains service availability

---

For technical implementation details, see `docs/design.md`.
For development standards and methodology, see `docs/rules.md`.
