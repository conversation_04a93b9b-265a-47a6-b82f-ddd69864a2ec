# Project Structure & Conventions

## **Overview**

This document defines the **authoritative directory structure** for the project. All code, documentation, configuration,
and assets **must** follow this structure to ensure consistency, maintainability, and seamless integration with the RAG
pipeline and AI agents.

The structure is designed to:

- Enable **clear separation of concerns**
- Support **incremental expansion** for future features
- Facilitate **automated knowledge ingestion** for LLM queries
- Provide a **single source of truth** for design and implementation

---

## **Directory Layout**

```text
root/
│
├── README.md                  # Project overview and quick start guide
├── docs/                      # Authoritative project documentation (SINGLE SOURCE OF TRUTH)
│   ├── product.md              # Product specification
│   ├── structure.md            # Project structure documentation
│   ├── tech.md                 # Technology stack specification
│   ├── rules.md                # Development standards and rules
│   ├── requirements.md         # Functional and non-functional requirements
│   ├── design.md               # Technical architecture and design
│   ├── tasks.md                # Implementation task breakdown
│   ├── workflows.md            # Common workflows
│   └── TESTING.md              # Authoritative testing documentation
│
```

---

## **Structure Principles**

1. **Documentation First**

   - All changes must be reflected in `docs/` **before** implementation.
   - Agents ingest `docs/` as primary knowledge for reasoning.

2. **Separation of Agents**

   - Each AI agent (`orchestrator`, `technical_architect`, `task_planner`) lives in its own module.
   - Prompts are stored in `/prompts` subdirectories for traceability.

3. **Centralized RAG Pipeline**

   - All ingestion, retrieval, and ranking logic lives in `src/rag/` to ensure single responsibility.

4. **Immutable Knowledge Base**

   - `data/raw/` contains **unaltered source documents**.
   - Preprocessing results are stored in `data/processed/`.

5. **Environment-Safe Configurations**

   - All environment variables stored in `.env`.
   - Configurations are YAML-based for readability and machine parsing.

---

## **Agent Directory Rules**

| Agent                   | Directory                  | Purpose                                                       |
| ----------------------- | -------------------------- | ------------------------------------------------------------- |
| **Orchestrator**        | `src/orchestrator/`        | Handles workflow control and query routing                    |
| **Technical Architect** | `src/technical_architect/` | Generates system designs and architecture documentation       |
| **Task Planner**        | `src/task_planner/`        | Breaks down requirements into actionable implementation tasks |

---

## **File Naming Conventions**

- Python files: `snake_case.py`
- Documentation: `lowercase-with-dashes.md`
- Config files: `.yaml` (preferred over `.json` for readability)
- Test files: Must mirror source filenames with `_test` suffix.

---
