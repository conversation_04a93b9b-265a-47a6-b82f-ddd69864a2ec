# Environment Configuration Guide

This document explains how to configure the LLM RAG Codebase Query System for different
environments.

## Overview

The system uses a multi-layered configuration approach:

1. **Environment Variables** - Primary configuration method
2. **YAML Configuration Files** - Environment-specific settings
3. **Default Values** - Fallback configuration in code

## Environment Variables

### Required Variables

These variables must be set for the system to function:

```bash
# LLM Provider
OPENAI_API_KEY=your_openai_api_key_here

# GitHub Integration
GITHUB_TOKEN=your_github_token_here

# Security
SECRET_KEY=your_secret_key_here
JWT_SECRET=your_jwt_secret_here
```

### Optional Variables

These variables have sensible defaults but can be customized:

```bash
# Application
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API
API_HOST=127.0.0.1  # Use 0.0.0.0 only in production with proper security
API_PORT=8000
API_WORKERS=1

# Vector Database
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_COLLECTION_NAME=codebase_embeddings

# Redis
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
```

## Configuration Files

### Development (.env)

Copy `.env.template` to `.env` and fill in your values:

```bash
cp .env.template .env
# Edit .env with your actual API keys and tokens
```

### Production (.env.production)

For production deployments, create a separate environment file:

```bash
# Production-specific values
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=WARNING
API_WORKERS=4
```

### YAML Configuration

Environment-specific YAML files provide additional configuration:

- `config/dev/settings.yaml` - Development settings
- `config/staging/settings.yaml` - Staging settings (create as needed)
- `config/prod/settings.yaml` - Production settings

## Environment Setup

### Development

1. Copy the template:

   ```bash
   cp .env.template .env
   ```

2. Set required API keys:

   ```bash
   # Edit .env file
   OPENAI_API_KEY=sk-your-actual-openai-key
   GITHUB_TOKEN=ghp_your-actual-github-token
   ```

3. Start services:
   ```bash
   docker-compose up -d
   ```

### Production

1. Create production environment file:

   ```bash
   cp .env.template .env.production
   ```

2. Set production values:

   ```bash
   # Edit .env.production
   ENVIRONMENT=production
   DEBUG=false
   LOG_LEVEL=WARNING
   API_WORKERS=4
   # ... other production settings
   ```

3. Deploy with production compose:
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

## Configuration Loading

### Backend (Python)

The backend uses Pydantic for configuration management:

```python
from src.config import settings

# Access configuration
api_key = settings.openai_api_key
debug_mode = settings.debug
chroma_url = settings.chroma_url
```

### Frontend (TypeScript)

The frontend uses Zod for configuration validation:

```typescript
import { config, env } from '@/lib/config'

// Access configuration
const apiUrl = config.api.baseUrl
const isDev = env.NODE_ENV === 'development'
```

## Security Considerations

### Sensitive Data

Never commit sensitive data to version control:

- API keys
- Passwords
- Secret keys
- Tokens

### Environment Separation

Use different values for different environments:

- **Development**: Use test/development API keys
- **Staging**: Use staging-specific credentials
- **Production**: Use production credentials with appropriate permissions

### Secret Management

For production deployments, consider using:

- Docker secrets
- Kubernetes secrets
- Cloud provider secret managers (AWS Secrets Manager, Azure Key Vault, etc.)
- HashiCorp Vault

## Validation

The system validates configuration on startup:

### Backend Validation

- Required environment variables are checked
- Data types are validated
- URL formats are verified
- Enum values are validated

### Frontend Validation

- Public environment variables are validated
- API URLs are checked for proper format
- Feature flags are validated

## Troubleshooting

### Common Issues

1. **Missing API Keys**

   ```
   Error: OPENAI_API_KEY is required
   ```

   Solution: Set the required environment variable

2. **Invalid URL Format**

   ```
   Error: NEXT_PUBLIC_API_URL must be a valid URL
   ```

   Solution: Ensure URLs include protocol (http:// or https://)

3. **Configuration Not Loading**
   - Check file permissions
   - Verify file encoding (UTF-8)
   - Ensure no syntax errors in YAML files

### Debug Configuration

Enable debug mode to see loaded configuration:

```bash
DEBUG=true LOG_LEVEL=DEBUG python -m src.main
```

## Best Practices

1. **Use Environment Variables** for sensitive data
2. **Use YAML files** for complex configuration structures
3. **Validate early** - fail fast on invalid configuration
4. **Document defaults** - make it clear what values are optional
5. **Separate environments** - never mix dev and prod configurations
6. **Version control** - track configuration changes (except secrets)
7. **Test configuration** - validate in CI/CD pipelines

## Security Considerations

### Network Binding

- **Development**: Use `API_HOST=127.0.0.1` (localhost only) for security
- **Production**: Use `API_HOST=0.0.0.0` only with proper firewall/proxy configuration
- **Container**: When running in containers, use `API_HOST=0.0.0.0` with network isolation

### Production Security Checklist

- [ ] Set `DEBUG=false` in production
- [ ] Use strong, unique values for `SECRET_KEY` and `JWT_SECRET`
- [ ] Configure proper firewall rules when using `API_HOST=0.0.0.0`
- [ ] Use HTTPS in production (configure reverse proxy)
- [ ] Rotate API keys regularly
- [ ] Monitor access logs

## Examples

### Docker Compose Override

Create `docker-compose.override.yml` for local development:

```yaml
version: '3.8'
services:
  backend:
    environment:
      - DEBUG=true
      - LOG_LEVEL=DEBUG
    volumes:
      - ./local-data:/app/data
```

### Kubernetes ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: rag-config
data:
  ENVIRONMENT: 'production'
  LOG_LEVEL: 'INFO'
  CHROMA_HOST: 'chroma-service'
  REDIS_HOST: 'redis-service'
```
