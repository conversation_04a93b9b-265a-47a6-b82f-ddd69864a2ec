# Technology Stack Specification (`tech.md`)

## 1. **Overview**

This document specifies the **authoritative technology stack** for the project. It defines
**languages, frameworks, libraries, tool versions, and service dependencies** used across all
environments. All contributors must **strictly adhere** to the versions and tools specified here to
maintain reproducibility and stability.

---

## 2. **Programming Languages**

| Language                 | Version  | Purpose                                                        |
| ------------------------ | -------- | -------------------------------------------------------------- |
| **Python**               | 3.13.x   | Backend orchestration, agents, RAG pipeline, ingestion scripts |
| **JavaScript (Node.js)** | 20.x LTS | Frontend integration, interactive dashboards                   |
| **Markdown**             | N/A      | Documentation and project specifications                       |
| **YAML**                 | N/A      | Configurations for pipelines, workflows, and deployment        |

---

## 3. **Core Frameworks & Libraries**

### 3.1. **LLM & AI Orchestration**

| Tool                           | Version    | Purpose                                             |
| ------------------------------ | ---------- | --------------------------------------------------- |
| **LangChain**                  | `>=0.1.x`  | Orchestrating multi-agent workflows and RAG         |
| **LlamaIndex**                 | `>=0.9.x`  | Data indexing and retrieval optimization            |
| **Transformers (HuggingFace)** | `>=4.40.x` | Model loading and fine-tuning                       |
| **OpenAI API / Azure OpenAI**  | Latest     | Access to GPT models for reasoning and conversation |
| **Instructor**                 | Latest     | JSON schema-constrained generation                  |
| **Pydantic**                   | `>=2.x`    | Data validation and schema enforcement              |

### 3.2. **Vector Databases**

| Tool         | Version  | Purpose                                            |
| ------------ | -------- | -------------------------------------------------- |
| **Weaviate** | Latest   | Primary vector store for embeddings                |
| **Chroma**   | Latest   | Alternative lightweight vector store for local use |
| **Milvus**   | Optional | Scalable high-volume vector storage                |

### 3.3. **Data Processing**

| Tool                     | Version | Purpose                                      |
| ------------------------ | ------- | -------------------------------------------- |
| **Pandas**               | Latest  | Data manipulation                            |
| **NumPy**                | Latest  | Numerical computing                          |
| **BeautifulSoup4**       | Latest  | HTML parsing (for repo README/code comments) |
| **PyMuPDF / pdfplumber** | Latest  | PDF extraction for docs ingestion            |

### 3.4. **Caching & Performance Optimization**

| Tool         | Version  | Purpose                                         |
| ------------ | -------- | ----------------------------------------------- |
| **Redis**    | 7.x      | Primary caching backend for LLM responses       |
| **redis-py** | Latest   | Python Redis client with async support          |
| **asyncio**  | Built-in | Parallel agent execution and async coordination |
| **hashlib**  | Built-in | Deterministic cache key generation              |

### 3.5. **Model Selection & Optimization**

| Tool                  | Version | Purpose                                    |
| --------------------- | ------- | ------------------------------------------ |
| **OpenAI GPT-5**      | Latest  | Complex analysis and architectural queries |
| **OpenAI GPT-5-mini** | Latest  | Simple queries and fast responses          |
| **tiktoken**          | Latest  | Token counting for cost optimization       |

**Model Specifications**:

- **GPT-5**: Advanced reasoning, complex analysis, architectural design

  - Use case: High complexity queries (score ≥0.7)
  - Performance: Enhanced capabilities over GPT-4
  - Cost: Premium pricing for advanced features

- **GPT-5-mini**: Optimized for speed and efficiency
  - Use case: Simple queries, fast responses (score <0.7)
  - Performance: ~3x faster than GPT-5
  - Cost: ~10x cheaper than GPT-5

**Upgrade Status**: Successfully migrated from GPT-4/GPT-3.5-turbo (2025-08-11)

---

## 4. **Infrastructure & Deployment**

| Tool / Service       | Version | Purpose                                     |
| -------------------- | ------- | ------------------------------------------- |
| **Docker**           | Latest  | Containerization of services                |
| **Docker Compose**   | Latest  | Multi-container orchestration               |
| **Kubernetes (K8s)** | 1.29.x  | Production orchestration                    |
| **Helm**             | Latest  | Kubernetes deployment automation            |
| **Terraform**        | Latest  | Infrastructure as Code (IaC)                |
| **AWS S3 / GCS**     | Latest  | Object storage for docs and model artifacts |
| **AWS Lambda**       | Latest  | Serverless ingestion tasks                  |
| **GitHub Actions**   | Latest  | CI/CD automation                            |

---

## 5. **Frontend & Integration**

| Tool             | Version | Purpose                               |
| ---------------- | ------- | ------------------------------------- |
| **Next.js**      | 14.x    | Web interface for querying the system |
| **Tailwind CSS** | Latest  | UI styling                            |
| **React Query**  | Latest  | Data fetching & caching               |
| **Socket.IO**    | Latest  | Real-time query execution updates     |

---

## 6. **Testing & Quality Assurance**

| Tool                 | Version | Purpose                                |
| -------------------- | ------- | -------------------------------------- |
| **Pytest**           | Latest  | Python testing framework               |
| **pytest-asyncio**   | Latest  | Async tests for orchestrator pipelines |
| **Coverage.py**      | Latest  | Code coverage reporting                |
| **Pre-commit Hooks** | Latest  | Code linting and format enforcement    |
| **Black**            | Latest  | Python code formatting                 |
| **Ruff**             | Latest  | Python linting                         |
| **ESLint**           | Latest  | JavaScript linting                     |
| **Jest**             | Latest  | JS/TS testing                          |

---

## 7. **Observability & Monitoring**

| Tool              | Version | Purpose               |
| ----------------- | ------- | --------------------- |
| **Prometheus**    | Latest  | Metrics collection    |
| **Grafana**       | Latest  | Metrics visualization |
| **OpenTelemetry** | Latest  | Distributed tracing   |
| **Sentry**        | Latest  | Error tracking        |

---

## 8. **Environment Configuration**

- **Local Development**: `.env.local`
- **Staging**: `.env.staging`
- **Production**: `.env.production`
- All environment variables must be documented in `docs/env.example`.

### 8.1. **Performance Optimization Configuration**

**Required Environment Variables**:

```bash
# LLM Response Caching
ENABLE_LLM_CACHE=true
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_TTL=3600

# Intelligent Model Selection
ENABLE_SMART_MODEL_SELECTION=true
OPENAI_FAST_MODEL=gpt-3.5-turbo
COMPLEXITY_THRESHOLD=0.7

# Parallel Processing
PARALLEL_EXECUTION=true
PARALLEL_TIMEOUT_BUFFER=5.0
```

**Performance Monitoring Variables**:

```bash
# Statistics and Metrics
ENABLE_PERFORMANCE_MONITORING=true
STATS_COLLECTION_INTERVAL=60
PERFORMANCE_LOG_LEVEL=INFO
```

---

## 9. **Version Pinning Policy**

- **All Python dependencies** must be pinned in `pyproject.toml` or `requirements.txt` with exact
  versions.
- **All Node.js dependencies** must be pinned in `package-lock.json`.
- Upgrades require:

  - Pull request with changelog review
  - Testing in staging before merging to `main`

---

## 10. **Prohibited Tools / Practices**

❌ Using unapproved cloud APIs without compliance review ❌ Hardcoding credentials in code ❌ Mixing
versions of libraries outside specified constraints ❌ Skipping vector store sync after ingestion

---
