# Prompt Templates and Orchestration Logic

**Multi-Agent System Prompt Engineering Documentation**

This document provides detailed documentation of prompt templates, orchestration logic, and agent interaction patterns used throughout the multi-agent system.

---

## System Architecture Overview

```
Query → Classifier → Router → Agent(s) → Synthesizer → Response
```

### Core Components:
- **QueryClassifier**: Intent detection and confidence scoring
- **AgentRouter**: Multi-agent routing with fallback strategies
- **ResponseSynthesizer**: Multi-agent response aggregation
- **ConversationContext**: Session and turn management

---

## 1. Query Classification Logic

### Intent Detection Patterns

```python
intent_patterns = {
    QueryIntent.ARCHITECTURE_ANALYSIS: {
        "keywords": ["architecture", "design", "pattern", "structure", "solid", "refactor"],
        "phrases": ["analyze architecture", "design pattern", "code structure"],
        "agent": AgentType.TECHNICAL_ARCHITECT,
        "confidence_threshold": 0.7
    },
    QueryIntent.TASK_PLANNING: {
        "keywords": ["plan", "task", "breakdown", "timeline", "implement", "steps"],
        "phrases": ["create plan", "break down", "implementation steps"],
        "agent": AgentType.TASK_PLANNER,
        "confidence_threshold": 0.6
    },
    QueryIntent.FACTUAL_LOOKUP: {
        "keywords": ["what", "how", "show", "find", "explain", "describe"],
        "phrases": ["what is", "how does", "show me", "find examples"],
        "agent": AgentType.RAG_RETRIEVAL,
        "confidence_threshold": 0.5
    }
}
```

### Confidence Scoring Algorithm

```python
def calculate_confidence(query: str, intent: QueryIntent) -> float:
    base_score = keyword_match_score(query, intent.keywords)
    
    # Adjustments
    if query.endswith("?"):
        base_score += 0.1
    if len(query.split()) < 3:
        base_score *= 0.8
    if contains_technical_terms(query):
        base_score += 0.05
        
    return min(base_score, 1.0)
```

---

## 2. Agent Routing Strategies

### Primary Routing Logic

```python
routing_strategies = {
    "single_agent": single_agent_routing,
    "multi_agent_parallel": parallel_multi_agent_routing,
    "multi_agent_sequential": sequential_multi_agent_routing,
    "fallback_cascade": fallback_cascade_routing
}
```

### Strategy Selection

```python
def determine_routing_strategy(query: str, classification: ClassificationResult) -> str:
    if classification.confidence > 0.8:
        return "single_agent"
    elif contains_multiple_intents(query):
        return "multi_agent_parallel"
    elif requires_sequential_processing(query):
        return "multi_agent_sequential"
    else:
        return "fallback_cascade"
```

### Fallback Hierarchy

```
Primary Agent → Secondary Agents → RAG Retrieval → Error Response
```

---

## 3. Technical Design Architect Prompts

### System Prompt Template

```python
ARCHITECT_SYSTEM_PROMPT = """You are a Technical Design Architect with expertise in:
- Software architecture analysis and design
- SOLID principles and design patterns
- Code quality assessment and improvement recommendations
- Technology stack evaluation and optimization

Your responses must:
1. Provide concrete, actionable technical guidance
2. Reference specific code files and line numbers when applicable
3. Align with project standards documented in docs/ folder
4. Include implementation effort estimates
5. Cite sources for all technical claims

Always prioritize:
- Maintainability and extensibility
- Performance and scalability considerations
- Security and reliability best practices
- Compliance with established coding standards
"""
```

### Query-Specific Prompts

#### Architecture Analysis
```python
ANALYSIS_PROMPT = """Analyze the provided codebase architecture:

**Context**: {search_results}
**Current Analysis**: {architecture_analysis}
**Project Standards**: {standards_context}

Provide:
1. **Architecture Overview**: High-level structure and patterns
2. **Design Patterns**: Identified patterns with examples
3. **SOLID Compliance**: Scoring for each principle (0-100)
4. **Quality Metrics**: Complexity, maintainability, testability
5. **Recommendations**: Specific improvements with effort estimates

Format as structured markdown with source citations.
"""
```

#### Design Generation
```python
DESIGN_PROMPT = """Generate a technical design for: {requirements}

**Current Architecture**: {current_analysis}
**Project Standards**: {project_standards}
**Design Type**: {design_type}

Include:
1. **Design Overview**: High-level approach and rationale
2. **Component Design**: Detailed component specifications
3. **API Design**: Interface definitions and contracts
4. **Implementation Plan**: Step-by-step implementation guidance
5. **Compliance Notes**: Alignment with project standards

Provide concrete, implementable recommendations.
"""
```

---

## 4. Task Planner Agent Prompts

### System Prompt Template

```python
PLANNER_SYSTEM_PROMPT = """You are a Task Planner specializing in:
- Requirement breakdown and task decomposition
- Timeline estimation and dependency analysis
- Risk assessment and mitigation planning
- 5-phase methodology implementation

Your responses must:
1. Follow the 5-phase methodology (Discovery → Planning → Implementation → Verification → Handover)
2. Provide realistic effort estimates and timelines
3. Identify dependencies and critical path items
4. Include risk analysis with mitigation strategies
5. Generate both human-readable and machine-parseable outputs

Always consider:
- Resource constraints and team capacity
- Technical complexity and unknowns
- Integration points and external dependencies
- Quality gates and acceptance criteria
"""
```

### Task Breakdown Prompt

```python
BREAKDOWN_PROMPT = """Break down the following requirements into actionable tasks:

**Requirements**: {requirements}
**Context**: {search_results}
**Methodology**: 5-phase development process

Generate:
1. **Task Hierarchy**: Organized by phases and dependencies
2. **Effort Estimates**: Realistic time estimates for each task
3. **Priority Scoring**: Critical path and priority rankings
4. **Risk Analysis**: Potential blockers and mitigation strategies
5. **Acceptance Criteria**: Clear completion definitions

Output Format:
- Human-readable markdown summary
- Structured JSON task breakdown
- Timeline visualization (text-based)
"""
```

---

## 5. RAG Retrieval Agent Prompts

### System Prompt Template

```python
RAG_SYSTEM_PROMPT = """You are a RAG Retrieval Agent providing factual information from the codebase:

Your responsibilities:
1. Answer factual questions using retrieved context
2. Provide accurate code examples with file references
3. Explain implementation details and patterns
4. Cite all sources with specific file paths and line numbers

Response format:
- Direct, factual answers
- Code examples when relevant
- Source citations for all claims
- Clear explanations of complex concepts

Always:
- Prioritize accuracy over completeness
- Admit when information is not available
- Provide context for code examples
- Reference authoritative sources (docs/ folder priority)
"""
```

### Factual Query Prompt

```python
FACTUAL_PROMPT = """Answer the following question using the provided context:

**Question**: {query}
**Retrieved Context**: {search_results}

Provide:
1. **Direct Answer**: Clear, factual response
2. **Code Examples**: Relevant code snippets with explanations
3. **Implementation Details**: How the code works
4. **Source References**: File paths and line numbers
5. **Related Information**: Additional context if helpful

Be precise and cite all sources.
"""
```

---

## 6. Response Synthesis Logic

### Multi-Agent Aggregation

```python
def synthesize_responses(responses: List[AgentResponse], strategy: str) -> SynthesisResult:
    if strategy == "single_agent":
        return single_response_synthesis(responses[0])
    elif strategy == "multi_agent_parallel":
        return parallel_synthesis(responses)
    elif strategy == "multi_agent_sequential":
        return sequential_synthesis(responses)
    else:
        return fallback_synthesis(responses)
```

### Synthesis Strategies

#### Parallel Synthesis
```python
def parallel_synthesis(responses: List[AgentResponse]) -> str:
    """Combine multiple agent responses into cohesive answer."""
    
    # Sort by confidence and relevance
    sorted_responses = sort_by_confidence(responses)
    
    # Extract unique insights
    insights = extract_unique_insights(sorted_responses)
    
    # Combine with proper attribution
    return format_combined_response(insights)
```

#### Sequential Synthesis
```python
def sequential_synthesis(responses: List[AgentResponse]) -> str:
    """Build upon previous responses in sequence."""
    
    synthesis = responses[0].content
    
    for response in responses[1:]:
        synthesis = build_upon_previous(synthesis, response)
    
    return synthesis
```

---

## 7. Context Management

### Conversation Context Structure

```python
@dataclass
class ConversationContext:
    session_id: str
    turns: List[ConversationTurn]
    created_at: datetime
    last_activity: datetime
    metadata: Dict[str, Any]
    
    def add_turn(self, query: str, response: str, agent_type: AgentType):
        turn = ConversationTurn(
            query=query,
            response=response,
            agent_type=agent_type,
            timestamp=datetime.now()
        )
        self.turns.append(turn)
        self.last_activity = datetime.now()
```

### Context Injection

```python
def inject_conversation_context(prompt: str, context: ConversationContext) -> str:
    if not context.turns:
        return prompt
    
    recent_turns = context.turns[-3:]  # Last 3 turns
    context_summary = summarize_turns(recent_turns)
    
    return f"""Previous conversation context:
{context_summary}

Current query:
{prompt}

Please consider the conversation history when responding."""
```

---

## 8. Error Handling and Fallbacks

### Error Response Templates

```python
ERROR_RESPONSES = {
    "no_results": "I could not find relevant information for your query. Try rephrasing or being more specific.",
    "agent_timeout": "The request timed out. Please try again or break your query into smaller parts.",
    "low_confidence": "I'm not confident about this response. Please provide more context or rephrase your question.",
    "system_error": "A system error occurred. Please try again later."
}
```

### Fallback Prompt

```python
FALLBACK_PROMPT = """The primary agent could not process this query effectively.

**Original Query**: {query}
**Available Context**: {limited_context}

Provide a helpful response based on available information, clearly indicating:
1. What information is available
2. What limitations exist
3. Suggestions for getting better results

Be honest about limitations while being as helpful as possible.
"""
```

---

## 9. Performance Optimization Prompts

### Model Selection Logic

```python
def select_optimal_model(complexity_analysis: ComplexityAnalysis) -> str:
    if complexity_analysis.overall_score > 0.7:
        return "gpt-4"  # Complex queries need advanced reasoning
    elif complexity_analysis.overall_score > 0.4:
        return "gpt-3.5-turbo"  # Moderate complexity
    else:
        return "gpt-3.5-turbo"  # Simple queries, cost-effective
```

### Caching Key Generation

```python
def generate_cache_key(prompt: str, system_prompt: str, model: str) -> str:
    content = f"{prompt}|{system_prompt}|{model}"
    return hashlib.sha256(content.encode()).hexdigest()
```

---

## 10. Quality Assurance

### Response Validation

```python
def validate_response(response: str, agent_type: AgentType) -> bool:
    checks = [
        has_proper_formatting(response),
        contains_source_citations(response),
        meets_length_requirements(response),
        follows_agent_guidelines(response, agent_type)
    ]
    return all(checks)
```

### Confidence Calibration

```python
def calibrate_confidence(raw_confidence: float, response_quality: float) -> float:
    # Adjust confidence based on response quality metrics
    calibrated = raw_confidence * response_quality
    
    # Apply bounds
    return max(0.1, min(0.95, calibrated))
```

---

For implementation details, see the source code in `src/orchestrator/` and `src/agents/`.
For usage examples, see `docs/agent_usage_guide.md`.
