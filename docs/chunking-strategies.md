# Chunking Strategies Reference

This document provides detailed information about the chunking strategies implemented in the system, their use cases, and best practices for optimal results.

## Strategy Overview

The chunking system uses a strategy pattern to handle different file types with specialized approaches:

| Strategy | File Types | Key Features |
|----------|------------|--------------|
| **CodeChunkingStrategy** | .py, .js, .ts, .java, .cpp, .go, etc. | Function/class boundaries, context preservation |
| **MarkdownChunkingStrategy** | .md, .markdown, .rst | Header-based hierarchy, code block extraction |
| **TextChunkingStrategy** | .txt, .log, README, LICENSE | Paragraph-based, semantic boundaries |
| **ConfigChunkingStrategy** | .json, .yaml, .toml, .ini, .env | Structure-aware, section-based |

## Code Chunking Strategy

### Supported Languages

- **Python**: Classes, functions, methods, decorators
- **JavaScript/TypeScript**: Classes, functions, arrow functions, methods
- **Java**: Classes, interfaces, methods
- **C/C++**: Classes, functions, structs
- **Go**: Structs, interfaces, functions, methods
- **PHP**: Classes, functions, methods
- **Ruby**: Classes, modules, methods
- **And more...**

### Chunking Approach

1. **Function-Level Chunking**: Each function becomes a separate chunk
2. **Class-Level Chunking**: Classes with their methods grouped appropriately
3. **Context Preservation**: Maintains class names, function names, module information
4. **Hierarchical Relationships**: Links methods to their parent classes

### Example: Python Code

```python
# Input file: data_processor.py
class DataProcessor:
    """Processes data from various sources."""
    
    def __init__(self, config):
        self.config = config
    
    def load_data(self, source):
        """Load data from source."""
        if source == "file":
            return self._load_from_file()
        return []
    
    def _load_from_file(self):
        """Private method to load from file."""
        return []

def standalone_function():
    """Standalone utility function."""
    return "result"
```

**Generated Chunks**:

1. **Chunk 1** (CODE_CLASS):
   - Content: Class definition with `__init__` method
   - Context: `class_name="DataProcessor", function_name="__init__"`

2. **Chunk 2** (CODE_FUNCTION):
   - Content: `load_data` method
   - Context: `class_name="DataProcessor", function_name="load_data"`

3. **Chunk 3** (CODE_FUNCTION):
   - Content: `_load_from_file` method
   - Context: `class_name="DataProcessor", function_name="_load_from_file"`

4. **Chunk 4** (CODE_FUNCTION):
   - Content: `standalone_function`
   - Context: `function_name="standalone_function", module_name="data_processor"`

### Best Practices

- **Keep functions focused**: Smaller, well-defined functions create better chunks
- **Use descriptive names**: Function and class names become part of the context
- **Document code**: Docstrings are included in chunks for better context
- **Organize logically**: Related functions in the same class/module

## Markdown Chunking Strategy

### Chunking Approach

1. **Header-Based Sections**: Each header (H1-H6) starts a new section
2. **Hierarchical Context**: Maintains parent header relationships
3. **Code Block Extraction**: Separates code blocks as individual chunks
4. **Content Preservation**: Keeps markdown formatting for context

### Example: Documentation File

```markdown
# API Documentation

This document describes the API endpoints.

## Authentication

All requests require authentication.

### OAuth 2.0

Use OAuth 2.0 for secure authentication:

```python
import requests

def authenticate(client_id, client_secret):
    # Authentication logic
    return token
```

## Endpoints

### GET /users

Returns a list of users.

```json
{
  "users": [
    {"id": 1, "name": "John"}
  ]
}
```
```

**Generated Chunks**:

1. **Chunk 1** (MARKDOWN_SECTION):
   - Content: "# API Documentation\nThis document describes..."
   - Context: `section_headers=["API Documentation"]`

2. **Chunk 2** (MARKDOWN_SECTION):
   - Content: "## Authentication\nAll requests require..."
   - Context: `section_headers=["API Documentation", "Authentication"]`

3. **Chunk 3** (MARKDOWN_SECTION):
   - Content: "### OAuth 2.0\nUse OAuth 2.0 for..."
   - Context: `section_headers=["API Documentation", "Authentication", "OAuth 2.0"]`

4. **Chunk 4** (MARKDOWN_CODE_BLOCK):
   - Content: Python authentication code
   - Context: `section_headers=["API Documentation", "Authentication", "OAuth 2.0"]`

5. **Chunk 5** (MARKDOWN_SECTION):
   - Content: "## Endpoints\n### GET /users..."
   - Context: `section_headers=["API Documentation", "Endpoints"]`

6. **Chunk 6** (MARKDOWN_CODE_BLOCK):
   - Content: JSON response example
   - Context: `section_headers=["API Documentation", "Endpoints", "GET /users"]`

### Best Practices

- **Use clear header hierarchy**: Logical H1 → H2 → H3 structure
- **Keep sections focused**: Each section should cover one topic
- **Separate code examples**: Code blocks become searchable chunks
- **Use descriptive headers**: Headers become part of the search context

## Text Chunking Strategy

### Chunking Approach

1. **Paragraph-Based**: Splits on double newlines (paragraph breaks)
2. **Sentence Grouping**: Groups sentences to reach optimal chunk size
3. **Overlap Handling**: Configurable overlap between chunks
4. **Content Normalization**: Cleans up formatting inconsistencies

### Example: README File

```text
Project Overview

This project implements a RAG system for codebase querying. It allows users to ask questions about code repositories and get intelligent answers based on the codebase content.

The system consists of several components including ingestion, chunking, embedding, and retrieval. Each component is designed to be modular and extensible.

Installation

To install the project, follow these steps:

1. Clone the repository
2. Install dependencies
3. Configure the environment
4. Run the application

The installation process is straightforward and should take only a few minutes.
```

**Generated Chunks**:

1. **Chunk 1** (TEXT_PARAGRAPH):
   - Content: "Project Overview\n\nThis project implements..."
   - Context: Basic text context

2. **Chunk 2** (TEXT_PARAGRAPH):
   - Content: "Installation\n\nTo install the project..."
   - Context: Basic text context

### Best Practices

- **Use clear paragraph breaks**: Double newlines help define logical sections
- **Keep paragraphs focused**: Each paragraph should cover one idea
- **Use consistent formatting**: Helps with better chunk boundaries
- **Include context clues**: First sentences should provide context

## Configuration Chunking Strategy

### Supported Formats

- **JSON**: Object-based chunking by top-level keys
- **YAML**: Section-based chunking with hierarchy preservation
- **TOML**: Section-based chunking with `[section]` headers
- **INI**: Section-based chunking with `[section]` headers
- **Properties/ENV**: Key-value grouping by prefixes

### Example: JSON Configuration

```json
{
  "database": {
    "host": "localhost",
    "port": 5432,
    "name": "myapp",
    "credentials": {
      "username": "admin",
      "password": "secret"
    }
  },
  "api": {
    "version": "v1",
    "base_url": "https://api.example.com",
    "timeout": 30,
    "retries": 3
  },
  "logging": {
    "level": "INFO",
    "file": "app.log",
    "format": "%(asctime)s - %(levelname)s - %(message)s"
  }
}
```

**Generated Chunks**:

1. **Chunk 1** (CONFIG_SECTION):
   - Content: Database configuration section
   - Context: `namespace="database"`

2. **Chunk 2** (CONFIG_SECTION):
   - Content: API configuration section
   - Context: `namespace="api"`

3. **Chunk 3** (CONFIG_SECTION):
   - Content: Logging configuration section
   - Context: `namespace="logging"`

### Best Practices

- **Group related settings**: Organize configuration into logical sections
- **Use descriptive keys**: Key names become part of the search context
- **Maintain hierarchy**: Nested structures are preserved in chunks
- **Document configuration**: Comments help provide context

## Strategy Selection Logic

The system automatically selects the appropriate strategy based on:

1. **File Extension**: Primary method for strategy selection
2. **Language Detection**: For code files, uses language metadata
3. **Content Analysis**: Fallback analysis of file content
4. **MIME Type**: Additional validation for file type

### Selection Priority

```python
def get_strategy(file_metadata):
    file_extension = get_extension(file_metadata.file_path)
    language = file_metadata.language
    
    # 1. Markdown files
    if file_extension in ['md', 'markdown', 'rst']:
        return MarkdownChunkingStrategy()
    
    # 2. Configuration files
    if file_extension in ['json', 'yaml', 'yml', 'toml', 'ini', 'cfg', 'conf']:
        return ConfigChunkingStrategy()
    
    # 3. Code files (by language or extension)
    if language or file_extension in CODE_EXTENSIONS:
        return CodeChunkingStrategy()
    
    # 4. Default to text strategy
    return TextChunkingStrategy()
```

## Performance Considerations

### Token Counting

- Uses tiktoken (GPT-4 tokenizer) for accurate token counts
- Falls back to character-based estimation if tokenizer fails
- Caches tokenization results for performance

### Memory Management

- Processes files in configurable batches
- Clears encoding detection cache periodically
- Limits maximum file size to prevent memory issues

### Parallel Processing

- Async/await for concurrent file processing
- Configurable batch sizes based on system resources
- Error isolation prevents single file failures from stopping processing

## Quality Assurance

### Chunk Validation

- **Size Validation**: Ensures chunks meet minimum/maximum size requirements
- **Content Validation**: Filters out empty or invalid chunks
- **Duplicate Detection**: Identifies and handles duplicate chunk IDs
- **Context Validation**: Ensures proper context information is preserved

### Error Handling

- **Graceful Degradation**: Falls back to simpler strategies if parsing fails
- **Detailed Logging**: Comprehensive error reporting with context
- **Recovery Mechanisms**: Continues processing despite individual file failures
- **Validation Reporting**: Provides detailed quality metrics

## Customization and Extension

### Adding New Strategies

```python
class CustomStrategy(BaseChunkingStrategy):
    def get_strategy_name(self) -> str:
        return "custom"
    
    def is_applicable(self, file_metadata: FileMetadata) -> bool:
        return file_metadata.file_path.endswith('.custom')
    
    async def chunk_content(self, content: str, file_metadata: FileMetadata):
        # Custom implementation
        pass

# Register the strategy
factory.register_strategy("custom", CustomStrategy)
```

### Configuration Tuning

```python
# Adjust for different use cases
settings = Settings(
    chunk_size=800,        # Smaller chunks for detailed search
    chunk_overlap=150,     # More overlap for better context
    min_chunk_size=50,     # Allow smaller chunks
    max_chunk_size=1500    # Limit large chunks
)
```

## Monitoring and Analytics

### Processing Statistics

- Files processed vs. skipped
- Chunks created by type and strategy
- Processing time and performance metrics
- Error rates and failure patterns

### Quality Metrics

- Chunk size distribution
- Context preservation rates
- Strategy selection accuracy
- Validation pass/fail rates

This comprehensive chunking system provides the foundation for effective RAG applications by creating semantically meaningful, context-rich chunks that preserve the structure and relationships present in the original codebase.
