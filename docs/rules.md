# Project Rules and Standards (`rules.md`)

## 1. **Overview**

This document defines the **mandatory rules and standards** governing code quality, testing, development methodologies,
project management, architecture, error handling, and workflows. All contributors must **strictly adhere** to these
rules to maintain a robust, scalable, and maintainable system. This is an **authoritative** document — deviations must
be formally approved.

---

## 2. **Code Quality Standards**

### 2.1 Language and Style

- Follow [PEP 8](https://peps.python.org/pep-0008/) for Python code style.
- Use **snake_case** for variable and function names.
- Class names use **PascalCase**.
- Keep line length ≤ 88 characters.
- Docstrings for all public classes, methods, and functions using Google style.
- Avoid code duplication: prefer reusable functions and modules.
- Prefer clear and expressive variable names.

### 2.2 Formatting and Linting

- Use **Black** for code formatting with default settings.
- Use **Ruff** for linting; all lint warnings must be resolved before merging.
- JavaScript/TypeScript code must comply with **ESLint** and **Prettier** standards.
- Enforce pre-commit hooks for linting and formatting checks (`pre-commit` framework).

### 2.3 Code Reviews

- All code changes require at least **one peer review** before merging.
- Reviewers must verify adherence to coding standards, correctness, and test coverage.
- No merge without passing CI checks including linting and tests.

---

## 3. **Testing Standards**

Testing requirements are detailed in [`docs/TESTING.md`](./TESTING.md). Highlights:

- All new code must have **unit tests** covering >80% of new logic.
- **Integration tests** must validate key workflows, especially ingestion, retrieval, and multi-agent interactions.
- Use **pytest** with fixtures for test isolation.
- Tests should be deterministic and not depend on external state.
- Automated tests must run on every pull request via CI.
- Performance and regression tests are mandatory before releases.
- Tests must be categorized in `tests/unit/`, `tests/integration/`, and `tests/e2e/`.

---

## 4. **Development Methodologies and Principles**

### 4.1 5-Phase Implementation & Testing Methodology

All project work **must** follow the mandated end-to-end methodology:

| Phase                           | Description                                                                                          |
| ------------------------------- | ---------------------------------------------------------------------------------------------------- |
| **1. Discovery & Analysis**     | Identify needs, requirements, constraints. Gather information and perform gap analysis.              |
| **2. Task Planning**            | Break down requirements into concrete tasks, estimate effort, assign ownership. See `docs/tasks.md`. |
| **3. Implementation**           | Develop features/tests adhering to quality rules. Perform peer reviews.                              |
| **4. Verification**             | Execute tests, perform manual and automated QA, fix defects.                                         |
| **5. Documentation & Handover** | Update docs (`docs/`), hand over to stakeholders or operations teams.                                |

- Enforcement: No phase can be skipped. Progression to next phase requires approval and documentation.
- See [`docs/workflows.md`](./workflows.md) for detailed workflows aligned with this methodology.

### 4.2 Agile and Iterative Development

- Adopt **Agile principles**: iterative delivery, continuous feedback, adaptive planning.
- Use **user stories** and **acceptance criteria** from `docs/requirements.md`.
- Tasks are tracked and updated in project management tools aligned with `docs/tasks.md`.

---

## 5. **Project Management and Workflow Rules**

### 5.1 Branching Strategy

- Use **GitFlow** or **trunk-based development** as decided by the team.
- Feature branches named as `feature/<JIRA-ID>-short-description`.
- Hotfix branches named as `hotfix/<issue-description>`.
- Pull requests require linked issue or task.

### 5.2 Commit Messages

- Follow **Conventional Commits** format:

  - `feat: Add new query router`
  - `fix: Correct tokenization bug`
  - `docs: Update design.md with architecture diagram`

- Include issue/task references.

### 5.3 Continuous Integration (CI)

- CI pipeline runs:

  - Linting & formatting checks
  - Unit & integration tests
  - Security scans (dependency vulnerability)
  - Build validation (Docker, packages)

- Fail fast on any error; all failures must be addressed before merge.

---

## 6. **Architectural Rules**

### 6.1 Modularity and Separation of Concerns

- Separate agents into distinct modules:

  - Orchestrator
  - Technical Design Architect
  - Task Planner

- RAG pipeline components (ingestion, retrieval, ranking) must be isolated.

### 6.2 Dependency Management

- All dependencies must be explicitly declared and version-pinned in `requirements.txt` or `pyproject.toml`.
- Avoid circular dependencies.
- Use dependency injection where applicable for testability.

### 6.3 Configuration

- All runtime configurations must be environment-driven (`.env` files or env vars).
- No hardcoded secrets or credentials in source code.
- Use config files in `config/` for static settings.

---

## 7. **Unified Error Handling**

- Use **centralized error handling** in API endpoints and agent modules.
- Define custom exceptions for common error categories:

  - `IngestionError`
  - `RetrievalError`
  - `AgentProcessingError`
  - `AuthenticationError`

- All errors must log detailed context and stack traces.
- Client-facing errors should be sanitized and user-friendly.
- Errors triggering retries or fallbacks must be annotated and handled explicitly.

---

## 8. **Development Workflow Rules**

### 8.1 Local Development

- Follow documented **setup instructions** (`README.md`).
- Use virtual environments or containers for consistency.
- Run lint and tests locally before committing.

### 8.2 Pull Requests

- Open pull requests against `develop` or `main` branch as per workflow.
- Include clear description, testing instructions, and linked issues.
- Engage at least one reviewer before merge.

### 8.3 Code Documentation

- Update relevant doc files for any architectural or functional changes.
- Add inline code comments for complex logic.
- Document all new modules in `docs/structure.md` and `docs/design.md` as needed.

### 8.4 Release Management

- Tag releases with semantic versioning: `vMAJOR.MINOR.PATCH`.
- Attach release notes summarizing features, fixes, and breaking changes.
- Perform final verification (QA) before production deployment.

---

## 9. **Enforcement and Compliance**

- Violations of rules must be flagged in code reviews or CI pipelines.
- Repeat violations lead to escalations to project leads.
- Automated tooling (linters, tests, security scanners) will enforce compliance wherever possible.

---

## 10. **References**

- **Testing standards**: [`docs/TESTING.md`](./TESTING.md)
- **Development workflows & phases**: [`docs/workflows.md`](./workflows.md)
- **Architecture & design**: [`docs/design.md`](./design.md)
- **Product and requirements**: [`docs/product.md`](./product.md), [`docs/requirements.md`](./requirements.md)

---
