# Embedding System Usage Guide

## Quick Start

### 1. Basic Setup

```python
from src.ingestion import DefaultEmbeddingPipeline
from src.config import get_settings

# Load configuration
settings = get_settings()

# Create embedding pipeline
pipeline = DefaultEmbeddingPipeline(settings)
```

### 2. Process Chunks

```python
# Assuming you have chunks from the chunking pipeline
from src.ingestion import Default<PERSON>hunking<PERSON>ipeline

# Get chunks
chunking_pipeline = DefaultChunkingPipeline(settings)
chunks = await chunking_pipeline.process_files(file_metadata_list, repository_path)

# Generate embeddings
embedded_chunks = await pipeline.process_chunks(chunks)

# Store in vector database
success = await pipeline.store_embeddings(embedded_chunks)
```

### 3. Search Similar Content

```python
# Search for similar chunks
results = await pipeline.search_similar_chunks(
    query="authentication implementation",
    top_k=10
)

for result in results:
    print(f"Score: {result.similarity_score:.3f}")
    print(f"File: {result.chunk.file_metadata.file_path}")
    print(f"Content: {result.content[:100]}...")
    print("---")
```

## Configuration Examples

### OpenAI Configuration

```bash
# .env file
OPENAI_API_KEY=your_api_key_here
EMBEDDING_PROVIDER=openai
EMBEDDING_MODEL=text-embedding-3-large
EMBEDDING_BATCH_SIZE=100
EMBEDDING_MAX_RETRIES=3
EMBEDDING_TIMEOUT=60
```

### Local Model Configuration

```bash
# .env file
EMBEDDING_PROVIDER=local
LOCAL_EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_BATCH_SIZE=50
EMBEDDING_NORMALIZE=true
```

### ChromaDB Configuration

```bash
# .env file
VECTOR_STORE_PROVIDER=chromadb
CHROMA_COLLECTION_NAME=my_codebase
CHROMA_PERSIST_DIRECTORY=./data/chroma
CHROMA_HOST=localhost
CHROMA_PORT=8001
```

## Advanced Usage

### Custom Embedding Client

```python
from src.ingestion.embedding import EmbeddingClientFactory

# Create specific client
client = EmbeddingClientFactory.create_client(settings)

# Generate embeddings directly
texts = ["function definition", "class implementation"]
embeddings = await client.generate_embeddings(texts)

# Get model information
info = client.get_model_info()
print(f"Model: {info['model']}, Dimensions: {info['dimension']}")
```

### Custom Vector Store

```python
from src.ingestion.vector import VectorStoreFactory

# Create vector store
store = VectorStoreFactory.create_vector_store(settings)

# Add embeddings with custom metadata
embeddings = [[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]]
metadata = [
    {"file_path": "src/auth.py", "function": "login"},
    {"file_path": "src/auth.py", "function": "logout"}
]
ids = ["auth_login", "auth_logout"]

await store.add_embeddings(embeddings, metadata, ids)

# Search with filters
results = await store.search_similar(
    query_embedding=[0.2, 0.3, 0.4],
    top_k=5,
    filters={"file_path": "src/auth.py"}
)
```

### Batch Processing with Custom Settings

```python
# Create pipeline with custom settings
pipeline = DefaultEmbeddingPipeline(
    settings,
    embedding_client=custom_client,
    vector_store=custom_store
)

# Process large datasets in batches
all_chunks = get_large_chunk_list()
batch_size = 100

for i in range(0, len(all_chunks), batch_size):
    batch = all_chunks[i:i + batch_size]
    embedded_chunks = await pipeline.process_chunks(batch)
    await pipeline.store_embeddings(embedded_chunks)
    
    # Monitor progress
    stats = await pipeline.get_pipeline_stats()
    print(f"Processed {stats['chunks_processed']} chunks")
```

## Integration Patterns

### Complete Repository Processing

```python
async def process_repository(repository_url: str):
    """Complete repository processing workflow."""
    
    # 1. Ingest repository
    github_connector = GitHubConnector(settings)
    result = await github_connector.ingest_repository(repository_url)
    
    # 2. Generate chunks
    chunking_pipeline = DefaultChunkingPipeline(settings)
    chunks = await chunking_pipeline.process_files(
        result["file_metadata"], 
        Path(result["local_path"])
    )
    
    # 3. Generate embeddings
    embedding_pipeline = DefaultEmbeddingPipeline(settings)
    embedded_chunks = await embedding_pipeline.process_chunks(chunks)
    
    # 4. Store embeddings
    await embedding_pipeline.store_embeddings(embedded_chunks)
    
    return {
        "files_processed": len(result["file_metadata"]),
        "chunks_generated": len(chunks),
        "embeddings_stored": len(embedded_chunks)
    }
```

### Incremental Updates

```python
async def update_repository(repository_url: str, changed_files: List[str]):
    """Update embeddings for changed files only."""
    
    # Get existing collection info
    store = VectorStoreFactory.create_vector_store(settings)
    collection_info = await store.get_collection_info()
    
    # Process only changed files
    for file_path in changed_files:
        # Remove old embeddings for this file
        old_ids = await get_embedding_ids_for_file(file_path)
        if old_ids:
            await store.delete_embeddings(old_ids)
        
        # Generate new chunks and embeddings
        file_chunks = await process_single_file(file_path)
        embedded_chunks = await embedding_pipeline.process_chunks(file_chunks)
        await embedding_pipeline.store_embeddings(embedded_chunks)
```

### Search with Context

```python
async def search_with_context(query: str, file_types: List[str] = None):
    """Search with additional context and filtering."""
    
    # Build filters
    filters = {}
    if file_types:
        filters["language"] = {"$in": file_types}
    
    # Search
    results = await pipeline.search_similar_chunks(
        query=query,
        top_k=20,
        filters=filters
    )
    
    # Group by file for context
    file_results = {}
    for result in results:
        file_path = result.chunk.file_metadata.file_path
        if file_path not in file_results:
            file_results[file_path] = []
        file_results[file_path].append(result)
    
    return file_results
```

## Performance Optimization

### Optimal Batch Sizes

```python
# For OpenAI API
EMBEDDING_BATCH_SIZE = 100  # Good balance of speed and rate limits

# For local models
EMBEDDING_BATCH_SIZE = 50   # Depends on available memory

# For pipeline processing
EMBEDDING_PIPELINE_BATCH_SIZE = 25  # Smaller for better memory usage
```

### Memory Management

```python
# Process large repositories in chunks
async def process_large_repository(chunks: List[Chunk]):
    """Process large repositories with memory management."""
    
    pipeline = DefaultEmbeddingPipeline(settings)
    batch_size = 100
    
    for i in range(0, len(chunks), batch_size):
        batch = chunks[i:i + batch_size]
        
        # Process batch
        embedded_chunks = await pipeline.process_chunks(batch)
        await pipeline.store_embeddings(embedded_chunks)
        
        # Clear cache periodically
        if i % 1000 == 0:
            pipeline.clear_cache()
        
        # Log progress
        print(f"Processed {i + len(batch)}/{len(chunks)} chunks")
```

### Caching Strategy

```python
# Enable caching for repositories with duplicate content
EMBEDDING_CACHE_ENABLED = True

# Monitor cache performance
stats = await pipeline.get_pipeline_stats()
cache_hit_rate = stats["cache_hits"] / (stats["cache_hits"] + stats["embeddings_generated"])
print(f"Cache hit rate: {cache_hit_rate:.2%}")
```

## Error Handling

### Robust Processing

```python
async def robust_embedding_processing(chunks: List[Chunk]):
    """Process chunks with comprehensive error handling."""
    
    pipeline = DefaultEmbeddingPipeline(settings)
    successful_chunks = []
    failed_chunks = []
    
    for chunk in chunks:
        try:
            embedded_chunks = await pipeline.process_chunks([chunk])
            if embedded_chunks:
                await pipeline.store_embeddings(embedded_chunks)
                successful_chunks.extend(embedded_chunks)
            else:
                failed_chunks.append(chunk)
                
        except EmbeddingGenerationError as e:
            logger.error(f"Failed to generate embedding for {chunk.chunk_id}: {e}")
            failed_chunks.append(chunk)
            
        except VectorStoreError as e:
            logger.error(f"Failed to store embedding for {chunk.chunk_id}: {e}")
            failed_chunks.append(chunk)
    
    return {
        "successful": len(successful_chunks),
        "failed": len(failed_chunks),
        "failed_chunks": failed_chunks
    }
```

### Retry Logic

```python
async def process_with_retries(chunks: List[Chunk], max_retries: int = 3):
    """Process chunks with custom retry logic."""
    
    pipeline = DefaultEmbeddingPipeline(settings)
    
    for attempt in range(max_retries):
        try:
            embedded_chunks = await pipeline.process_chunks(chunks)
            await pipeline.store_embeddings(embedded_chunks)
            return embedded_chunks
            
        except Exception as e:
            if attempt == max_retries - 1:
                raise
            
            wait_time = 2 ** attempt  # Exponential backoff
            logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {wait_time}s...")
            await asyncio.sleep(wait_time)
```

## Monitoring and Debugging

### Statistics Monitoring

```python
async def monitor_pipeline_performance():
    """Monitor pipeline performance metrics."""
    
    stats = await pipeline.get_pipeline_stats()
    
    print("=== Embedding Pipeline Statistics ===")
    print(f"Chunks processed: {stats['chunks_processed']}")
    print(f"Embeddings generated: {stats['embeddings_generated']}")
    print(f"Embeddings cached: {stats['embeddings_cached']}")
    print(f"Cache hits: {stats['cache_hits']}")
    print(f"Quality failures: {stats['quality_failures']}")
    print(f"Processing time: {stats['processing_time']:.2f}s")
    
    if "embedding_client" in stats:
        client_stats = stats["embedding_client"]
        print(f"API requests: {client_stats.get('total_requests', 0)}")
        print(f"Total tokens: {client_stats.get('total_tokens', 0)}")
        print(f"Total cost: ${client_stats.get('total_cost', 0):.4f}")
    
    if "vector_store" in stats:
        store_stats = stats["vector_store"]
        print(f"Total embeddings stored: {store_stats.get('total_embeddings', 0)}")
```

### Debug Logging

```python
import logging

# Enable debug logging
logging.getLogger('src.ingestion.embedding').setLevel(logging.DEBUG)
logging.getLogger('src.ingestion.vector').setLevel(logging.DEBUG)

# Process with detailed logging
embedded_chunks = await pipeline.process_chunks(chunks)
```

## Testing

### Unit Testing

```python
import pytest
from unittest.mock import Mock, AsyncMock

@pytest.mark.asyncio
async def test_embedding_pipeline():
    """Test embedding pipeline functionality."""
    
    # Mock dependencies
    mock_client = Mock()
    mock_client.generate_embeddings = AsyncMock(return_value=[[0.1, 0.2, 0.3]])
    
    mock_store = Mock()
    mock_store.add_embeddings = AsyncMock(return_value=True)
    
    # Create pipeline
    pipeline = DefaultEmbeddingPipeline(
        settings,
        embedding_client=mock_client,
        vector_store=mock_store
    )
    
    # Test processing
    chunks = [create_test_chunk()]
    embedded_chunks = await pipeline.process_chunks(chunks)
    
    assert len(embedded_chunks) == 1
    assert len(embedded_chunks[0].embedding) == 3
```

### Integration Testing

```python
@pytest.mark.asyncio
async def test_end_to_end_processing():
    """Test complete end-to-end processing."""
    
    # Use real components with test configuration
    test_settings = get_test_settings()
    pipeline = DefaultEmbeddingPipeline(test_settings)
    
    # Process test chunks
    test_chunks = create_test_chunks()
    embedded_chunks = await pipeline.process_chunks(test_chunks)
    
    # Verify storage
    success = await pipeline.store_embeddings(embedded_chunks)
    assert success
    
    # Test search
    results = await pipeline.search_similar_chunks("test query")
    assert len(results) > 0
```
