# Chunking Pipeline Usage Guide

This guide provides comprehensive documentation for using the chunking pipeline to process repository content into semantic chunks for RAG applications.

## Overview

The chunking pipeline is a sophisticated system that processes files from the GitHub Connector and creates intelligent, context-aware chunks suitable for embedding and retrieval. It automatically selects appropriate chunking strategies based on file type and content structure.

## Quick Start

### Basic Usage

```python
from src.ingestion import DefaultChunkingPipeline, GitHubConnector
from src.config import get_settings

# Initialize components
settings = get_settings()
github_connector = GitHubConnector(settings)
chunking_pipeline = DefaultChunkingPipeline(settings)

# Ingest repository
repo_result = await github_connector.ingest_repository(
    "https://github.com/user/repo"
)

# Create chunks
chunks = await chunking_pipeline.process_files(
    repo_result['file_metadata'],
    Path(repo_result['local_path'])
)

print(f"Created {len(chunks)} chunks")
```

### Processing Single Files

```python
from pathlib import Path
from datetime import datetime
from src.ingestion import DefaultChunkingPipeline, FileMetadata

# Create file metadata
file_metadata = FileMetadata(
    file_path="src/main.py",
    file_size=2048,
    file_type="text",
    last_modified=datetime.now(),
    language="python"
)

# Process single file
chunks = await chunking_pipeline.process_single_file(
    file_metadata, 
    Path("/path/to/repository")
)
```

## Configuration

### Environment Variables

```bash
# Chunking configuration
CHUNK_SIZE=1000                    # Default chunk size in tokens
CHUNK_OVERLAP=200                  # Overlap between chunks
MAX_CHUNK_SIZE=2000               # Maximum chunk size
MIN_CHUNK_SIZE=100                # Minimum chunk size
CHUNK_BATCH_SIZE=20               # Parallel processing batch size
PRESERVE_CODE_STRUCTURE=true      # Use AST-based chunking for code
MARKDOWN_HEADER_SPLIT=true        # Split markdown on headers
```

### Programmatic Configuration

```python
from src.config import Settings

settings = Settings(
    chunk_size=800,
    chunk_overlap=150,
    max_chunk_size=1500,
    min_chunk_size=50,
    chunk_batch_size=10
)

pipeline = DefaultChunkingPipeline(settings)
```

## Chunking Strategies

### Code Files

**Supported Languages**: Python, JavaScript, TypeScript, Java, C++, Go, PHP, Ruby, and more.

**Features**:
- Function and class boundary detection
- Context preservation (class names, function names, module names)
- Language-specific parsing patterns
- Hierarchical relationships

**Example Output**:
```python
# Input: Python class
class DataProcessor:
    def __init__(self, config):
        self.config = config
    
    def process_data(self, data):
        return processed_data

# Output: 2 chunks
# Chunk 1: Class definition with __init__ method
# Chunk 2: process_data method
```

### Markdown Files

**Features**:
- Header-based hierarchical chunking (H1-H6)
- Code block extraction as separate chunks
- Section context preservation
- Parent header tracking

**Example Output**:
```markdown
# Main Title
Introduction content

## Section 1
Section content

```python
def example():
    pass
```

# Output: 3 chunks
# Chunk 1: Main Title + Introduction (MARKDOWN_SECTION)
# Chunk 2: Section 1 content (MARKDOWN_SECTION)  
# Chunk 3: Python code block (MARKDOWN_CODE_BLOCK)
```

### Text Files

**Supported Files**: .txt, .log, README, LICENSE, CHANGELOG, etc.

**Features**:
- Semantic paragraph-based chunking
- Sentence boundary detection
- Configurable overlap
- Content normalization

### Configuration Files

**Supported Formats**: JSON, YAML, TOML, INI, .env, .properties

**Features**:
- Structure-aware chunking
- Section-based organization
- Key-value grouping
- Namespace preservation

## Working with Chunks

### Chunk Data Structure

```python
@dataclass
class Chunk:
    content: str                    # The actual chunk content
    chunk_id: str                   # Unique identifier
    chunk_type: ChunkType          # Type classification
    file_metadata: FileMetadata    # Source file information
    start_line: int                # Starting line number
    end_line: int                  # Ending line number
    context: ChunkContext          # Hierarchical context
    token_count: Optional[int]     # Token count
    chunking_strategy: str         # Strategy used
```

### Accessing Chunk Information

```python
for chunk in chunks:
    print(f"Chunk ID: {chunk.chunk_id}")
    print(f"Type: {chunk.chunk_type.value}")
    print(f"File: {chunk.file_metadata.file_path}")
    print(f"Lines: {chunk.line_range}")
    print(f"Tokens: {chunk.token_count}")
    print(f"Context: {chunk.full_context_path}")
    print(f"Content: {chunk.content[:100]}...")
    print("---")
```

### Filtering Chunks

```python
# Filter by chunk type
code_chunks = [c for c in chunks if c.chunk_type == ChunkType.CODE_FUNCTION]
markdown_chunks = [c for c in chunks if c.chunk_type == ChunkType.MARKDOWN_SECTION]

# Filter by file type
python_chunks = [c for c in chunks if c.file_metadata.language == "python"]

# Filter by size
large_chunks = [c for c in chunks if c.token_count and c.token_count > 500]
```

## Advanced Usage

### Custom Chunking Strategy

```python
from src.ingestion.strategies.base import BaseChunkingStrategy
from src.ingestion.base import ChunkType, ChunkContext

class CustomChunkingStrategy(BaseChunkingStrategy):
    def get_strategy_name(self) -> str:
        return "custom"
    
    def is_applicable(self, file_metadata: FileMetadata) -> bool:
        return file_metadata.file_path.endswith('.custom')
    
    async def chunk_content(self, content: str, file_metadata: FileMetadata, **kwargs):
        # Custom chunking logic
        chunks = []
        # ... implementation
        return chunks

# Register custom strategy
factory = ChunkingStrategyFactory(settings)
factory.register_strategy("custom", CustomChunkingStrategy)
```

### Batch Processing with Progress Tracking

```python
import asyncio
from tqdm import tqdm

async def process_with_progress(file_metadata_list, repository_path):
    pipeline = DefaultChunkingPipeline(settings)
    
    # Process in smaller batches for progress tracking
    batch_size = 10
    all_chunks = []
    
    with tqdm(total=len(file_metadata_list), desc="Processing files") as pbar:
        for i in range(0, len(file_metadata_list), batch_size):
            batch = file_metadata_list[i:i + batch_size]
            
            # Process batch
            batch_chunks = await pipeline.process_files(batch, repository_path)
            all_chunks.extend(batch_chunks)
            
            pbar.update(len(batch))
    
    return all_chunks
```

### Quality Analysis

```python
# Get processing statistics
stats = pipeline.get_processing_stats()
print(f"Files processed: {stats['files_processed']}")
print(f"Chunks created: {stats['chunks_created']}")
print(f"Processing time: {stats['processing_time']:.2f}s")
print(f"Errors: {stats['errors_encountered']}")

# Validate chunk quality
validation_results = await pipeline.validate_chunks(chunks)
print(f"Valid chunks: {validation_results['valid_chunks']}")
print(f"Empty chunks: {validation_results['empty_chunks']}")
print(f"Oversized chunks: {validation_results['oversized_chunks']}")
```

### Performance Optimization

```python
# Estimate processing requirements
estimation = await pipeline.estimate_processing_time(
    file_metadata_list, 
    repository_path
)

print(f"Estimated chunks: {estimation['estimated_chunks']}")
print(f"Estimated time: {estimation['estimated_time_seconds']:.2f}s")

# Adjust batch size based on system resources
import psutil
cpu_count = psutil.cpu_count()
settings.chunk_batch_size = min(cpu_count * 2, 50)
```

## Error Handling

### Common Issues and Solutions

**File Encoding Issues**:
```python
# The file loader automatically handles encoding detection
# and falls back to multiple encodings if needed
try:
    chunks = await pipeline.process_single_file(file_metadata, repo_path)
except FileProcessingError as e:
    print(f"Failed to process {e.file_path}: {e.message}")
    # File will be skipped, processing continues
```

**Large Files**:
```python
# Configure maximum file size
settings.max_file_size = 50 * 1024 * 1024  # 50MB

# Files exceeding this size will be skipped with a warning
```

**Memory Management**:
```python
# Process files in smaller batches to manage memory
settings.chunk_batch_size = 5  # Reduce for memory-constrained environments

# Clear caches periodically
pipeline.file_loader.clear_encoding_cache()
```

## Integration Examples

### With Vector Database

```python
# Process chunks and prepare for embedding
chunks = await chunking_pipeline.process_files(file_metadata_list, repo_path)

# Convert to embedding format
documents = []
for chunk in chunks:
    doc = {
        "id": chunk.chunk_id,
        "content": chunk.content,
        "metadata": {
            "file_path": chunk.file_metadata.file_path,
            "chunk_type": chunk.chunk_type.value,
            "context": chunk.full_context_path,
            "line_range": chunk.line_range,
            "token_count": chunk.token_count
        }
    }
    documents.append(doc)

# Store in vector database
# vector_store.add_documents(documents)
```

### With Search Index

```python
# Create search index entries
search_entries = []
for chunk in chunks:
    entry = {
        "id": chunk.chunk_id,
        "title": chunk.full_context_path,
        "content": chunk.content,
        "file_path": chunk.file_metadata.file_path,
        "chunk_type": chunk.chunk_type.value,
        "language": chunk.file_metadata.language,
        "priority": chunk.file_metadata.priority
    }
    search_entries.append(entry)
```

## Best Practices

1. **Configure chunk sizes** based on your embedding model's context window
2. **Monitor processing statistics** to identify performance bottlenecks
3. **Use appropriate batch sizes** based on available system resources
4. **Validate chunk quality** before storing in vector databases
5. **Preserve context information** for better retrieval accuracy
6. **Handle errors gracefully** to ensure robust processing
7. **Test with representative repositories** to tune configuration

## Troubleshooting

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)

# Enable detailed logging for chunking operations
logger = logging.getLogger('src.ingestion')
logger.setLevel(logging.DEBUG)
```

### Performance Profiling

```python
import time
import cProfile

def profile_chunking():
    start_time = time.time()
    
    # Your chunking code here
    chunks = await pipeline.process_files(file_metadata_list, repo_path)
    
    end_time = time.time()
    print(f"Processing took {end_time - start_time:.2f} seconds")
    print(f"Average time per file: {(end_time - start_time) / len(file_metadata_list):.3f}s")

# Run with profiler
cProfile.run('profile_chunking()')
```
