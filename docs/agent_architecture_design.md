# Multi-Agent Orchestration Architecture Design

## Overview

This document defines the architecture for the multi-agent orchestration layer that will coordinate
between specialized AI agents to provide comprehensive codebase analysis and task planning.

## Agent System Architecture

```text
┌─────────────────────────────────────────────────────────────────┐
│                    Query Input (User/API)                      │
└─────────────────────┬───────────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────────┐
│                 Orchestrator Agent                             │
│  ┌─────────────────────────────────────────────────────────┐   │
│  │ • Query Classification & Intent Detection               │   │
│  │ • Agent Routing Logic                                   │   │
│  │ • Conversational Context Management                     │   │
│  │ • Response Synthesis & Formatting                       │   │
│  └─────────────────────────────────────────────────────────┘   │
└─────────────┬───────────────┬───────────────┬───────────────────┘
              │               │               │
    ┌─────────▼─────────┐ ┌───▼────────┐ ┌───▼──────────────────┐
    │ Technical Design  │ │ Task       │ │ Direct RAG Retrieval │
    │ Architect Agent   │ │ Planner    │ │ (Factual/Code Lookup)│
    │                   │ │ Agent      │ │                      │
    └─────────┬─────────┘ └───┬────────┘ └───┬──────────────────┘
              │               │               │
              └───────────────┼───────────────┘
                              │
    ┌─────────────────────────▼─────────────────────────────────┐
    │              Shared Knowledge Base                       │
    │  ┌─────────────────────────────────────────────────────┐ │
    │  │ • Vector Store (ChromaDB)                           │ │
    │  │ • Embedding Pipeline                                │ │
    │  │ • GitHub Repository Content                         │ │
    │  │ • Metadata & Context                                │ │
    │  └─────────────────────────────────────────────────────┘ │
    └───────────────────────────────────────────────────────────┘
```

## Agent Roles & Capabilities

### 1. Orchestrator Agent (Primary Controller)

**Responsibilities:**

- **Query Classification**: Determine query intent and route to appropriate agent
- **Workflow Management**: Coordinate multi-agent interactions and dependencies
- **Context Management**: Maintain conversational state across multi-turn interactions
- **Response Synthesis**: Combine outputs from multiple agents into coherent responses
- **Output Formatting**: Ensure Markdown formatting and source citations (FR-13, FR-14)

**Routing Logic:**

```python
def classify_query_intent(query: str) -> AgentType:
    """
    Architecture/Design queries → Technical Design Architect
    Task/Implementation queries → Task Planner
    Factual/Code lookup → Direct RAG Retrieval
    Complex queries → Multi-agent collaboration
    """
```

**Key Interfaces:**

- `route_query(query: str, context: ConversationContext) -> AgentResponse`
- `synthesize_responses(responses: List[AgentResponse]) -> FormattedResponse`
- `manage_conversation_context(context: ConversationContext) -> None`

### 2. Technical Design Architect Agent

**Responsibilities:**

- **Architecture Analysis**: Interpret architectural needs and constraints
- **Design Generation**: Create implementation strategies aligned with project standards
- **Standards Compliance**: Ensure designs follow `docs/` authoritative sources (NFR-4)
- **Pattern Recognition**: Identify and recommend established architectural patterns

**Capabilities:**

- Access to `docs/` folder content with priority weighting
- Integration with existing codebase patterns
- SOLID principles application
- Technology stack alignment per `docs/tech.md`

**Key Interfaces:**

- `analyze_architecture_request(query: str, context: CodebaseContext) -> ArchitectureResponse`
- `generate_design_recommendations(requirements: List[str]) -> DesignDocument`
- `validate_design_compliance(design: DesignDocument) -> ComplianceReport`

### 3. Task Planner Agent

**Responsibilities:**

- **Requirement Breakdown**: Decompose complex requests into actionable tasks
- **Timeline Generation**: Create realistic implementation schedules
- **Dependency Analysis**: Identify task dependencies and critical paths
- **Resource Planning**: Estimate effort and assign task priorities

**Capabilities:**

- Integration with existing task management patterns
- 5-phase methodology application
- SOLID principles consideration in task breakdown
- Risk assessment and mitigation planning

**Key Interfaces:**

- `break_down_requirements(requirements: str, context: ProjectContext) -> TaskPlan`
- `generate_timeline(tasks: List[Task]) -> Timeline`
- `analyze_dependencies(tasks: List[Task]) -> DependencyGraph`

## Shared Memory & Context Management

### Conversational Context Storage

```python
@dataclass
class ConversationContext:
    session_id: str
    user_id: Optional[str]
    conversation_history: List[Message]
    current_repository: Optional[str]
    active_agents: Set[AgentType]
    shared_state: Dict[str, Any]
    created_at: datetime
    last_updated: datetime
```

**Storage Strategy:**

- **Short-term**: In-memory storage for active sessions
- **Medium-term**: Redis cache for session persistence
- **Long-term**: Optional database storage for conversation history

### Context Sharing Between Agents

```python
@dataclass
class AgentContext:
    conversation_context: ConversationContext
    repository_context: RepositoryContext
    search_results: List[SearchResult]
    previous_agent_outputs: Dict[AgentType, AgentResponse]
    execution_metadata: Dict[str, Any]
```

## LLM Integration Pattern

### Unified LLM Client

```python
class LLMClient:
    """Unified client for LLM interactions across all agents."""

    def __init__(self, settings: Settings):
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.model = settings.openai_model
        self.temperature = settings.openai_temperature
        self.max_tokens = settings.openai_max_tokens

    async def generate_response(
        self,
        prompt: str,
        system_prompt: str,
        context: Optional[Dict[str, Any]] = None
    ) -> LLMResponse:
        """Generate response with consistent error handling and logging."""
```

### Prompt Engineering Standards

**Template Structure:**

```markdown
# System Prompt

You are a [AGENT_ROLE] for a codebase analysis system.

## Context

Repository: {repository_name} Files analyzed: {file_count} Query type: {query_classification}

## Instructions

[AGENT_SPECIFIC_INSTRUCTIONS]

## Output Format

- Use Markdown formatting
- Include source file citations: `[filename:line_range]`
- Structure responses with clear headings
- Provide actionable recommendations

## Retrieved Context

{search_results}

# User Query

{user_query}
```

## Error Handling & Fallback Mechanisms

### Agent-Level Error Handling

- **Timeout Handling**: 30-second timeout per agent with graceful degradation
- **LLM API Failures**: Retry logic with exponential backoff
- **Context Overflow**: Automatic context truncation with priority preservation
- **Agent Unavailability**: Fallback to direct RAG retrieval

### System-Level Fallbacks

- **Multi-agent Failure**: Fall back to single-agent responses
- **Complete Agent Failure**: Direct vector search with basic formatting
- **Context Loss**: Reconstruct minimal context from query history

## Integration with Existing System

### Vector Store Integration

```python
# Leverage existing embedding pipeline for agent context
search_results = await embedding_pipeline.search_similar_chunks(
    query=processed_query,
    top_k=20,  # Higher for agent context
    filters={"priority": "high"}  # Prioritize docs/ content
)
```

### Configuration Extensions

```python
# Additional settings for agent system
agent_timeout: int = 30
agent_max_context_tokens: int = 8000
agent_fallback_enabled: bool = True
conversation_ttl: int = 3600  # 1 hour
```

## Success Metrics & Validation

### Functional Requirements Compliance

- **FR-5**: All queries routed through Orchestrator ✓
- **FR-6**: Correct agent routing based on query type ✓
- **FR-7**: Conversational context preservation ✓
- **FR-8**: Design alignment with project standards ✓
- **FR-9**: Actionable task breakdowns ✓
- **FR-10**: Coherent multi-agent response synthesis ✓

### Quality Metrics

- **Response Time**: <3 seconds for simple queries (NFR-1)
- **Design Alignment**: >95% compliance with docs/ standards (NFR-4)
- **Context Preservation**: Multi-turn conversation accuracy
- **Agent Routing Accuracy**: >90% correct intent classification

## Implementation Phases

### Phase 3.2: Task Planning ✅ **COMPLETE**

- ✅ Define detailed implementation tasks
- ✅ Create agent base classes and interfaces
- ✅ Design prompt templates and validation

### Phase 3.3: Implementation 🚧 **IN PROGRESS**

- ✅ Build Orchestrator Agent with routing logic
- ✅ Implement Technical Design Architect Agent
- ✅ **Implement Task Planner Agent** - **COMPLETE**
  - ✅ Requirement breakdown engine with priority/complexity analysis
  - ✅ Timeline generator with dependency management and buffer calculations
  - ✅ Risk analyzer for intelligent project risk identification
  - ✅ Multi-type query classification and processing
  - ✅ RAG integration with search functionality
  - ✅ 5-phase methodology compliance and SOLID principles adherence
  - ✅ Comprehensive test suite (87 tests with 100% pass rate)
- ✅ Create shared memory system
- ✅ Develop LLM client integration
- 🚧 Agent factory and dependency injection system
- 🚧 API endpoints for agent interactions

### Phase 3.4: Verification 🚧 **IN PROGRESS**

- ✅ Unit tests for Task Planner Agent (87 tests passing)
- 🚧 Unit tests for remaining agents
- 🚧 Integration tests for multi-agent workflows
- 🚧 Performance testing for response times
- 🚧 Compliance validation for design outputs

### Phase 3.5: Documentation & Handover

- Agent usage guides
- Prompt template documentation
- Troubleshooting and maintenance guides
