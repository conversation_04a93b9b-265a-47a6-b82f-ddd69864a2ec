# Embedding & Vector Store System

## Overview

The embedding and vector store system provides semantic search capabilities for the LLM RAG Codebase Query System. It transforms text chunks into high-dimensional vector representations and stores them in a vector database for efficient similarity search.

## Architecture

### Core Components

1. **EmbeddingClient** - Generates embeddings from text content
2. **VectorStore** - Stores and retrieves embeddings with metadata
3. **EmbeddingPipeline** - Orchestrates the embedding generation and storage process

### Design Principles

- **Provider Abstraction**: Support multiple embedding providers (OpenAI, local models)
- **Vector Store Flexibility**: Support multiple vector databases (ChromaDB, Weaviate, Pinecone)
- **Quality Assurance**: Built-in validation and quality checks for embeddings
- **Performance Optimization**: Batching, caching, and rate limiting
- **Error Resilience**: Comprehensive error handling and retry mechanisms

## Embedding Clients

### OpenAI Embedding Client

Provides integration with OpenAI's embedding models:

```python
from src.ingestion import OpenAIEmbeddingClient
from src.config import get_settings

settings = get_settings()
client = OpenAIEmbeddingClient(settings)

# Generate embeddings
texts = ["Hello world", "How are you?"]
embeddings = await client.generate_embeddings(texts)
```

**Features:**
- Support for all OpenAI embedding models
- Automatic rate limiting and retry logic
- Cost estimation and usage tracking
- Batch processing for efficiency

**Supported Models:**
- `text-embedding-3-large` (3072 dimensions)
- `text-embedding-3-small` (1536 dimensions)
- `text-embedding-ada-002` (1536 dimensions)

### Local Embedding Client

Provides integration with local sentence-transformers models:

```python
from src.ingestion import LocalEmbeddingClient

client = LocalEmbeddingClient(settings)
embeddings = await client.generate_embeddings(texts)
```

**Features:**
- No API costs or rate limits
- Privacy-preserving (no data sent externally)
- Support for popular sentence-transformers models
- Automatic model downloading and caching

**Supported Models:**
- `all-MiniLM-L6-v2` (384 dimensions)
- `all-mpnet-base-v2` (768 dimensions)
- Custom models via Hugging Face

## Vector Stores

### ChromaDB Vector Store

Provides persistent vector storage with ChromaDB:

```python
from src.ingestion import ChromaVectorStore

store = ChromaVectorStore(settings)

# Add embeddings
await store.add_embeddings(embeddings, metadata, ids)

# Search similar vectors
results = await store.search_similar(query_embedding, top_k=10)
```

**Features:**
- Local persistence with SQLite backend
- Metadata filtering and search
- Collection management
- Backup and restore capabilities

### Future Vector Stores

Planned support for:
- **Weaviate**: Cloud-native vector database with GraphQL API
- **Pinecone**: Managed vector database service

## Embedding Pipeline

The `DefaultEmbeddingPipeline` orchestrates the complete embedding workflow:

```python
from src.ingestion import DefaultEmbeddingPipeline

pipeline = DefaultEmbeddingPipeline(settings)

# Process chunks to generate embeddings
embedded_chunks = await pipeline.process_chunks(chunks)

# Store embeddings in vector store
success = await pipeline.store_embeddings(embedded_chunks)

# Search for similar content
results = await pipeline.search_similar_chunks("search query", top_k=5)
```

### Pipeline Features

1. **Batch Processing**: Processes chunks in configurable batches for efficiency
2. **Content Caching**: Avoids regenerating embeddings for duplicate content
3. **Quality Validation**: Validates embedding dimensions and values
4. **Normalization**: Optional L2 normalization for improved similarity search
5. **Statistics Tracking**: Comprehensive metrics for monitoring and optimization

### Configuration Options

```python
# Embedding settings
EMBEDDING_PROVIDER = "openai"  # or "local"
EMBEDDING_MODEL = "text-embedding-3-large"
EMBEDDING_BATCH_SIZE = 100
EMBEDDING_MAX_RETRIES = 3
EMBEDDING_TIMEOUT = 60
EMBEDDING_NORMALIZE = True
EMBEDDING_QUALITY_CHECK = True
EMBEDDING_CACHE_ENABLED = True

# Vector store settings
VECTOR_STORE_PROVIDER = "chromadb"
CHROMA_COLLECTION_NAME = "codebase_embeddings"
CHROMA_PERSIST_DIRECTORY = "./data/chroma"
```

## Integration with Chunking Pipeline

The embedding system seamlessly integrates with the chunking pipeline:

```python
from src.ingestion import DefaultChunkingPipeline, DefaultEmbeddingPipeline

# Process repository files into chunks
chunking_pipeline = DefaultChunkingPipeline(settings)
chunks = await chunking_pipeline.process_files(file_metadata_list, repository_path)

# Generate embeddings for chunks
embedding_pipeline = DefaultEmbeddingPipeline(settings)
embedded_chunks = await embedding_pipeline.process_chunks(chunks)

# Store in vector database
await embedding_pipeline.store_embeddings(embedded_chunks)
```

## Error Handling

The system includes comprehensive error handling:

### Exception Hierarchy

- `EmbeddingError` - Base class for embedding-related errors
- `EmbeddingGenerationError` - Embedding generation failures
- `EmbeddingQualityError` - Quality validation failures
- `VectorStoreError` - Vector store operation errors
- `VectorStoreConnectionError` - Connection failures
- `VectorStoreOperationError` - Storage/retrieval failures
- `EmbeddingPipelineError` - Pipeline orchestration errors

### Retry Logic

- Automatic retries for transient failures
- Exponential backoff for rate limiting
- Configurable retry limits and timeouts

## Performance Optimization

### Batching Strategy

The system processes embeddings in batches to optimize API usage:

```python
# Configure batch sizes
EMBEDDING_BATCH_SIZE = 100  # Chunks per batch
EMBEDDING_PIPELINE_BATCH_SIZE = 50  # Pipeline batch size
```

### Caching

Content-based caching prevents duplicate embedding generation:

```python
# Enable caching
EMBEDDING_CACHE_ENABLED = True

# Cache is based on content hash (MD5)
# Identical content reuses cached embeddings
```

### Rate Limiting

Built-in rate limiting for API providers:

```python
# OpenAI rate limiting
# Automatically tracks tokens per minute
# Implements exponential backoff
```

## Monitoring and Statistics

The system provides comprehensive statistics:

```python
# Get pipeline statistics
stats = await pipeline.get_pipeline_stats()

print(f"Chunks processed: {stats['chunks_processed']}")
print(f"Embeddings generated: {stats['embeddings_generated']}")
print(f"Cache hits: {stats['cache_hits']}")
print(f"Processing time: {stats['processing_time']:.2f}s")
```

### Key Metrics

- **Processing Statistics**: Chunks processed, embeddings generated, processing time
- **Cache Performance**: Cache hits, cache misses, cache efficiency
- **Quality Metrics**: Quality failures, validation errors
- **API Usage**: Requests made, tokens consumed, costs incurred
- **Vector Store**: Total embeddings stored, collection size, search performance

## Best Practices

### Embedding Model Selection

1. **OpenAI text-embedding-3-large**: Best quality, higher cost
2. **OpenAI text-embedding-3-small**: Good quality, lower cost
3. **Local models**: No cost, privacy-preserving, lower quality

### Vector Store Configuration

1. **Collection Naming**: Use descriptive names for different projects
2. **Persistence**: Ensure persistent storage for production use
3. **Backup Strategy**: Regular backups of vector data
4. **Metadata Design**: Include relevant metadata for filtering

### Performance Tuning

1. **Batch Size**: Optimize based on API limits and memory constraints
2. **Caching**: Enable for repositories with duplicate content
3. **Quality Checks**: Balance quality vs. processing speed
4. **Normalization**: Enable for improved similarity search

## Troubleshooting

### Common Issues

1. **API Rate Limits**: Reduce batch size, increase timeout
2. **Memory Issues**: Reduce batch size, process in smaller chunks
3. **Quality Failures**: Check embedding dimensions, validate input text
4. **Storage Errors**: Verify vector store configuration and permissions

### Debugging

Enable debug logging for detailed information:

```python
import logging
logging.getLogger('src.ingestion.embedding').setLevel(logging.DEBUG)
```

## Future Enhancements

1. **Additional Providers**: Support for more embedding providers
2. **Hybrid Search**: Combine vector and keyword search
3. **Incremental Updates**: Efficient updates for changed content
4. **Distributed Processing**: Support for distributed embedding generation
5. **Advanced Caching**: Persistent cache with TTL and eviction policies
