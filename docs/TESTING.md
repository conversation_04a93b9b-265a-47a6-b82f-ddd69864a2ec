# Testing Documentation (`TESTING.md`)

## 1. Overview & Philosophy

Testing is a **critical pillar** of our software development lifecycle. Our goal is to deliver **reliable, maintainable,
and high-quality software** that meets business and technical requirements. We emphasize **early defect detection**,
**automated regression prevention**, and **confidence in production releases** through comprehensive test coverage.

**Testing is everyone’s responsibility** — developers, reviewers, and CI pipelines must collaborate to ensure quality.

---

## 2. Testing Standards & Requirements

- **Test Types**: We categorize tests into:

  - **Unit Tests**: Validate individual functions or modules in isolation.
  - **Integration Tests**: Validate interaction between components (e.g., ingestion pipeline + vector DB).
  - **End-to-End (E2E) Tests**: Simulate real user scenarios spanning multiple layers.
  - **Performance Tests**: Ensure latency and throughput targets.
  - **Regression Tests**: Detect unintended side effects after changes.

- **Coverage**:

  - Minimum **80% coverage** for all new code.
  - Coverage reports generated automatically during CI runs.
  - Critical modules (ingestion, retrieval, agents) require **>90% coverage**.

- **Test Quality**:

  - Tests must be **deterministic**, **repeatable**, and **fast**.
  - Avoid reliance on external state or network unless explicitly integration tests.
  - Use mocks/stubs for dependencies where applicable.
  - All tests must have **clear assertions** and cover both positive and negative cases.

- **Test Data**:

  - Use **representative fixtures** or synthetic data.
  - Store test assets under `tests/assets/`.
  - Avoid large files that slow down test runs.

---

## 3. Development Commands

### 3.1 Backend (Python)

- Run all tests with coverage report:

```bash
uv run -m pytest --cov=src tests/
```

- Run a single test file or function:

```bash
uv run -m pytest tests/unit/test_ingestion.py
uv run -m pytest tests/unit/test_ingestion.py::test_chunking_function
```

- Generate HTML coverage report:

```bash
uv run -m pytest --cov=src --cov-report=html
```

- Lint and format checks (must pass before tests):

```bash
ruff src tests
black --check src tests
```

### 3.2 Frontend (Node.js/TypeScript)

- Run all tests with Vitest:

```bash
pnpm vitest run
```

- Run in watch mode during development:

```bash
pnpm vitest
```

- Run ESLint for linting:

```bash
pnpm eslint src
```

---

## 4. Backend Testing Strategy

### 4.1 Unit Testing

- Test core modules independently: ingestion, retrieval, ranking, and agent logic.
- Use **pytest** fixtures for mocking dependencies (e.g., vector DB).
- Focus on input-output correctness and edge cases.

### 4.2 Integration Testing

- Test pipelines end-to-end within a controlled environment.
- Spin up lightweight in-memory or dockerized vector DB instances (e.g., Chroma).
- Validate data flow through ingestion, embedding, and retrieval.
- Use test repositories or mock data.

### 4.3 End-to-End Testing

- Simulate full system workflows triggered by CLI or API.
- Validate query routing, agent orchestration, and response formatting.
- Run against staging-like environment with real components.

### 4.4 Test Isolation

- Clean up external resources after tests.
- Use database transactions or temporary namespaces.

---

## 5. Frontend Testing Strategy

### 5.1 Unit & Component Testing

- Use **Vitest** with **Vue Testing Library** or **React Testing Library** depending on framework.
- Test UI components, hooks, and utilities in isolation.
- Mock API calls to isolate UI logic.

### 5.2 Integration Testing

- Validate interaction between UI components and API clients.
- Use mock server or test API endpoints.

### 5.3 End-to-End Testing

- Use **Playwright** or **Cypress** for user journey tests.
- Validate major user flows such as submitting queries, viewing results, and error states.

---

## 6. Testing Workflows

### 6.1 Local Development

- Run tests frequently during feature development.
- Use watch modes and interactive debugging.
- Address failing tests before committing.

### 6.2 Pull Requests

- All tests must pass in CI before merge.
- Review test coverage changes.
- Verify new features have appropriate tests.

### 6.3 Continuous Integration

- Automated test suites run on every push and PR.
- Generate reports for coverage, test results, and linting.
- Notify teams of failures immediately.

### 6.4 Release Verification

- Run full regression and performance tests before production deployment.
- Use smoke tests to confirm deployment health.

---

## 7. Future Guidelines & Best Practices

- Introduce **contract testing** between agents and RAG pipelines to validate integration points.
- Explore **property-based testing** for edge case generation.
- Automate **performance benchmarking** and track regressions over time.
- Adopt **test case documentation** to improve maintainability and onboarding.
- Incrementally increase test coverage in legacy areas.
- Implement **security testing** (static analysis, dependency checks).

---

## 8. References

- See [`docs/rules.md`](./rules.md) for code quality enforcement and testing mandates.
- See [`docs/workflows.md`](./workflows.md) for how testing integrates with development phases.

---
