"""
Unit tests for vector store implementations.

Tests the vector store implementations including ChromaDB with mocking
for external dependencies.
"""

import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
import tempfile
from pathlib import Path
from datetime import datetime

from src.ingestion.vector.chroma import ChromaVectorStore
from src.ingestion.vector.factory import VectorStoreFactory
from src.ingestion.base import EmbeddedChunk, Chunk, ChunkType, ChunkContext, FileMetadata, EmbeddingMetadata
from src.ingestion.exceptions import IngestionError
from src.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.vector_store_provider = "chromadb"
    settings.chroma_collection_name = "test_collection"
    settings.chroma_persist_directory = str(Path(tempfile.mkdtemp()) / "test_chroma")
    settings.chroma_host = "localhost"
    settings.chroma_port = 8001
    return settings


@pytest.fixture
def sample_chunk():
    """Create a sample chunk for testing."""
    file_metadata = FileMetadata(
        file_path="test.py",
        file_size=1000,
        file_type="text",
        last_modified=datetime.now(),
        language="python"
    )

    context = ChunkContext(
        function_name="test_function",
        class_name="TestClass",
        module_name="test_module"
    )

    chunk = Chunk(
        content="def test_function():\n    return 'test'",
        chunk_id="test_chunk_1",
        chunk_type=ChunkType.CODE_FUNCTION,
        file_metadata=file_metadata,
        start_line=1,
        end_line=2,
        context=context,
        token_count=10,
        chunking_strategy="code"
    )

    return chunk


@pytest.fixture
def sample_embedded_chunk(sample_chunk):
    """Create a sample embedded chunk for testing."""
    embedding_metadata = EmbeddingMetadata(
        embedding_model="text-embedding-3-large",
        embedding_dimension=3072,
        embedding_provider="openai"
    )

    embedded_chunk = EmbeddedChunk(
        chunk=sample_chunk,
        embedding=[0.1, 0.2, 0.3] * 1024,  # 3072 dimensions
        embedding_metadata=embedding_metadata
    )

    return embedded_chunk


class TestChromaVectorStore:
    """Test cases for ChromaVectorStore."""

    def test_initialization(self, mock_settings):
        """Test vector store initialization."""
        store = ChromaVectorStore(mock_settings)

        assert store.collection_name == "test_collection"
        assert store.persist_directory.name == "test_chroma"
        assert store._client is None
        assert store._collection is None

    @patch('src.ingestion.vector.chroma.chromadb.PersistentClient')
    def test_get_client(self, mock_client_class, mock_settings):
        """Test ChromaDB client creation."""
        mock_client = Mock()
        mock_client_class.return_value = mock_client

        store = ChromaVectorStore(mock_settings)
        client = store._get_client()

        assert client == mock_client
        assert store._client == mock_client

        # Verify client was created with correct settings
        mock_client_class.assert_called_once()

    @patch('src.ingestion.vector.chroma.chromadb.PersistentClient')
    def test_get_collection_existing(self, mock_client_class, mock_settings):
        """Test getting existing collection."""
        mock_client = Mock()
        mock_collection = Mock()
        mock_client.get_collection.return_value = mock_collection
        mock_client_class.return_value = mock_client

        store = ChromaVectorStore(mock_settings)
        collection = store._get_collection()

        assert collection == mock_collection
        assert store._collection == mock_collection
        mock_client.get_collection.assert_called_once_with(name="test_collection")

    @patch('src.ingestion.vector.chroma.chromadb.PersistentClient')
    def test_get_collection_create_new(self, mock_client_class, mock_settings):
        """Test creating new collection when it doesn't exist."""
        mock_client = Mock()
        mock_collection = Mock()

        # First call raises exception (collection doesn't exist)
        mock_client.get_collection.side_effect = Exception("Collection not found")
        mock_client.create_collection.return_value = mock_collection
        mock_client_class.return_value = mock_client

        store = ChromaVectorStore(mock_settings)
        collection = store._get_collection()

        assert collection == mock_collection
        assert store._collection == mock_collection
        mock_client.create_collection.assert_called_once_with(
            name="test_collection",
            metadata={"description": "Codebase embeddings for RAG system"}
        )

    @pytest.mark.asyncio
    async def test_add_embeddings_success(self, mock_settings, sample_embedded_chunk):
        """Test successful embedding addition."""
        store = ChromaVectorStore(mock_settings)

        # Mock collection
        mock_collection = Mock()
        store._collection = mock_collection

        embeddings = [sample_embedded_chunk.embedding]
        metadata = [sample_embedded_chunk.to_dict()["metadata"]]
        ids = [sample_embedded_chunk.embedding_id]

        result = await store.add_embeddings(embeddings, metadata, ids)

        assert result is True
        mock_collection.add.assert_called_once()

        # Verify call arguments
        call_args = mock_collection.add.call_args
        assert len(call_args.kwargs["embeddings"]) == 1
        assert len(call_args.kwargs["documents"]) == 1
        assert len(call_args.kwargs["metadatas"]) == 1
        assert len(call_args.kwargs["ids"]) == 1

    @pytest.mark.asyncio
    async def test_add_embeddings_empty_input(self, mock_settings):
        """Test adding embeddings with empty input."""
        store = ChromaVectorStore(mock_settings)

        result = await store.add_embeddings([], [], [])
        assert result is True

    @pytest.mark.asyncio
    async def test_add_embeddings_mismatched_lengths(self, mock_settings):
        """Test adding embeddings with mismatched input lengths."""
        store = ChromaVectorStore(mock_settings)

        with pytest.raises(IngestionError) as exc_info:
            await store.add_embeddings([[0.1, 0.2]], [{"test": "meta"}], [])

        assert "must have the same length" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_search_similar_success(self, mock_settings):
        """Test successful similarity search."""
        store = ChromaVectorStore(mock_settings)

        # Mock collection and search results
        mock_collection = Mock()
        mock_results = {
            "ids": [["emb_test_chunk_1", "emb_test_chunk_2"]],
            "distances": [[0.1, 0.3]],
            "documents": [["test content 1", "test content 2"]],
            "metadatas": [[
                {"chunk_id": "test_chunk_1", "file_path": "test1.py", "chunk_type": "code_function"},
                {"chunk_id": "test_chunk_2", "file_path": "test2.py", "chunk_type": "code_class"}
            ]],
            "embeddings": [[[0.1, 0.2, 0.3], [0.4, 0.5, 0.6]]]
        }
        mock_collection.query.return_value = mock_results
        store._collection = mock_collection

        query_embedding = [0.1, 0.2, 0.3]
        results = await store.search_similar(query_embedding, top_k=2)

        assert len(results) == 2
        assert results[0].similarity_score == 0.9  # 1.0 - 0.1
        assert results[1].similarity_score == 0.7  # 1.0 - 0.3
        assert results[0].rank == 1
        assert results[1].rank == 2

        # Verify query was called correctly
        mock_collection.query.assert_called_once_with(
            query_embeddings=[query_embedding],
            n_results=2,
            where=None,
            include=["embeddings", "documents", "metadatas", "distances"]
        )

    @pytest.mark.asyncio
    async def test_search_similar_with_filters(self, mock_settings):
        """Test similarity search with filters."""
        store = ChromaVectorStore(mock_settings)

        mock_collection = Mock()
        mock_collection.query.return_value = {
            "ids": [[]],
            "distances": [[]],
            "documents": [[]],
            "metadatas": [[]],
            "embeddings": [[]]
        }
        store._collection = mock_collection

        query_embedding = [0.1, 0.2, 0.3]
        filters = {"file_path": "test.py", "chunk_type": "code_function"}

        await store.search_similar(query_embedding, top_k=5, filters=filters)

        # Verify filters were applied
        call_args = mock_collection.query.call_args
        assert call_args.kwargs["where"] == filters

    @pytest.mark.asyncio
    async def test_delete_embeddings_success(self, mock_settings):
        """Test successful embedding deletion."""
        store = ChromaVectorStore(mock_settings)

        mock_collection = Mock()
        store._collection = mock_collection

        ids = ["emb_test_1", "emb_test_2"]
        result = await store.delete_embeddings(ids)

        assert result is True
        mock_collection.delete.assert_called_once_with(ids=ids)

    @pytest.mark.asyncio
    async def test_delete_embeddings_empty_input(self, mock_settings):
        """Test deleting embeddings with empty input."""
        store = ChromaVectorStore(mock_settings)

        result = await store.delete_embeddings([])
        assert result is True

    @pytest.mark.asyncio
    async def test_get_embedding_success(self, mock_settings):
        """Test getting specific embedding by ID."""
        store = ChromaVectorStore(mock_settings)

        mock_collection = Mock()
        mock_results = {
            "ids": ["emb_test_chunk_1"],
            "documents": ["test content"],
            "metadatas": [{"chunk_id": "test_chunk_1", "file_path": "test.py"}],
            "embeddings": [[0.1, 0.2, 0.3]]
        }
        mock_collection.get.return_value = mock_results
        store._collection = mock_collection

        result = await store.get_embedding("emb_test_chunk_1")

        assert result is not None
        assert result.chunk.chunk_id == "test_chunk_1"
        mock_collection.get.assert_called_once_with(
            ids=["emb_test_chunk_1"],
            include=["embeddings", "documents", "metadatas"]
        )

    @pytest.mark.asyncio
    async def test_get_embedding_not_found(self, mock_settings):
        """Test getting embedding that doesn't exist."""
        store = ChromaVectorStore(mock_settings)

        mock_collection = Mock()
        mock_collection.get.return_value = {"ids": [], "documents": [], "metadatas": []}
        store._collection = mock_collection

        result = await store.get_embedding("nonexistent_id")
        assert result is None

    @pytest.mark.asyncio
    async def test_get_collection_info(self, mock_settings):
        """Test getting collection information."""
        store = ChromaVectorStore(mock_settings)

        mock_collection = Mock()
        mock_collection.count.return_value = 100
        mock_collection.metadata = {"description": "test collection"}
        store._collection = mock_collection

        info = await store.get_collection_info()

        assert info["name"] == "test_collection"
        assert info["count"] == 100
        assert info["metadata"]["description"] == "test collection"
        assert "stats" in info

    def test_stats_tracking(self, mock_settings):
        """Test statistics tracking."""
        store = ChromaVectorStore(mock_settings)

        # Initial stats
        stats = store.get_stats()
        assert stats["total_embeddings"] == 0
        assert stats["total_searches"] == 0
        assert stats["failed_operations"] == 0

        # Reset stats
        store.reset_stats()
        reset_stats = store.get_stats()
        assert reset_stats["total_embeddings"] == 0


class TestVectorStoreFactory:
    """Test cases for VectorStoreFactory."""

    def test_create_chromadb_store(self, mock_settings):
        """Test creating ChromaDB store through factory."""
        store = VectorStoreFactory.create_vector_store(mock_settings)

        assert isinstance(store, ChromaVectorStore)
        assert store.collection_name == "test_collection"

    def test_unsupported_provider(self, mock_settings):
        """Test creating store with unsupported provider."""
        mock_settings.vector_store_provider = "unsupported"

        with pytest.raises(IngestionError) as exc_info:
            VectorStoreFactory.create_vector_store(mock_settings)

        assert "Unsupported vector store provider" in str(exc_info.value)

    def test_get_available_providers(self):
        """Test getting available providers."""
        providers = VectorStoreFactory.get_available_providers()

        assert "chromadb" in providers
        assert len(providers) >= 1

    def test_validate_chromadb_config(self, mock_settings):
        """Test validating ChromaDB configuration."""
        # Valid config
        assert VectorStoreFactory.validate_provider_config(mock_settings) is True

        # Invalid config (no collection name)
        mock_settings.chroma_collection_name = None
        with pytest.raises(IngestionError) as exc_info:
            VectorStoreFactory.validate_provider_config(mock_settings)

        assert "collection name is required" in str(exc_info.value)

    def test_get_provider_info(self):
        """Test getting provider information."""
        info = VectorStoreFactory.get_provider_info("chromadb")

        assert info["name"] == "ChromaDB"
        assert "features" in info
        assert "requirements" in info
        assert "config_keys" in info

    def test_list_providers(self):
        """Test listing all providers."""
        providers = VectorStoreFactory.list_providers()

        assert "chromadb" in providers
        assert isinstance(providers["chromadb"], dict)
        assert "name" in providers["chromadb"]
