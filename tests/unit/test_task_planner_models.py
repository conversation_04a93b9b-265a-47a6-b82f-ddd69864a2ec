"""
Unit tests for Task Planner data models.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock

from src.task_planner.models import (
    DependencyGraph,
    DependencyType,
    FileReference,
    Milestone,
    RiskLevel,
    Task,
    TaskDependency,
    TaskPlan,
    TaskPriority,
    TaskRisk,
    TaskStatus,
    TaskType,
    Timeline,
)


class TestTask:
    """Test Task model functionality."""

    def test_task_creation(self):
        """Test basic task creation."""
        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
            priority=TaskPriority.HIGH,
            estimate_hours=8.0,
        )

        assert task.id == "T001"
        assert task.title == "Test Task"
        assert task.task_type == TaskType.IMPLEMENTATION
        assert task.priority == TaskPriority.HIGH
        assert task.estimate_hours == 8.0
        assert task.status == TaskStatus.NOT_STARTED
        assert len(task.dependencies) == 0

    def test_add_dependency(self):
        """Test adding dependencies to a task."""
        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
        )

        task.add_dependency("T000", DependencyType.FINISH_TO_START, 2.0)

        assert len(task.dependencies) == 1
        assert task.dependencies[0].task_id == "T000"
        assert task.dependencies[0].dependency_type == DependencyType.FINISH_TO_START
        assert task.dependencies[0].lag_hours == 2.0

    def test_add_risk(self):
        """Test adding risks to a task."""
        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
        )

        task.add_risk(
            description="High complexity risk",
            level=RiskLevel.HIGH,
            probability=0.7,
            impact="Potential delays",
            mitigation="Break down into smaller tasks"
        )

        assert len(task.risks) == 1
        assert task.risks[0].description == "High complexity risk"
        assert task.risks[0].level == RiskLevel.HIGH
        assert task.risks[0].probability == 0.7

    def test_get_dependency_ids(self):
        """Test getting dependency IDs."""
        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
        )

        task.add_dependency("T000")
        task.add_dependency("T002")

        dependency_ids = task.get_dependency_ids()
        assert dependency_ids == ["T000", "T002"]

    def test_is_ready_to_start(self):
        """Test checking if task is ready to start."""
        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
        )

        # Task with no dependencies should be ready
        assert task.is_ready_to_start(set())

        # Add dependency
        task.add_dependency("T000")

        # Should not be ready if dependency not completed
        assert not task.is_ready_to_start(set())

        # Should be ready if dependency completed
        assert task.is_ready_to_start({"T000"})

        # Task already in progress should not be ready
        task.status = TaskStatus.IN_PROGRESS
        assert not task.is_ready_to_start({"T000"})


class TestTimeline:
    """Test Timeline model functionality."""

    def test_timeline_creation(self):
        """Test basic timeline creation."""
        start_date = datetime.now()
        end_date = start_date + timedelta(days=30)

        timeline = Timeline(
            title="Test Timeline",
            description="A test timeline",
            start_date=start_date,
            end_date=end_date,
        )

        assert timeline.title == "Test Timeline"
        assert timeline.start_date == start_date
        assert timeline.end_date == end_date
        assert timeline.total_estimated_hours == 0.0
        assert len(timeline.tasks) == 0

    def test_add_task(self):
        """Test adding tasks to timeline."""
        timeline = Timeline(
            title="Test Timeline",
            description="A test timeline",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
        )

        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
            estimate_hours=8.0,
        )

        timeline.add_task(task)

        assert len(timeline.tasks) == 1
        assert timeline.total_estimated_hours == 8.0

    def test_add_milestone(self):
        """Test adding milestones to timeline."""
        timeline = Timeline(
            title="Test Timeline",
            description="A test timeline",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
        )

        milestone = Milestone(
            id="M001",
            title="Test Milestone",
            description="A test milestone",
            target_date=datetime.now() + timedelta(days=15),
        )

        timeline.add_milestone(milestone)

        assert len(timeline.milestones) == 1
        assert timeline.milestones[0].title == "Test Milestone"

    def test_get_task_by_id(self):
        """Test getting task by ID."""
        timeline = Timeline(
            title="Test Timeline",
            description="A test timeline",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
        )

        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
        )

        timeline.add_task(task)

        found_task = timeline.get_task_by_id("T001")
        assert found_task is not None
        assert found_task.id == "T001"

        not_found = timeline.get_task_by_id("T999")
        assert not_found is None

    def test_get_tasks_by_status(self):
        """Test getting tasks by status."""
        timeline = Timeline(
            title="Test Timeline",
            description="A test timeline",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
        )

        task1 = Task(id="T001", title="Task 1", description="Task 1", task_type=TaskType.IMPLEMENTATION)
        task2 = Task(id="T002", title="Task 2", description="Task 2", task_type=TaskType.TESTING)
        task2.status = TaskStatus.IN_PROGRESS

        timeline.add_task(task1)
        timeline.add_task(task2)

        not_started = timeline.get_tasks_by_status(TaskStatus.NOT_STARTED)
        assert len(not_started) == 1
        assert not_started[0].id == "T001"

        in_progress = timeline.get_tasks_by_status(TaskStatus.IN_PROGRESS)
        assert len(in_progress) == 1
        assert in_progress[0].id == "T002"

    def test_get_ready_tasks(self):
        """Test getting ready tasks."""
        timeline = Timeline(
            title="Test Timeline",
            description="A test timeline",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
        )

        # Task with no dependencies
        task1 = Task(id="T001", title="Task 1", description="Task 1", task_type=TaskType.IMPLEMENTATION)

        # Task with dependency on T001
        task2 = Task(id="T002", title="Task 2", description="Task 2", task_type=TaskType.TESTING)
        task2.add_dependency("T001")

        # Completed task
        task3 = Task(id="T003", title="Task 3", description="Task 3", task_type=TaskType.DOCUMENTATION)
        task3.status = TaskStatus.COMPLETED

        timeline.add_task(task1)
        timeline.add_task(task2)
        timeline.add_task(task3)

        ready_tasks = timeline.get_ready_tasks()

        # Only T001 should be ready (no dependencies, not started)
        assert len(ready_tasks) == 1
        assert ready_tasks[0].id == "T001"


class TestDependencyGraph:
    """Test DependencyGraph model functionality."""

    def test_dependency_graph_creation(self):
        """Test basic dependency graph creation."""
        graph = DependencyGraph()

        assert len(graph.nodes) == 0
        assert len(graph.edges) == 0
        assert len(graph.critical_path) == 0

    def test_add_task(self):
        """Test adding tasks to dependency graph."""
        graph = DependencyGraph()

        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
        )

        graph.add_task(task)

        assert "T001" in graph.nodes
        assert graph.nodes["T001"] == task
        assert "T001" in graph.edges

    def test_add_dependency(self):
        """Test adding dependencies to graph."""
        graph = DependencyGraph()

        task1 = Task(id="T001", title="Task 1", description="Task 1", task_type=TaskType.IMPLEMENTATION)
        task2 = Task(id="T002", title="Task 2", description="Task 2", task_type=TaskType.TESTING)

        graph.add_task(task1)
        graph.add_task(task2)
        graph.add_dependency("T001", "T002")

        assert "T002" in graph.edges["T001"]

    def test_get_predecessors(self):
        """Test getting task predecessors."""
        graph = DependencyGraph()

        task1 = Task(id="T001", title="Task 1", description="Task 1", task_type=TaskType.IMPLEMENTATION)
        task2 = Task(id="T002", title="Task 2", description="Task 2", task_type=TaskType.TESTING)
        task3 = Task(id="T003", title="Task 3", description="Task 3", task_type=TaskType.DOCUMENTATION)

        graph.add_task(task1)
        graph.add_task(task2)
        graph.add_task(task3)

        graph.add_dependency("T001", "T003")
        graph.add_dependency("T002", "T003")

        predecessors = graph.get_predecessors("T003")
        assert set(predecessors) == {"T001", "T002"}

    def test_get_successors(self):
        """Test getting task successors."""
        graph = DependencyGraph()

        task1 = Task(id="T001", title="Task 1", description="Task 1", task_type=TaskType.IMPLEMENTATION)
        task2 = Task(id="T002", title="Task 2", description="Task 2", task_type=TaskType.TESTING)

        graph.add_task(task1)
        graph.add_task(task2)
        graph.add_dependency("T001", "T002")

        successors = graph.get_successors("T001")
        assert successors == ["T002"]

    def test_has_cycle_no_cycle(self):
        """Test cycle detection with no cycle."""
        graph = DependencyGraph()

        task1 = Task(id="T001", title="Task 1", description="Task 1", task_type=TaskType.IMPLEMENTATION)
        task2 = Task(id="T002", title="Task 2", description="Task 2", task_type=TaskType.TESTING)

        graph.add_task(task1)
        graph.add_task(task2)
        graph.add_dependency("T001", "T002")

        assert not graph.has_cycle()

    def test_has_cycle_with_cycle(self):
        """Test cycle detection with cycle."""
        graph = DependencyGraph()

        task1 = Task(id="T001", title="Task 1", description="Task 1", task_type=TaskType.IMPLEMENTATION)
        task2 = Task(id="T002", title="Task 2", description="Task 2", task_type=TaskType.TESTING)

        graph.add_task(task1)
        graph.add_task(task2)

        # Create a cycle
        graph.add_dependency("T001", "T002")
        graph.add_dependency("T002", "T001")

        assert graph.has_cycle()


class TestTaskPlan:
    """Test TaskPlan model functionality."""

    def test_task_plan_creation(self):
        """Test basic task plan creation."""
        timeline = Timeline(
            title="Test Timeline",
            description="A test timeline",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
        )

        graph = DependencyGraph()

        task_plan = TaskPlan(
            title="Test Plan",
            summary="A test plan",
            timeline=timeline,
            dependency_graph=graph,
            confidence=0.8,
        )

        assert task_plan.title == "Test Plan"
        assert task_plan.confidence == 0.8
        assert task_plan.methodology == "5-phase"

    def test_to_dict(self):
        """Test converting task plan to dictionary."""
        timeline = Timeline(
            title="Test Timeline",
            description="A test timeline",
            start_date=datetime.now(),
            end_date=datetime.now() + timedelta(days=30),
        )

        graph = DependencyGraph()

        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
            estimate_hours=8.0,
        )

        task_plan = TaskPlan(
            title="Test Plan",
            summary="A test plan",
            timeline=timeline,
            dependency_graph=graph,
            tasks=[task],
            confidence=0.8,
        )

        plan_dict = task_plan.to_dict()

        assert plan_dict["title"] == "Test Plan"
        assert plan_dict["confidence"] == 0.8
        assert len(plan_dict["tasks"]) == 1
        assert plan_dict["tasks"][0]["id"] == "T001"
        assert plan_dict["methodology"] == "5-phase"
