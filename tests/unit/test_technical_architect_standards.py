"""
Unit tests for Technical Architect Standards module.
"""

import pytest
import tempfile
from pathlib import Path
from unittest.mock import Mock, patch

from src.technical_architect.standards import (
    DocumentType,
    ProjectStandardsManager,
    StandardsContext,
    StandardsPriority,
    StandardsRule,
)


class TestProjectStandardsManager:
    """Test cases for ProjectStandardsManager."""

    @pytest.fixture
    def temp_docs_dir(self):
        """Create temporary docs directory with sample files."""
        with tempfile.TemporaryDirectory() as temp_dir:
            docs_path = Path(temp_dir) / "docs"
            docs_path.mkdir()

            # Create sample rules.md
            rules_content = """
# Project Rules

## Code Quality Standards

- Follow PEP 8 for Python code style
- Use Black for code formatting
- All code must have unit tests with >80% coverage
- Docstrings are required for all public functions

## Testing Standards

- Use pytest framework for testing
- Tests must be deterministic
- Mock external dependencies

## Architecture Rules

- Follow SOLID principles
- Use dependency injection for better testability
- Implement proper error handling
"""
            (docs_path / "rules.md").write_text(rules_content)

            # Create sample design.md
            design_content = """
# Technical Design

## Design Principles

- Maintain separation of concerns
- Use established design patterns
- Ensure scalability and maintainability

## Technology Stack

- Python 3.11+ for backend
- FastAPI for web framework
- PostgreSQL for database
- Docker for containerization
"""
            (docs_path / "design.md").write_text(design_content)

            # Create sample tech.md
            tech_content = """
# Technology Stack

## Core Technologies

- **Language**: Python 3.11+
- **Web Framework**: FastAPI
- **Database**: PostgreSQL
- **Vector Store**: ChromaDB
- **LLM Provider**: OpenAI
- **Containerization**: Docker
"""
            (docs_path / "tech.md").write_text(tech_content)

            yield str(docs_path)

    @pytest.fixture
    def standards_manager(self, temp_docs_dir):
        """Create standards manager with temporary docs."""
        return ProjectStandardsManager(temp_docs_dir)

    def test_initialization(self, temp_docs_dir):
        """Test standards manager initialization."""
        manager = ProjectStandardsManager(temp_docs_dir)
        assert manager.docs_path == Path(temp_docs_dir)
        assert "rules.md" in manager.document_priorities
        assert manager.document_priorities["rules.md"] == StandardsPriority.MANDATORY

    def test_get_standards_context(self, standards_manager):
        """Test getting standards context."""
        context = standards_manager.get_standards_context()

        assert isinstance(context, StandardsContext)
        assert len(context.rules) > 0
        assert isinstance(context.design_principles, list)
        assert isinstance(context.technology_stack, dict)
        assert isinstance(context.coding_standards, dict)
        assert isinstance(context.testing_requirements, dict)
        assert isinstance(context.architectural_constraints, list)

    def test_get_standards_context_caching(self, standards_manager):
        """Test that standards context is cached."""
        context1 = standards_manager.get_standards_context()
        context2 = standards_manager.get_standards_context()

        # Should return the same cached instance
        assert context1 is context2

        # Force refresh should create new instance
        context3 = standards_manager.get_standards_context(force_refresh=True)
        assert context3 is not context1

    def test_get_rules_for_category(self, standards_manager):
        """Test getting rules for specific category."""
        context = standards_manager.get_standards_context()

        # Get code quality rules
        code_quality_rules = standards_manager.get_rules_for_category("code_quality")
        assert isinstance(code_quality_rules, list)
        assert len(code_quality_rules) > 0

        # Get testing rules
        testing_rules = standards_manager.get_rules_for_category("testing")
        assert isinstance(testing_rules, list)

        # Get non-existent category
        empty_rules = standards_manager.get_rules_for_category("nonexistent")
        assert isinstance(empty_rules, list)
        assert len(empty_rules) == 0

    def test_get_mandatory_rules(self, standards_manager):
        """Test getting mandatory rules."""
        mandatory_rules = standards_manager.get_mandatory_rules()

        assert isinstance(mandatory_rules, list)
        assert len(mandatory_rules) > 0
        assert all(rule.priority == StandardsPriority.MANDATORY for rule in mandatory_rules)

    def test_validate_against_standards(self, standards_manager):
        """Test validation against standards."""
        # Test compliant design
        compliant_design = """
This design follows PEP 8 standards and includes comprehensive unit tests.
The implementation uses dependency injection and proper error handling.
All public functions have docstrings and the code is well-documented.
"""

        validation = standards_manager.validate_against_standards(compliant_design)

        assert isinstance(validation, dict)
        assert "compliant" in validation
        assert "violations" in validation
        assert "recommendations" in validation
        assert "score" in validation
        assert 0 <= validation["score"] <= 1

        # Test non-compliant design
        non_compliant_design = """
This design doesn't follow any standards and has no tests.
"""

        validation = standards_manager.validate_against_standards(non_compliant_design)
        assert validation["score"] < 1.0  # Should have lower score

    def test_parse_document(self, standards_manager):
        """Test document parsing."""
        # Create a test document
        test_content = """
# Test Document

## Section 1

- Rule 1: Must follow this rule
- Rule 2: Should follow this guideline
- This is not a rule

## Section 2

> Important: This is a blockquote rule
> Another blockquote

1. Numbered rule 1
2. Numbered rule 2

Components must implement proper interfaces.
Tests should cover all edge cases.
"""

        with tempfile.NamedTemporaryFile(mode="w", suffix=".md", delete=False) as f:
            f.write(test_content)
            f.flush()

            rules = standards_manager._parse_document(Path(f.name))

        assert isinstance(rules, list)
        assert len(rules) > 0

        # Check that rules were extracted
        rule_contents = [rule.content for rule in rules]
        assert any("Must follow this rule" in content for content in rule_contents)
        assert any("Should follow this guideline" in content for content in rule_contents)

    def test_split_into_sections(self, standards_manager):
        """Test splitting document into sections."""
        content = """
# Main Title

Introduction content

## Section 1

Section 1 content
More content

## Section 2

Section 2 content

### Subsection

Subsection content
"""

        sections = standards_manager._split_into_sections(content)

        assert isinstance(sections, dict)
        assert "Section 1" in sections
        assert "Section 2" in sections
        assert "Section 1 content" in sections["Section 1"]

    def test_extract_rules_from_section(self, standards_manager):
        """Test extracting rules from a section."""
        section_content = """
- Rule 1: Follow this pattern
- Rule 2: Use this approach
> Important guideline
1. First numbered rule
2. Second numbered rule

Components must implement interfaces.
Tests should be comprehensive.
"""

        rules = standards_manager._extract_rules_from_section(
            "Test Section", section_content, "test.md", StandardsPriority.MANDATORY
        )

        assert isinstance(rules, list)
        assert len(rules) > 0

        # Check rule properties
        for rule in rules:
            assert isinstance(rule, StandardsRule)
            assert rule.source_file == "test.md"
            assert rule.section == "Test Section"
            assert len(rule.content) > 0

    def test_categorize_rule(self, standards_manager):
        """Test rule categorization."""
        # Test code quality categorization
        assert standards_manager._categorize_rule("Code Quality", "Follow PEP 8 style") == "code_quality"

        # Test testing categorization
        assert standards_manager._categorize_rule("Testing", "Use pytest framework") == "testing"

        # Test architecture categorization
        assert standards_manager._categorize_rule("Architecture", "Follow SOLID principles") == "architecture"

        # Test general categorization
        assert standards_manager._categorize_rule("Other", "Some other rule") == "general"

    def test_extract_tags(self, standards_manager):
        """Test tag extraction from rule text."""
        # Test mandatory tag
        tags = standards_manager._extract_tags("This rule must be followed")
        assert "mandatory" in tags

        # Test testing tag
        tags = standards_manager._extract_tags("All code must have unit tests")
        assert "testing" in tags

        # Test SOLID tag
        tags = standards_manager._extract_tags("Follow single responsibility principle")
        assert "solid" in tags

        # Test patterns tag
        tags = standards_manager._extract_tags("Use factory pattern for object creation")
        assert "patterns" in tags

    def test_extract_design_principles(self, standards_manager):
        """Test design principles extraction."""
        rules = [
            StandardsRule(
                title="SOLID Rule",
                content="Follow SOLID principles for better design",
                source_file="rules.md",
                priority=StandardsPriority.MANDATORY,
                category="architecture",
                tags=["solid"],
            ),
            StandardsRule(
                title="Other Rule",
                content="Some other rule",
                source_file="rules.md",
                priority=StandardsPriority.MANDATORY,
                category="general",
                tags=[],
            ),
        ]

        principles = standards_manager._extract_design_principles(rules)

        assert isinstance(principles, list)
        assert len(principles) > 0
        assert any("SOLID" in principle for principle in principles)

    def test_extract_technology_stack(self, standards_manager):
        """Test technology stack extraction."""
        rules = [
            StandardsRule(
                title="Tech Rule",
                content="Use Python and FastAPI for development",
                source_file="tech.md",
                priority=StandardsPriority.GUIDANCE,
                category="technology",
            ),
        ]

        tech_stack = standards_manager._extract_technology_stack(rules)

        assert isinstance(tech_stack, dict)
        # Should extract Python and FastAPI
        assert "language" in tech_stack or "web_framework" in tech_stack

    def test_extract_coding_standards(self, standards_manager):
        """Test coding standards extraction."""
        rules = [
            StandardsRule(
                title="Style Rule",
                content="Follow PEP 8 and use Black formatter with 88 characters line length",
                source_file="rules.md",
                priority=StandardsPriority.MANDATORY,
                category="code_quality",
            ),
        ]

        standards = standards_manager._extract_coding_standards(rules)

        assert isinstance(standards, dict)
        assert "style_guide" in standards
        assert "formatter" in standards
        assert "line_length" in standards
        assert standards["style_guide"] == "PEP 8"

    def test_extract_testing_requirements(self, standards_manager):
        """Test testing requirements extraction."""
        rules = [
            StandardsRule(
                title="Testing Rule",
                content="Use pytest framework with 80% coverage minimum",
                source_file="rules.md",
                priority=StandardsPriority.MANDATORY,
                category="testing",
            ),
        ]

        requirements = standards_manager._extract_testing_requirements(rules)

        assert isinstance(requirements, dict)
        assert "framework" in requirements
        assert "min_coverage" in requirements
        assert requirements["framework"] == "pytest"
        assert requirements["min_coverage"] == 80

    def test_extract_architectural_constraints(self, standards_manager):
        """Test architectural constraints extraction."""
        rules = [
            StandardsRule(
                title="Architecture Rule",
                content="Maintain separation between client and server",
                source_file="design.md",
                priority=StandardsPriority.RECOMMENDED,
                category="architecture",
            ),
        ]

        constraints = standards_manager._extract_architectural_constraints(rules)

        assert isinstance(constraints, list)
        assert len(constraints) > 0

    def test_check_rule_compliance(self, standards_manager):
        """Test rule compliance checking."""
        # Test compliant content
        compliant_content = "This design includes comprehensive unit tests using pytest framework"
        test_rule = StandardsRule(
            title="Testing Rule",
            content="All code must have unit tests",
            source_file="rules.md",
            priority=StandardsPriority.MANDATORY,
            category="testing",
        )

        assert standards_manager._check_rule_compliance(compliant_content, test_rule) is True

        # Test non-compliant content
        non_compliant_content = "This design has no tests and ignores errors"
        assert standards_manager._check_rule_compliance(non_compliant_content, test_rule) is False  # Should detect non-compliance

    def test_get_standards_summary(self, standards_manager):
        """Test getting standards summary."""
        summary = standards_manager.get_standards_summary()

        assert isinstance(summary, dict)
        assert "total_rules" in summary
        assert "mandatory_rules" in summary
        assert "recommended_rules" in summary
        assert "guidance_rules" in summary
        assert "categories" in summary
        assert "design_principles" in summary
        assert "technology_stack" in summary
        assert "coding_standards" in summary
        assert "testing_requirements" in summary
        assert "architectural_constraints" in summary

        # Verify counts
        assert summary["total_rules"] >= 0
        assert summary["mandatory_rules"] >= 0
        assert summary["recommended_rules"] >= 0
        assert summary["guidance_rules"] >= 0
