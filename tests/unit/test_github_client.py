"""
Unit tests for GitHubClient.

Tests the GitHub API client functionality including authentication,
repository information retrieval, and error handling.
"""

import pytest
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime
from github import GithubException

from src.ingestion.github.client import GitHub<PERSON>lient
from src.ingestion.exceptions import (
    AuthenticationError,
    RepositoryError,
    RateLimitError,
)
from src.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.github_token = "test_token"
    settings.github_api_url = "https://api.github.com"
    settings.max_file_size = 10485760
    settings.allowed_file_types = ["py", "js", "md"]
    settings.temp_dir = "/tmp/test"
    return settings


@pytest.fixture
def github_client(mock_settings):
    """Create GitHubClient instance for testing."""
    return GitHubClient(mock_settings)


class TestGitHubClient:
    """Test cases for GitHubClient."""

    @pytest.mark.asyncio
    async def test_authenticate_with_token_success(self, github_client):
        """Test successful authentication with token."""
        with patch('src.ingestion.github.client.Github') as mock_github:
            mock_user = Mock()
            mock_user.login = "testuser"
            mock_github.return_value.get_user.return_value = mock_user

            result = await github_client.authenticate()

            assert result is True
            assert github_client._authenticated is True
            mock_github.assert_called_once()

    @pytest.mark.asyncio
    async def test_authenticate_without_token_success(self, mock_settings):
        """Test successful authentication without token (public access)."""
        mock_settings.github_token = None
        client = GitHubClient(mock_settings)

        with patch('src.ingestion.github.client.Github') as mock_github:
            result = await client.authenticate()

            assert result is True
            assert client._authenticated is True
            mock_github.assert_called_once()

    @pytest.mark.asyncio
    async def test_authenticate_failure(self, github_client):
        """Test authentication failure."""
        with patch('src.ingestion.github.client.Github') as mock_github:
            mock_github.return_value.get_user.side_effect = GithubException(401, "Unauthorized")

            with pytest.raises(AuthenticationError) as exc_info:
                await github_client.authenticate()

            assert "Failed to authenticate with GitHub" in str(exc_info.value)
            assert exc_info.value.service == "github"

    def test_parse_repository_url_https(self, github_client):
        """Test parsing HTTPS GitHub URL."""
        url = "https://github.com/owner/repo"
        owner, repo = github_client._parse_repository_url(url)

        assert owner == "owner"
        assert repo == "repo"

    def test_parse_repository_url_ssh(self, github_client):
        """Test parsing SSH GitHub URL."""
        url = "**************:owner/repo.git"
        owner, repo = github_client._parse_repository_url(url)

        assert owner == "owner"
        assert repo == "repo"

    def test_parse_repository_url_short(self, github_client):
        """Test parsing short format URL."""
        url = "owner/repo"
        owner, repo = github_client._parse_repository_url(url)

        assert owner == "owner"
        assert repo == "repo"

    def test_parse_repository_url_invalid(self, github_client):
        """Test parsing invalid URL."""
        url = "invalid-url"

        with pytest.raises(RepositoryError) as exc_info:
            github_client._parse_repository_url(url)

        assert "Invalid GitHub repository URL format" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_get_repository_info_success(self, github_client):
        """Test successful repository information retrieval."""
        with patch('src.ingestion.github.client.Github') as mock_github:
            # Mock repository object
            mock_repo = Mock()
            mock_repo.name = "test-repo"
            mock_repo.owner.login = "test-owner"
            mock_repo.default_branch = "main"
            mock_repo.private = False
            mock_repo.description = "Test repository"
            mock_repo.language = "Python"
            mock_repo.size = 1024
            mock_repo.created_at = datetime(2023, 1, 1)
            mock_repo.updated_at = datetime(2023, 12, 1)

            # Mock commits
            mock_commit = Mock()
            mock_commit.sha = "abc123"
            mock_commits = Mock()
            mock_commits.totalCount = 1
            mock_commits.__getitem__ = Mock(return_value=mock_commit)
            mock_commits.__iter__ = Mock(return_value=iter([mock_commit]))
            mock_repo.get_commits.return_value = mock_commits

            mock_github_instance = mock_github.return_value
            mock_github_instance.get_repo.return_value = mock_repo

            # Mock rate limit
            mock_rate_limit = Mock()
            mock_rate_limit.core.remaining = 5000
            mock_rate_limit.core.reset = datetime.now()
            mock_github_instance.get_rate_limit.return_value = mock_rate_limit

            github_client._authenticated = True
            github_client._github = mock_github_instance

            repo_info = await github_client.get_repository_info("https://github.com/test-owner/test-repo")

            assert repo_info.name == "test-repo"
            assert repo_info.owner == "test-owner"
            assert repo_info.branch == "main"
            assert repo_info.commit_sha == "abc123"
            assert repo_info.is_private is False

    @pytest.mark.asyncio
    async def test_get_repository_info_not_found(self, github_client):
        """Test repository not found error."""
        with patch('src.ingestion.github.client.Github') as mock_github:
            mock_github_instance = mock_github.return_value
            mock_github_instance.get_repo.side_effect = GithubException(404, "Not Found")

            github_client._authenticated = True
            github_client._github = mock_github_instance

            with pytest.raises(RepositoryError) as exc_info:
                await github_client.get_repository_info("https://github.com/owner/nonexistent")

            assert "Failed to get repository information" in str(exc_info.value)

    @pytest.mark.asyncio
    async def test_rate_limit_check_exceeded(self, github_client):
        """Test rate limit exceeded handling."""
        from datetime import timezone, timedelta
        github_client._rate_limit_remaining = 5
        # Set reset time to 1 hour in the future
        github_client._rate_limit_reset_time = datetime.now(timezone.utc) + timedelta(hours=1)

        with pytest.raises(RateLimitError) as exc_info:
            await github_client._check_rate_limit()

        assert "GitHub API rate limit nearly exceeded" in str(exc_info.value)
        assert exc_info.value.service == "github"

    @pytest.mark.asyncio
    async def test_list_files_success(self, github_client):
        """Test successful file listing."""
        with patch('src.ingestion.github.client.Github') as mock_github:
            # Mock repository
            mock_repo = Mock()
            mock_repo.get_branch.return_value = Mock()  # Branch exists

            mock_github_instance = mock_github.return_value
            mock_github_instance.get_repo.return_value = mock_repo

            github_client._authenticated = True
            github_client._github = mock_github_instance
            github_client._rate_limit_remaining = 5000

            # Mock the recursive file listing
            with patch.object(github_client, '_list_files_recursive') as mock_list_files:
                mock_list_files.return_value = None  # Modifies files list in place

                files = await github_client.list_files("https://github.com/owner/repo")

                assert isinstance(files, list)
                mock_list_files.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_file_content_success(self, github_client):
        """Test successful file content retrieval."""
        with patch('src.ingestion.github.client.Github') as mock_github:
            # Mock repository and file content
            mock_repo = Mock()
            mock_repo.get_branch.return_value = Mock()  # Branch exists

            mock_file_content = Mock()
            mock_file_content.decoded_content = b"print('Hello, World!')"
            mock_repo.get_contents.return_value = mock_file_content

            mock_github_instance = mock_github.return_value
            mock_github_instance.get_repo.return_value = mock_repo

            github_client._authenticated = True
            github_client._github = mock_github_instance
            github_client._rate_limit_remaining = 5000

            # Mock rate limit update
            with patch.object(github_client, '_update_rate_limit_info'):
                content = await github_client.get_file_content(
                    "https://github.com/owner/repo", 
                    "main.py"
                )

                assert content == "print('Hello, World!')"

    @pytest.mark.asyncio
    async def test_get_file_content_directory_error(self, github_client):
        """Test error when trying to get content of a directory."""
        with patch('src.ingestion.github.client.Github') as mock_github:
            # Mock repository
            mock_repo = Mock()
            mock_repo.get_branch.return_value = Mock()
            mock_repo.get_contents.return_value = [Mock(), Mock()]  # List indicates directory

            mock_github_instance = mock_github.return_value
            mock_github_instance.get_repo.return_value = mock_repo

            github_client._authenticated = True
            github_client._github = mock_github_instance
            github_client._rate_limit_remaining = 5000

            with patch.object(github_client, '_update_rate_limit_info'):
                with pytest.raises(RepositoryError) as exc_info:
                    await github_client.get_file_content(
                        "https://github.com/owner/repo", 
                        "src/"
                    )

                assert "is a directory, not a file" in str(exc_info.value)
