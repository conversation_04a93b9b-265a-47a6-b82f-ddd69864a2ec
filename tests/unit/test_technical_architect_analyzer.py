"""
Unit tests for Technical Architect Analyzer module.
"""

import pytest
from unittest.mock import Mock

from src.ingestion.base import SearchResult
from src.technical_architect.analyzer import (
    ArchitectureAnalyzer,
    ArchitecturePattern,
    ComponentAnalysis,
    SOLIDPrinciple,
)


class TestArchitectureAnalyzer:
    """Test cases for ArchitectureAnalyzer."""

    @pytest.fixture
    def analyzer(self):
        """Create analyzer instance."""
        return ArchitectureAnalyzer()

    @pytest.fixture
    def sample_search_results(self):
        """Create sample search results."""
        def create_search_result(content, file_path, similarity_score):
            mock_chunk = Mock()
            mock_chunk.content = content
            mock_chunk.file_metadata.file_path = file_path

            mock_embedded_chunk = Mock()
            mock_embedded_chunk.chunk = mock_chunk

            return SearchResult(
                embedded_chunk=mock_embedded_chunk,
                similarity_score=similarity_score,
                rank=1
            )

        return [
            create_search_result(
                """
class UserFactory:
    @staticmethod
    def create_user(name: str) -> User:
        return User(name)
""",
                "src/user_factory.py",
                0.9
            ),
            create_search_result(
                """
class DatabaseRepository:
    def __init__(self, db_client):
        self.db_client = db_client

    def find_user(self, user_id: str):
        return self.db_client.query(user_id)

    def save_user(self, user):
        return self.db_client.save(user)
""",
                "src/repository.py",
                0.8
            ),
            create_search_result(
                """
def process_data(data):
    # Complex processing logic
    if data.type == "A":
        return process_type_a(data)
    elif data.type == "B":
        return process_type_b(data)
    elif data.type == "C":
        return process_type_c(data)
    else:
        return default_process(data)
""",
                "src/processor.py",
                0.7
            ),
        ]

    def test_analyze_codebase(self, analyzer, sample_search_results):
        """Test codebase analysis."""
        analysis = analyzer.analyze_codebase(sample_search_results)

        assert analysis is not None
        assert isinstance(analysis.patterns_detected, list)
        assert isinstance(analysis.solid_compliance, dict)
        assert isinstance(analysis.code_quality_metrics, dict)
        assert isinstance(analysis.recommendations, list)
        assert isinstance(analysis.concerns, list)
        assert isinstance(analysis.strengths, list)
        assert 0 <= analysis.confidence <= 1

    def test_detect_factory_pattern(self, analyzer):
        """Test factory pattern detection."""
        factory_code = """
class UserFactory:
    @staticmethod
    def create_user(name: str) -> User:
        return User(name)
"""
        assert analyzer._detect_factory_pattern(factory_code) is True

        non_factory_code = """
class User:
    def __init__(self, name: str):
        self.name = name
"""
        assert analyzer._detect_factory_pattern(non_factory_code) is False

    def test_detect_singleton_pattern(self, analyzer):
        """Test singleton pattern detection."""
        singleton_code = """
class Singleton:
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
"""
        assert analyzer._detect_singleton_pattern(singleton_code) is True

        non_singleton_code = """
class RegularClass:
    def __init__(self):
        pass
"""
        assert analyzer._detect_singleton_pattern(non_singleton_code) is False

    def test_detect_dependency_injection(self, analyzer):
        """Test dependency injection pattern detection."""
        di_code = """
class Service:
    def __init__(self, repository, logger):
        self.repository = repository
        self.logger = logger
"""
        assert analyzer._detect_dependency_injection(di_code) is True

        non_di_code = """
class Service:
    def __init__(self):
        pass
"""
        assert analyzer._detect_dependency_injection(non_di_code) is False

    def test_detect_repository_pattern(self, analyzer):
        """Test repository pattern detection."""
        repo_code = """
class UserRepository:
    def find_by_id(self, user_id):
        pass
    
    def save(self, user):
        pass
    
    def delete(self, user):
        pass
"""
        assert analyzer._detect_repository_pattern(repo_code) is True

        non_repo_code = """
class User:
    def get_name(self):
        return self.name
"""
        assert analyzer._detect_repository_pattern(non_repo_code) is False

    def test_analyze_component(self, analyzer):
        """Test individual component analysis."""
        mock_chunk = Mock()
        mock_chunk.content = """
class UserService:
    '''Service for managing user operations.'''

    def __init__(self, repository):
        self.repository = repository

    def create_user(self, name):
        user = User(name)
        return self.repository.save(user)

    def find_user(self, user_id):
        return self.repository.find_by_id(user_id)
"""
        mock_chunk.file_metadata.file_path = "src/user_service.py"

        mock_embedded_chunk = Mock()
        mock_embedded_chunk.chunk = mock_chunk

        search_result = SearchResult(
            embedded_chunk=mock_embedded_chunk,
            similarity_score=0.9,
            rank=1
        )

        component = analyzer._analyze_component(search_result)

        assert component is not None
        assert component.component_name == "UserService"
        assert component.file_path == "src/user_service.py"
        assert component.component_type == "class"
        assert len(component.responsibilities) > 0
        assert len(component.dependencies) >= 0
        assert isinstance(component.patterns_used, list)
        assert isinstance(component.solid_violations, list)
        assert component.complexity_score >= 0

    def test_determine_component_type(self, analyzer):
        """Test component type determination."""
        class_content = "class MyClass:\n    pass"
        assert analyzer._determine_component_type(class_content, "test.py") == "class"

        function_content = "def my_function():\n    pass"
        assert analyzer._determine_component_type(function_content, "test.py") == "function"

        module_content = "import os\nVARIABLE = 'value'"
        assert analyzer._determine_component_type(module_content, "test.py") == "module"

        doc_content = "# Documentation\nThis is a readme file."
        assert analyzer._determine_component_type(doc_content, "README.md") == "documentation"

    def test_extract_component_name(self, analyzer):
        """Test component name extraction."""
        class_content = "class MyClass:\n    pass"
        assert analyzer._extract_component_name(class_content, "test.py") == "MyClass"

        function_content = "def my_function():\n    pass"
        assert analyzer._extract_component_name(function_content, "test.py") == "my_function"

        module_content = "import os"
        assert analyzer._extract_component_name(module_content, "src/utils.py") == "utils"

    def test_extract_responsibilities(self, analyzer):
        """Test responsibility extraction."""
        content_with_docstring = '''
class UserService:
    """
    Service for managing user operations.
    Handles user creation and retrieval.
    Provides validation and business logic.
    """
    pass
'''
        responsibilities = analyzer._extract_responsibilities(content_with_docstring)
        assert len(responsibilities) > 0
        assert any("user" in resp.lower() for resp in responsibilities)

    def test_extract_dependencies(self, analyzer):
        """Test dependency extraction."""
        content_with_imports = """
from src.repository import UserRepository
from src.logger import Logger
import json
import os
"""
        dependencies = analyzer._extract_dependencies(content_with_imports)
        assert "src.repository" in dependencies
        assert "src.logger" in dependencies
        # Standard library imports should be filtered out
        assert "json" not in dependencies
        assert "os" not in dependencies

    def test_check_solid_violations(self, analyzer):
        """Test SOLID violation detection."""
        # Code with potential SRP violation (many methods)
        complex_code = """
class ComplexClass:
    def method1(self): pass
    def method2(self): pass
    def method3(self): pass
    def method4(self): pass
    def method5(self): pass
    def method6(self): pass
    def method7(self): pass
    def method8(self): pass
    def method9(self): pass
    def method10(self): pass
    def method11(self): pass
    def method12(self): pass
    def method13(self): pass
    def method14(self): pass
    def method15(self): pass
    def method16(self): pass
"""
        violations = analyzer._check_solid_violations(complex_code)
        assert len(violations) > 0
        assert any("Single Responsibility" in violation for violation in violations)

        # Code with potential OCP violation (large if/elif chain)
        ocp_violation_code = """
def process_data(data_type):
    if data_type == "A":
        return "Process A"
    elif data_type == "B":
        return "Process B"
    elif data_type == "C":
        return "Process C"
"""
        violations = analyzer._check_solid_violations(ocp_violation_code)
        assert any("Open/Closed" in violation for violation in violations)

    def test_calculate_complexity(self, analyzer):
        """Test complexity calculation."""
        simple_code = """
def simple_function():
    return "hello"
"""
        complexity = analyzer._calculate_complexity(simple_code)
        assert complexity >= 0
        assert complexity < 5  # Should be low complexity

        complex_code = """
class ComplexClass:
    def method1(self):
        if condition1:
            for item in items:
                try:
                    if condition2:
                        while condition3:
                            process(item)
                except Exception:
                    handle_error()
    
    def method2(self):
        if condition4:
            for item in items2:
                process2(item)
"""
        complex_complexity = analyzer._calculate_complexity(complex_code)
        assert complex_complexity > complexity  # Should be higher complexity

    def test_detect_patterns_across_components(self, analyzer, sample_search_results):
        """Test pattern detection across multiple components."""
        # Create mock components with patterns
        components = []
        for result in sample_search_results:
            component = analyzer._analyze_component(result)
            if component:
                components.append(component)

        patterns = analyzer._detect_patterns(components)
        assert isinstance(patterns, list)
        # The patterns detected depend on the actual content analysis
        # Since we're using mock data, we just verify the method works
        assert len(patterns) >= 0  # Should return a list (may be empty for mock data)

    def test_analyze_solid_compliance(self, analyzer):
        """Test SOLID compliance analysis."""
        # Create mock components
        components = [
            ComponentAnalysis(
                component_name="GoodComponent",
                file_path="good.py",
                component_type="class",
                responsibilities=["Single responsibility"],
                dependencies=["dep1"],
                patterns_used=[ArchitecturePattern.DEPENDENCY_INJECTION],
                solid_violations=[],
                complexity_score=2.0,
            ),
            ComponentAnalysis(
                component_name="BadComponent",
                file_path="bad.py",
                component_type="class",
                responsibilities=["Multiple", "Different", "Responsibilities"],
                dependencies=["dep1", "dep2", "dep3", "dep4", "dep5"],
                patterns_used=[],
                solid_violations=["Single Responsibility Principle violation"],
                complexity_score=8.0,
            ),
        ]

        compliance = analyzer._analyze_solid_compliance(components)
        assert isinstance(compliance, dict)
        assert len(compliance) == 5  # All 5 SOLID principles
        for principle, score in compliance.items():
            assert isinstance(principle, SOLIDPrinciple)
            assert 0 <= score <= 1
