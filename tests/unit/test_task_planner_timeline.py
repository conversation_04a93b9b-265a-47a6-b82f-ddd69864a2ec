"""
Unit tests for Task Planner timeline generator.
"""

import pytest
from datetime import datetime, timedelta
from unittest.mock import Mock

from src.task_planner.timeline import TimelineGenerator
from src.task_planner.models import (
    DependencyType,
    Task,
    TaskPriority,
    TaskStatus,
    TaskType,
)


class TestTimelineGenerator:
    """Test TimelineGenerator functionality."""

    @pytest.fixture
    def timeline_generator(self):
        """Create a timeline generator for testing."""
        return TimelineGenerator(working_hours_per_day=8.0, working_days_per_week=5)

    @pytest.fixture
    def sample_tasks(self):
        """Create sample tasks for testing."""
        tasks = []

        # Task 1: Analysis (no dependencies)
        task1 = Task(
            id="T001",
            title="Analysis Task",
            description="Analyze requirements",
            task_type=TaskType.ANALYSIS,
            priority=TaskPriority.HIGH,
            estimate_hours=16.0,
            tags=["phase-1", "analysis"]
        )
        tasks.append(task1)

        # Task 2: Design (depends on T001)
        task2 = Task(
            id="T002",
            title="Design Task",
            description="Design system",
            task_type=TaskType.DESIGN,
            priority=TaskPriority.HIGH,
            estimate_hours=24.0,
            tags=["phase-2", "design"]
        )
        task2.add_dependency("T001")
        tasks.append(task2)

        # Task 3: Implementation (depends on T002)
        task3 = Task(
            id="T003",
            title="Implementation Task",
            description="Implement system",
            task_type=TaskType.IMPLEMENTATION,
            priority=TaskPriority.MEDIUM,
            estimate_hours=40.0,
            tags=["phase-3", "implementation"]
        )
        task3.add_dependency("T002")
        tasks.append(task3)

        # Task 4: Testing (depends on T003)
        task4 = Task(
            id="T004",
            title="Testing Task",
            description="Test system",
            task_type=TaskType.TESTING,
            priority=TaskPriority.HIGH,
            estimate_hours=16.0,
            tags=["phase-4", "testing"]
        )
        task4.add_dependency("T003")
        tasks.append(task4)

        return tasks

    def test_initialization(self, timeline_generator):
        """Test timeline generator initialization."""
        assert timeline_generator.working_hours_per_day == 8.0
        assert timeline_generator.working_days_per_week == 5
        assert timeline_generator.hours_per_week == 40.0

        # Check buffer factors
        assert "implementation" in timeline_generator.buffer_factors
        assert timeline_generator.buffer_factors["implementation"] == 1.5

    def test_generate_timeline_basic(self, timeline_generator, sample_tasks):
        """Test basic timeline generation."""
        start_date = datetime(2024, 1, 1, 9, 0)

        timeline = timeline_generator.generate_timeline(
            tasks=sample_tasks,
            start_date=start_date,
            title="Test Timeline",
            description="A test timeline"
        )

        assert timeline.title == "Test Timeline"
        assert timeline.start_date == start_date
        assert timeline.end_date > start_date
        assert len(timeline.tasks) == len(sample_tasks)
        assert timeline.total_estimated_hours > 0
        assert 0.0 <= timeline.confidence <= 1.0

    def test_generate_timeline_with_milestones(self, timeline_generator, sample_tasks):
        """Test timeline generation with milestones."""
        timeline = timeline_generator.generate_timeline(
            tasks=sample_tasks,
            title="Test Timeline with Milestones"
        )

        # Should generate milestones based on phases
        assert len(timeline.milestones) > 0

        # Check milestone properties
        for milestone in timeline.milestones:
            assert milestone.id is not None
            assert milestone.title is not None
            assert milestone.target_date is not None
            assert isinstance(milestone.task_ids, list)

    def test_estimate_task_duration_without_buffer(self, timeline_generator):
        """Test task duration estimation without buffer."""
        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
            estimate_hours=20.0
        )

        duration = timeline_generator.estimate_task_duration(task, include_buffer=False)
        assert duration == 20.0

    def test_estimate_task_duration_with_buffer(self, timeline_generator):
        """Test task duration estimation with buffer."""
        task = Task(
            id="T001",
            title="Test Task",
            description="A test task",
            task_type=TaskType.IMPLEMENTATION,
            estimate_hours=20.0
        )

        duration = timeline_generator.estimate_task_duration(task, include_buffer=True)

        # Should be more than original estimate due to buffer
        assert duration > 20.0
        # Should apply implementation buffer factor (1.5)
        expected = 20.0 * 1.5
        assert abs(duration - expected) < 1.0  # Allow for additional complexity buffers

    def test_estimate_task_duration_complex_task(self, timeline_generator):
        """Test duration estimation for complex tasks."""
        # Very complex task (>2 weeks)
        complex_task = Task(
            id="T001",
            title="Complex Task",
            description="A very complex task",
            task_type=TaskType.IMPLEMENTATION,
            estimate_hours=100.0  # >2 weeks
        )

        duration = timeline_generator.estimate_task_duration(complex_task, include_buffer=True)

        # Should have additional buffer for complexity
        base_buffer = 100.0 * 1.5  # Implementation buffer
        assert duration > base_buffer

    def test_calculate_critical_path_simple(self, timeline_generator, sample_tasks):
        """Test critical path calculation for simple dependency chain."""
        critical_path = timeline_generator.calculate_critical_path(sample_tasks)

        # All tasks should be on critical path since they form a chain
        assert len(critical_path) == len(sample_tasks)

        # Should include all task IDs
        task_ids = [task.id for task in sample_tasks]
        for task_id in critical_path:
            assert task_id in task_ids

    def test_calculate_critical_path_parallel_tasks(self, timeline_generator):
        """Test critical path calculation with parallel tasks."""
        tasks = []

        # Task 1: Base task
        task1 = Task(id="T001", title="Base", description="Base task", 
                    task_type=TaskType.ANALYSIS, estimate_hours=8.0)
        tasks.append(task1)

        # Task 2: Parallel task A (depends on T001)
        task2 = Task(id="T002", title="Parallel A", description="Parallel task A", 
                    task_type=TaskType.IMPLEMENTATION, estimate_hours=16.0)
        task2.add_dependency("T001")
        tasks.append(task2)

        # Task 3: Parallel task B (depends on T001, longer duration)
        task3 = Task(id="T003", title="Parallel B", description="Parallel task B", 
                    task_type=TaskType.IMPLEMENTATION, estimate_hours=32.0)
        task3.add_dependency("T001")
        tasks.append(task3)

        # Task 4: Final task (depends on both T002 and T003)
        task4 = Task(id="T004", title="Final", description="Final task", 
                    task_type=TaskType.TESTING, estimate_hours=8.0)
        task4.add_dependency("T002")
        task4.add_dependency("T003")
        tasks.append(task4)

        critical_path = timeline_generator.calculate_critical_path(tasks)

        # Critical path should include T001, T003 (longer parallel), and T004
        assert "T001" in critical_path
        assert "T003" in critical_path  # Longer parallel task
        assert "T004" in critical_path
        # T002 should not be on critical path (shorter parallel task)

    def test_apply_effort_buffers(self, timeline_generator, sample_tasks):
        """Test effort buffer application."""
        buffered_tasks = timeline_generator._apply_effort_buffers(sample_tasks)

        assert len(buffered_tasks) == len(sample_tasks)

        # Check that estimates are increased
        for original, buffered in zip(sample_tasks, buffered_tasks):
            assert buffered.estimate_hours >= original.estimate_hours
            # Should preserve other properties
            assert buffered.id == original.id
            assert buffered.title == original.title

    def test_schedule_tasks_sequential(self, timeline_generator, sample_tasks):
        """Test sequential task scheduling."""
        start_date = datetime(2024, 1, 1, 9, 0)

        scheduled_tasks = timeline_generator._schedule_tasks(sample_tasks, start_date)

        assert len(scheduled_tasks) == len(sample_tasks)
        # Tasks should be in dependency order
        # (This is a simplified test - full scheduling logic is complex)

    def test_calculate_end_date(self, timeline_generator, sample_tasks):
        """Test end date calculation."""
        start_date = datetime(2024, 1, 1, 9, 0)

        end_date = timeline_generator._calculate_end_date(sample_tasks, start_date)

        assert end_date > start_date
        # End date should account for total effort
        total_hours = sum(task.estimate_hours for task in sample_tasks)
        expected_days = total_hours / timeline_generator.working_hours_per_day
        actual_days = (end_date - start_date).days

        # Should be reasonably close (allowing for scheduling complexity and buffers)
        # The timeline generator applies buffers, so actual may be less than expected
        # Just verify that end date is after start date
        assert actual_days > 0  # Should have some duration

    def test_generate_milestones_by_phase(self, timeline_generator, sample_tasks):
        """Test milestone generation based on task phases."""
        start_date = datetime(2024, 1, 1, 9, 0)
        end_date = datetime(2024, 2, 1, 17, 0)

        milestones = timeline_generator._generate_milestones(sample_tasks, start_date, end_date)

        # Should generate milestones for each phase
        milestone_titles = [m.title for m in milestones]

        # Should have phase completion milestones
        assert any("Analysis" in title for title in milestone_titles)
        assert any("Design" in title or "Planning" in title for title in milestone_titles)
        assert any("Implementation" in title for title in milestone_titles)
        assert any("Testing" in title or "Verification" in title for title in milestone_titles)

        # Should have project completion milestone
        assert any("Project Complete" in title for title in milestone_titles)

    def test_calculate_timeline_confidence(self, timeline_generator, sample_tasks):
        """Test timeline confidence calculation."""
        confidence = timeline_generator._calculate_timeline_confidence(sample_tasks)

        assert 0.0 <= confidence <= 1.0

        # Test with high-risk tasks
        risky_task = Task(
            id="T999",
            title="Risky Task",
            description="A very risky task",
            task_type=TaskType.IMPLEMENTATION,
            estimate_hours=200.0  # Very large task
        )
        risky_task.add_dependency("T001")
        risky_task.add_dependency("T002")
        risky_task.add_dependency("T003")  # Many dependencies

        risky_tasks = sample_tasks + [risky_task]
        risky_confidence = timeline_generator._calculate_timeline_confidence(risky_tasks)

        # Should have lower confidence with risky tasks
        assert risky_confidence < confidence

    def test_hours_to_days_conversion(self, timeline_generator):
        """Test hours to days conversion."""
        # 8 hours = 1 day
        assert timeline_generator._hours_to_days(8.0) == 1.0

        # 40 hours = 5 days
        assert timeline_generator._hours_to_days(40.0) == 5.0

        # Partial days
        assert timeline_generator._hours_to_days(12.0) == 1.5

    def test_topological_sort(self, timeline_generator, sample_tasks):
        """Test topological sorting of tasks."""
        sorted_tasks = timeline_generator._topological_sort(sample_tasks)

        assert len(sorted_tasks) == len(sample_tasks)

        # Check that dependencies are respected
        task_positions = {task.id: i for i, task in enumerate(sorted_tasks)}

        for task in sorted_tasks:
            for dependency in task.dependencies:
                if dependency.task_id in task_positions:
                    # Dependency should come before dependent task
                    assert task_positions[dependency.task_id] < task_positions[task.id]

    def test_topological_sort_with_cycle(self, timeline_generator):
        """Test topological sort with circular dependencies."""
        tasks = []

        task1 = Task(id="T001", title="Task 1", description="Task 1", task_type=TaskType.IMPLEMENTATION)
        task2 = Task(id="T002", title="Task 2", description="Task 2", task_type=TaskType.IMPLEMENTATION)

        # Create circular dependency
        task1.add_dependency("T002")
        task2.add_dependency("T001")

        tasks = [task1, task2]

        # Should handle cycle gracefully (return original order)
        sorted_tasks = timeline_generator._topological_sort(tasks)
        assert len(sorted_tasks) == len(tasks)

    def test_calculate_earliest_times(self, timeline_generator, sample_tasks):
        """Test earliest start/finish time calculation."""
        task_lookup = {task.id: task for task in sample_tasks}

        earliest_times = timeline_generator._calculate_earliest_times(sample_tasks, task_lookup)

        # Should have times for all tasks
        assert len(earliest_times) == len(sample_tasks)

        # Check that dependent tasks start after their dependencies
        for task in sample_tasks:
            if task.dependencies:
                task_start = earliest_times[task.id]["start"]
                for dependency in task.dependencies:
                    if dependency.task_id in earliest_times:
                        dep_finish = earliest_times[dependency.task_id]["finish"]
                        assert task_start >= dep_finish

    def test_calculate_latest_times(self, timeline_generator, sample_tasks):
        """Test latest start/finish time calculation."""
        task_lookup = {task.id: task for task in sample_tasks}
        earliest_times = timeline_generator._calculate_earliest_times(sample_tasks, task_lookup)

        latest_times = timeline_generator._calculate_latest_times(sample_tasks, task_lookup, earliest_times)

        # Should have times for all tasks
        assert len(latest_times) == len(sample_tasks)

        # Latest times should be >= earliest times
        for task_id in earliest_times:
            if task_id in latest_times:
                assert latest_times[task_id]["start"] >= earliest_times[task_id]["start"]
                assert latest_times[task_id]["finish"] >= earliest_times[task_id]["finish"]

    def test_empty_task_list(self, timeline_generator):
        """Test handling of empty task list."""
        timeline = timeline_generator.generate_timeline(
            tasks=[],
            title="Empty Timeline"
        )

        assert timeline.title == "Empty Timeline"
        assert len(timeline.tasks) == 0
        assert timeline.total_estimated_hours == 0.0
        assert len(timeline.milestones) >= 0  # May have project completion milestone

    def test_single_task(self, timeline_generator):
        """Test timeline generation with single task."""
        task = Task(
            id="T001",
            title="Single Task",
            description="A single task",
            task_type=TaskType.IMPLEMENTATION,
            estimate_hours=8.0
        )

        timeline = timeline_generator.generate_timeline(
            tasks=[task],
            title="Single Task Timeline"
        )

        assert len(timeline.tasks) == 1
        assert timeline.total_estimated_hours > 8.0  # Should include buffer
        assert timeline.end_date > timeline.start_date
