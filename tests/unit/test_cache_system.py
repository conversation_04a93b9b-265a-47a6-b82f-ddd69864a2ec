"""
Comprehensive tests for the LLM response caching system.

Tests cover all cache backends, fallback mechanisms, and edge cases
to achieve 80%+ coverage for src/agents/cache.py.
"""

import asyncio
import json
import pytest
import time
from unittest.mock import AsyncMock, MagicMock, patch

from src.agents.cache import (
    CacheBackend,
    CacheEntry,
    InMemoryCache,
    LLMResponseCache,
    RedisCache,
)
from src.agents.models import LLMResponse
from src.config import Settings

# Import the module to trigger coverage
import src.agents.cache


class TestCacheEntry:
    """Test CacheEntry dataclass."""

    def test_cache_entry_creation(self):
        """Test basic cache entry creation."""
        response = LLMResponse(content="test", model="gpt-4", usage={"total_tokens": 10})
        entry = CacheEntry(response=response, created_at=time.time())

        assert entry.response == response
        assert entry.access_count == 0
        assert entry.last_accessed > 0

    def test_cache_entry_post_init(self):
        """Test post_init sets last_accessed if not provided."""
        response = LLMResponse(content="test", model="gpt-4", usage={"total_tokens": 10})
        created_time = time.time()
        entry = CacheEntry(response=response, created_at=created_time)

        assert entry.last_accessed == created_time


class TestInMemoryCache:
    """Test InMemoryCache backend."""

    @pytest.fixture
    def cache(self):
        """Create cache instance."""
        return InMemoryCache(max_size=3)

    @pytest.fixture
    def sample_entry(self):
        """Create sample cache entry."""
        response = LLMResponse(content="test response", model="gpt-4", usage={"total_tokens": 15})
        return CacheEntry(response=response, created_at=time.time())

    @pytest.mark.asyncio
    async def test_cache_miss(self, cache):
        """Test cache miss scenario."""
        result = await cache.get("nonexistent")
        assert result is None

        stats = await cache.get_stats()
        assert stats["misses"] == 1
        assert stats["hits"] == 0

    @pytest.mark.asyncio
    async def test_cache_hit(self, cache, sample_entry):
        """Test cache hit scenario."""
        await cache.set("test_key", sample_entry)

        result = await cache.get("test_key")
        assert result is not None
        assert result.response.content == "test response"
        assert result.access_count == 1

        stats = await cache.get_stats()
        assert stats["hits"] == 1
        assert stats["sets"] == 1

    @pytest.mark.asyncio
    async def test_cache_eviction_lru(self, cache, sample_entry):
        """Test LRU eviction when cache is full."""
        # Fill cache to max size
        for i in range(3):
            entry = CacheEntry(
                response=LLMResponse(content=f"content_{i}", model="gpt-4", usage={"total_tokens": 10}),
                created_at=time.time()
            )
            await cache.set(f"key_{i}", entry)

        # Access key_1 to make it more recently used
        await cache.get("key_1")

        # Add one more entry (should evict key_0, the LRU)
        new_entry = CacheEntry(
            response=LLMResponse(content="new_content", model="gpt-4", usage={"total_tokens": 10}),
            created_at=time.time()
        )
        await cache.set("key_new", new_entry)

        # key_0 should be evicted
        assert await cache.get("key_0") is None
        # key_1 and key_2 should still exist
        assert await cache.get("key_1") is not None
        assert await cache.get("key_2") is not None
        assert await cache.get("key_new") is not None

        stats = await cache.get_stats()
        assert stats["evictions"] == 1

    @pytest.mark.asyncio
    async def test_cache_delete(self, cache, sample_entry):
        """Test cache entry deletion."""
        await cache.set("test_key", sample_entry)
        assert await cache.get("test_key") is not None

        await cache.delete("test_key")
        assert await cache.get("test_key") is None

        stats = await cache.get_stats()
        assert stats["deletes"] == 1

    @pytest.mark.asyncio
    async def test_cache_clear(self, cache, sample_entry):
        """Test clearing all cache entries."""
        await cache.set("key1", sample_entry)
        await cache.set("key2", sample_entry)

        await cache.clear()

        assert await cache.get("key1") is None
        assert await cache.get("key2") is None
        assert len(cache.cache) == 0

    @pytest.mark.asyncio
    async def test_cache_stats(self, cache, sample_entry):
        """Test cache statistics calculation."""
        # Perform various operations
        await cache.set("key1", sample_entry)
        await cache.get("key1")  # hit
        await cache.get("nonexistent")  # miss
        await cache.delete("key1")

        stats = await cache.get_stats()

        assert stats["hits"] == 1
        assert stats["misses"] == 1
        assert stats["sets"] == 1
        assert stats["deletes"] == 1
        assert stats["hit_rate"] == 0.5  # 1 hit out of 2 requests
        assert stats["backend"] == "memory"

    @pytest.mark.asyncio
    async def test_ttl_cleanup(self, cache, sample_entry):
        """Test TTL-based cleanup."""
        # Set entry with very short TTL
        await cache.set("test_key", sample_entry, ttl=0.1)

        # Should exist immediately
        assert await cache.get("test_key") is not None

        # Wait for TTL to expire
        await asyncio.sleep(0.2)

        # Should be cleaned up
        assert await cache.get("test_key") is None

    @pytest.mark.asyncio
    async def test_evict_lru_empty_cache(self, cache):
        """Test LRU eviction on empty cache."""
        # Should not raise error
        await cache._evict_lru()
        assert len(cache.cache) == 0


@pytest.mark.skip(reason="Redis not installed in test environment")
class TestRedisCache:
    """Test RedisCache backend."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = MagicMock(spec=Settings)
        settings.redis_url = "redis://localhost:6379"
        settings.redis_ttl = 3600
        return settings

    @pytest.fixture
    def sample_entry(self):
        """Create sample cache entry."""
        response = LLMResponse(content="test response", model="gpt-4", usage={"total_tokens": 15})
        return CacheEntry(response=response, created_at=time.time())

    @pytest.mark.asyncio
    async def test_redis_cache_initialization(self, mock_settings):
        """Test Redis cache initialization."""
        cache = RedisCache(mock_settings)
        assert cache.settings == mock_settings
        assert cache.redis is None  # Lazy initialization

    @pytest.mark.asyncio
    async def test_redis_get_miss(self, mock_settings):
        """Test Redis cache miss."""
        with patch('redis.asyncio.Redis') as mock_redis_class:
            mock_redis = AsyncMock()
            mock_redis.get.return_value = None
            mock_redis.ping.return_value = True
            mock_redis_class.return_value = mock_redis

            cache = RedisCache(mock_settings)
            result = await cache.get("nonexistent")

            assert result is None
            mock_redis.get.assert_called_once_with("llm_cache:nonexistent")

    @pytest.mark.asyncio
    async def test_redis_get_hit(self, mock_settings, sample_entry):
        """Test Redis cache hit."""
        with patch('redis.asyncio.Redis') as mock_redis_class:
            mock_redis = AsyncMock()
            mock_redis.ping.return_value = True

            # Mock Redis response
            cached_data = {
                "response": {
                    "content": "test response",
                    "model": "gpt-4",
                    "usage": {"total_tokens": 15},
                    "processing_time": 0.0,
                    "metadata": {}
                },
                "created_at": time.time(),
                "access_count": 0,
                "last_accessed": time.time()
            }
            mock_redis.get.return_value = json.dumps(cached_data)
            mock_redis_class.return_value = mock_redis

            cache = RedisCache(mock_settings)
            result = await cache.get("test_key")

            assert result is not None
            assert result.response.content == "test response"
            assert result.access_count == 1  # Should be incremented

    @pytest.mark.asyncio
    async def test_redis_set(self, mock_settings, sample_entry):
        """Test Redis cache set."""
        with patch('redis.asyncio.Redis') as mock_redis_class:
            mock_redis = AsyncMock()
            mock_redis.ping.return_value = True
            mock_redis_class.return_value = mock_redis

            cache = RedisCache(mock_settings)
            await cache.set("test_key", sample_entry, ttl=3600)

            mock_redis.setex.assert_called_once()
            args = mock_redis.setex.call_args
            assert args[0][0] == "llm_cache:test_key"
            assert args[0][1] == 3600

    @pytest.mark.asyncio
    async def test_redis_error_handling(self, mock_settings):
        """Test Redis error handling."""
        with patch('redis.asyncio.Redis') as mock_redis_class:
            mock_redis = AsyncMock()
            mock_redis.ping.return_value = True
            mock_redis.get.side_effect = Exception("Redis connection error")
            mock_redis_class.return_value = mock_redis

            cache = RedisCache(mock_settings)
            result = await cache.get("test_key")

            # Should return None on error and increment error stats
            assert result is None
            assert cache.stats["errors"] == 1


class TestLLMResponseCache:
    """Test main LLMResponseCache interface."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = MagicMock(spec=Settings)
        settings.enable_llm_cache = True
        settings.redis_host = "localhost"
        settings.redis_port = 6379
        settings.redis_db = 0
        settings.redis_password = None
        settings.redis_ttl = 3600
        settings.openai_model = "gpt-4"
        settings.openai_temperature = 0.7
        settings.openai_max_tokens = 1000
        return settings

    @pytest.fixture
    def disabled_settings(self):
        """Create settings with cache disabled."""
        settings = MagicMock(spec=Settings)
        settings.enable_llm_cache = False
        return settings

    def test_cache_disabled(self, disabled_settings):
        """Test cache behavior when disabled."""
        cache = LLMResponseCache(disabled_settings)
        assert not cache.enabled
        assert cache.primary_backend is None

    @pytest.mark.asyncio
    async def test_cache_key_generation(self, mock_settings):
        """Test cache key generation."""
        with patch.object(LLMResponseCache, '_initialize_backends'):
            cache = LLMResponseCache(mock_settings)

            key1 = cache._generate_cache_key("test prompt", "system prompt")
            key2 = cache._generate_cache_key("test prompt", "system prompt")
            key3 = cache._generate_cache_key("different prompt", "system prompt")

            # Same inputs should generate same key
            assert key1 == key2
            # Different inputs should generate different keys
            assert key1 != key3

    @pytest.mark.asyncio
    async def test_cache_disabled_operations(self, disabled_settings):
        """Test that operations are no-ops when cache is disabled."""
        cache = LLMResponseCache(disabled_settings)

        # Should return None for get
        result = await cache.get("test prompt")
        assert result is None

        # Should not raise error for set
        response = LLMResponse(content="test", model="gpt-4", usage={"total_tokens": 10})
        await cache.set(response, "test prompt")  # Should not raise

    @pytest.mark.asyncio
    async def test_fallback_mechanism(self, mock_settings):
        """Test fallback to memory cache when Redis fails."""
        with patch('src.agents.cache.RedisCache') as mock_redis_class:
            # Make Redis initialization fail
            mock_redis_class.side_effect = Exception("Redis unavailable")

            cache = LLMResponseCache(mock_settings)

            # Should fall back to memory cache
            assert cache.primary_backend is None
            assert cache.fallback_backend is not None

            # Operations should still work via fallback
            response = LLMResponse(content="test", model="gpt-4", usage={"total_tokens": 10})
            await cache.set(response, "test prompt")

            result = await cache.get("test prompt")
            assert result is not None
            assert result.content == "test"
