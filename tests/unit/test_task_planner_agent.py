"""
Unit tests for Task Planner Agent.
"""

import json
import pytest
from unittest.mock import Async<PERSON>ock, <PERSON><PERSON>, patch

from src.agents.base import <PERSON><PERSON><PERSON>x<PERSON>, AgentType
from src.agents.exceptions import AgentError
from src.agents.llm_client import LLMClient, LLMResponse
from src.config import Settings
from src.ingestion.integration_example import IntegratedIngestionPipeline
from src.task_planner.agent import TaskPlannerAgent


class TestTaskPlannerAgent:
    """Test TaskPlannerAgent functionality."""

    @pytest.fixture
    def mock_llm_client(self):
        """Create a mock LLM client."""
        client = Mock(spec=LLMClient)
        client.generate_response = AsyncMock(return_value=LLMResponse(
            content="Generated task planning response",
            usage={"prompt_tokens": 100, "completion_tokens": 200, "total_tokens": 300},
            model="test-model",
            processing_time=0.5,
            metadata={}
        ))
        return client

    @pytest.fixture
    def mock_ingestion_pipeline(self):
        """Create a mock ingestion pipeline."""
        pipeline = Mock(spec=IntegratedIngestionPipeline)

        # Mock search results
        mock_chunk = Mock()
        mock_chunk.content = "Sample code content for testing"
        mock_chunk.file_metadata.file_path = "src/test_file.py"

        mock_embedded_chunk = Mock()
        mock_embedded_chunk.chunk = mock_chunk

        mock_result = Mock()
        mock_result.chunk = mock_chunk
        mock_result.similarity_score = 0.8

        pipeline.search_content = AsyncMock(return_value=[mock_result])
        return pipeline

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings."""
        settings = Mock(spec=Settings)
        settings.task_planner = {
            "working_hours_per_day": 8.0,
            "working_days_per_week": 5,
            "default_buffer_factor": 1.3,
            "max_task_complexity_hours": 80.0,
            "enable_risk_analysis": True,
            "enable_dependency_optimization": True,
        }
        return settings

    @pytest.fixture
    def agent(self, mock_llm_client, mock_ingestion_pipeline, mock_settings):
        """Create a TaskPlannerAgent for testing."""
        return TaskPlannerAgent(
            llm_client=mock_llm_client,
            ingestion_pipeline=mock_ingestion_pipeline,
            settings=mock_settings,
        )

    @pytest.fixture
    def sample_context(self):
        """Create sample agent context."""
        from src.agents.base import ConversationContext

        conversation_context = ConversationContext(
            session_id="test-conv-123",
            user_id="test-user",
        )

        return AgentContext(
            conversation_context=conversation_context,
            repository_context=None,
            search_results=[],
        )

    def test_initialization(self, agent):
        """Test agent initialization."""
        assert agent.agent_type == AgentType.TASK_PLANNER
        assert agent.breakdown_engine is not None
        assert agent.timeline_generator is not None
        assert agent.dependency_analyzer is not None
        assert agent.risk_analyzer is not None

    def test_can_handle_query_high_confidence(self, agent, sample_context):
        """Test query handling confidence for task planning queries."""
        high_confidence_queries = [
            "Break down this requirement into tasks",
            "Create a project plan for implementing user authentication",
            "Generate a task breakdown structure",
            "Plan the implementation roadmap",
            "Estimate effort for this project",
        ]

        for query in high_confidence_queries:
            confidence = agent.can_handle_query(query, sample_context)
            assert confidence >= 0.5, f"Query '{query}' should have high confidence"

    def test_can_handle_query_medium_confidence(self, agent, sample_context):
        """Test query handling confidence for medium relevance queries."""
        medium_confidence_queries = [
            "How long will this take?",
            "What's the timeline for this feature?",
            "Organize the work for this project",
            "What are the deliverables?",
        ]

        for query in medium_confidence_queries:
            confidence = agent.can_handle_query(query, sample_context)
            assert 0.2 <= confidence < 0.7, f"Query '{query}' should have medium confidence"

    def test_can_handle_query_low_confidence(self, agent, sample_context):
        """Test query handling confidence for low relevance queries."""
        low_confidence_queries = [
            "What's the weather like?",
            "How do I fix this bug?",
            "Explain this code to me",
            "What's the meaning of life?",
        ]

        for query in low_confidence_queries:
            confidence = agent.can_handle_query(query, sample_context)
            assert confidence < 0.3, f"Query '{query}' should have low confidence"

    def test_classify_query_type_breakdown(self, agent):
        """Test query type classification for breakdown queries."""
        breakdown_queries = [
            "Break down this requirement",
            "Decompose this into tasks",
            "Split this into smaller tasks",
            "Create a task list",
        ]

        for query in breakdown_queries:
            query_type = agent._classify_query_type(query)
            # Some queries might be classified as "planning" which is also acceptable
            assert query_type in ["breakdown", "planning"], f"Query '{query}' classified as '{query_type}'"

    def test_classify_query_type_timeline(self, agent):
        """Test query type classification for timeline queries."""
        timeline_queries = [
            "What's the timeline for this?",
            "How long will this take?",
            "Create a schedule",
            "When will this be done?",
            "Estimate the duration",
        ]

        for query in timeline_queries:
            query_type = agent._classify_query_type(query)
            assert query_type == "timeline"

    def test_classify_query_type_planning(self, agent):
        """Test query type classification for planning queries."""
        planning_queries = [
            "Create a project plan",
            "Plan the implementation",
            "Generate a roadmap",
            "Planning for this feature",
        ]

        for query in planning_queries:
            query_type = agent._classify_query_type(query)
            assert query_type == "planning"

    def test_classify_query_type_dependencies(self, agent):
        """Test query type classification for dependency queries."""
        dependency_queries = [
            "What are the dependencies?",
            "Analyze task dependencies",
            "What depends on what?",
            "Show the dependency order",
        ]

        for query in dependency_queries:
            query_type = agent._classify_query_type(query)
            assert query_type == "dependencies"

    def test_classify_query_type_risks(self, agent):
        """Test query type classification for risk queries."""
        risk_queries = [
            "What are the risks?",
            "Identify potential issues",
            "Risk assessment for this project",
            "What challenges might we face?",
        ]

        for query in risk_queries:
            query_type = agent._classify_query_type(query)
            assert query_type == "risks"

    @pytest.mark.asyncio
    async def test_process_query_breakdown(self, agent, sample_context, mock_llm_client):
        """Test processing breakdown query."""
        query = "Break down the user authentication requirement into tasks"

        response = await agent.process_query(query, sample_context)

        assert response.agent_type == AgentType.TASK_PLANNER
        assert response.content is not None
        assert response.confidence > 0.0
        assert response.metadata["query_type"] == "breakdown"
        assert "task planning" in response.content.lower() or "breakdown" in response.content.lower()

        # Should have called LLM
        mock_llm_client.generate_response.assert_called_once()

    @pytest.mark.asyncio
    async def test_process_query_timeline(self, agent, sample_context, mock_llm_client):
        """Test processing timeline query."""
        query = "What's the timeline for implementing user authentication?"

        response = await agent.process_query(query, sample_context)

        assert response.agent_type == AgentType.TASK_PLANNER
        assert response.metadata["query_type"] == "timeline"
        assert "timeline" in response.content.lower() or "duration" in response.content.lower()

    @pytest.mark.asyncio
    async def test_process_query_comprehensive_plan(self, agent, sample_context, mock_llm_client):
        """Test processing comprehensive planning query."""
        query = "Create a comprehensive project plan for user authentication"

        response = await agent.process_query(query, sample_context)

        assert response.agent_type == AgentType.TASK_PLANNER
        assert response.metadata["query_type"] == "planning"
        assert "project plan" in response.content.lower() or "comprehensive" in response.content.lower()

    @pytest.mark.asyncio
    async def test_process_query_dependencies(self, agent, sample_context, mock_llm_client):
        """Test processing dependency analysis query."""
        query = "Analyze the dependencies for this implementation"

        response = await agent.process_query(query, sample_context)

        assert response.agent_type == AgentType.TASK_PLANNER
        assert response.metadata["query_type"] == "dependencies"
        assert "dependencies" in response.content.lower() or "dependency" in response.content.lower()

    @pytest.mark.asyncio
    async def test_process_query_risks(self, agent, sample_context, mock_llm_client):
        """Test processing risk assessment query."""
        query = "What are the risks for this project?"

        response = await agent.process_query(query, sample_context)

        assert response.agent_type == AgentType.TASK_PLANNER
        assert response.metadata["query_type"] == "risks"
        assert "risk" in response.content.lower() or "assessment" in response.content.lower()

    @pytest.mark.asyncio
    async def test_process_query_with_search_results(self, agent, mock_ingestion_pipeline):
        """Test processing query with search results."""
        query = "Plan the implementation of user authentication"
        from src.agents.base import ConversationContext

        conversation_context = ConversationContext(
            session_id="test-conv-123",
            user_id="test-user",
        )

        context = AgentContext(
            conversation_context=conversation_context,
            repository_context=None,
            search_results=[],  # Empty, should trigger search
        )

        response = await agent.process_query(query, context)

        # Should have performed search
        mock_ingestion_pipeline.search_content.assert_called_once()
        assert response.sources is not None

    @pytest.mark.asyncio
    async def test_process_query_with_existing_search_results(self, agent, mock_ingestion_pipeline):
        """Test processing query with existing search results."""
        # Mock existing search results
        mock_chunk = Mock()
        mock_chunk.content = "Existing search content"
        mock_chunk.file_metadata.file_path = "src/existing.py"

        mock_result = Mock()
        mock_result.chunk = mock_chunk

        from src.agents.base import ConversationContext

        conversation_context = ConversationContext(
            session_id="test-conv-123",
            user_id="test-user",
        )

        context = AgentContext(
            conversation_context=conversation_context,
            repository_context=None,
            search_results=[mock_result],
        )

        query = "Plan the implementation"
        response = await agent.process_query(query, context)

        # Should not have performed new search
        mock_ingestion_pipeline.search_content.assert_not_called()
        assert response.sources is not None

    def test_extract_requirements_simple(self, agent):
        """Test requirement extraction from simple query."""
        query = "Implement user authentication system"
        search_results = []

        requirements = agent._extract_requirements(query, search_results)

        assert len(requirements) >= 1
        assert query in requirements

    def test_extract_requirements_with_search_results(self, agent):
        """Test requirement extraction with search results."""
        query = "Implement user authentication"

        # Mock search results with requirement-like content
        mock_chunk = Mock()
        mock_chunk.content = "The system must authenticate users. Should validate credentials."

        mock_result = Mock()
        mock_result.chunk = mock_chunk

        search_results = [mock_result]

        requirements = agent._extract_requirements(query, search_results)

        assert len(requirements) >= 1
        assert query in requirements

    def test_format_tasks_for_prompt(self, agent):
        """Test task formatting for LLM prompt."""
        from src.task_planner.models import Task, TaskType, TaskPriority

        tasks = [
            Task(
                id="T001",
                title="Test Task 1",
                description="A test task for formatting",
                task_type=TaskType.IMPLEMENTATION,
                priority=TaskPriority.HIGH,
                estimate_hours=8.0,
            ),
            Task(
                id="T002",
                title="Test Task 2",
                description="Another test task",
                task_type=TaskType.TESTING,
                priority=TaskPriority.MEDIUM,
                estimate_hours=4.0,
            ),
        ]

        formatted = agent._format_tasks_for_prompt(tasks)

        assert "T001" in formatted
        assert "Test Task 1" in formatted
        assert "implementation" in formatted
        assert "high" in formatted
        assert "8.0 hours" in formatted

    def test_format_search_results_for_prompt(self, agent):
        """Test search result formatting for LLM prompt."""
        mock_chunk = Mock()
        mock_chunk.content = "Sample code content for testing purposes"
        mock_chunk.file_metadata.file_path = "src/test_file.py"

        mock_result = Mock()
        mock_result.chunk = mock_chunk

        search_results = [mock_result]

        formatted = agent._format_search_results_for_prompt(search_results)

        assert "src/test_file.py" in formatted
        assert "Sample code content" in formatted

    def test_format_search_results_empty(self, agent):
        """Test search result formatting with empty results."""
        formatted = agent._format_search_results_for_prompt([])

        assert "No relevant context found" in formatted

    def test_extract_sources(self, agent):
        """Test source extraction from search results."""
        mock_chunk = Mock()
        mock_chunk.file_metadata.file_path = "src/test_file.py"

        mock_result = Mock()
        mock_result.chunk = mock_chunk

        search_results = [mock_result]

        sources = agent._extract_sources(search_results)

        assert len(sources) == 1
        assert "src/test_file.py" in sources

    def test_extract_sources_with_error(self, agent):
        """Test source extraction with malformed search results."""
        mock_result = Mock()
        # Missing chunk attribute to trigger error
        del mock_result.chunk

        search_results = [mock_result]

        sources = agent._extract_sources(search_results)

        assert len(sources) == 1
        assert "Unknown file" in sources

    @pytest.mark.asyncio
    async def test_process_query_error_handling(self, agent, sample_context, mock_llm_client):
        """Test error handling in query processing."""
        # Make LLM client raise an exception
        mock_llm_client.generate_response.side_effect = Exception("LLM error")

        query = "Plan the implementation"

        with pytest.raises(AgentError) as exc_info:
            await agent.process_query(query, sample_context)

        assert "Failed to process task planning query" in str(exc_info.value)
        assert exc_info.value.agent_type == AgentType.TASK_PLANNER.value

    @pytest.mark.asyncio
    async def test_perform_search_with_context(self, agent, mock_ingestion_pipeline):
        """Test search performance with context."""
        from src.agents.base import ConversationContext

        conversation_context = ConversationContext(
            session_id="test-conv-123",
            user_id="test-user",
        )

        context = AgentContext(
            conversation_context=conversation_context,
            repository_context=None,
            search_results=[],
        )

        query = "test query"
        results = await agent._perform_search(query, context)

        mock_ingestion_pipeline.search_content.assert_called_once_with(
            query=query,
            limit=20,
            similarity_threshold=0.3,
        )
        assert len(results) > 0

    @pytest.mark.asyncio
    async def test_perform_search_error_handling(self, agent, mock_ingestion_pipeline):
        """Test search error handling."""
        # Make search raise an exception
        mock_ingestion_pipeline.search_content.side_effect = Exception("Search error")

        from src.agents.base import ConversationContext

        conversation_context = ConversationContext(
            session_id="test-conv-123",
            user_id="test-user",
        )

        context = AgentContext(
            conversation_context=conversation_context,
            repository_context=None,
            search_results=[],
        )

        query = "test query"
        results = await agent._perform_search(query, context)

        # Should return empty list on error
        assert results == []

    def test_query_patterns_coverage(self, agent):
        """Test that query patterns cover expected categories."""
        patterns = agent.query_patterns

        expected_categories = ["breakdown", "timeline", "planning", "dependencies", "risks"]

        for category in expected_categories:
            assert category in patterns
            assert len(patterns[category]) > 0

    @pytest.mark.asyncio
    async def test_response_includes_structured_data(self, agent, sample_context):
        """Test that responses include structured JSON data."""
        query = "Break down user authentication into tasks"

        response = await agent.process_query(query, sample_context)

        # Response should contain JSON structure
        assert "```json" in response.content

        # Should be valid JSON
        json_start = response.content.find("```json") + 7
        json_end = response.content.find("```", json_start)
        json_content = response.content[json_start:json_end].strip()

        try:
            parsed_json = json.loads(json_content)
            assert "title" in parsed_json
            assert "tasks" in parsed_json
            assert "timeline" in parsed_json
        except json.JSONDecodeError:
            pytest.fail("Response should contain valid JSON structure")

    def test_agent_statistics_tracking(self, agent):
        """Test that agent tracks statistics properly."""
        # Initial stats should be zero
        stats = agent.get_stats()
        initial_queries = stats.get("total_queries", 0)

        # Stats should be tracked (this is tested in the base agent class)
        assert isinstance(stats, dict)

    def test_extract_requirements_comprehensive(self, agent):
        """Test requirement extraction with various scenarios."""
        # Test with simple query
        simple_query = "Create user authentication"
        simple_results = []
        requirements = agent._extract_requirements(simple_query, simple_results)
        assert len(requirements) > 0
        assert any("authentication" in req.lower() for req in requirements)

        # Test with complex query
        complex_query = "Build a complete user management system with authentication, authorization, profile management, and password reset functionality"
        requirements = agent._extract_requirements(complex_query, simple_results)
        assert len(requirements) >= 1  # Should extract at least one requirement
        assert any("authentication" in req.lower() for req in requirements)

        # Test with search results containing requirements
        mock_chunk = Mock()
        mock_chunk.content = "The system must support user registration, login, and logout functionality. It should also handle password reset and email verification."
        mock_chunk.file_metadata.file_path = "requirements.md"

        mock_result = Mock()
        mock_result.chunk = mock_chunk

        search_results = [mock_result]
        requirements = agent._extract_requirements("user system", search_results)
        assert len(requirements) > 0

    def test_format_tasks_for_prompt(self, agent):
        """Test task formatting for LLM prompts."""
        from src.task_planner.models import Task, TaskPriority, TaskStatus, TaskType

        # Create sample tasks
        task1 = Task(
            id="task-1",
            title="Implement authentication",
            description="Create user authentication system with JWT tokens and secure password handling",
            task_type=TaskType.IMPLEMENTATION,
            priority=TaskPriority.HIGH,
            status=TaskStatus.NOT_STARTED,
            estimate_hours=16.0,
        )

        task2 = Task(
            id="task-2",
            title="Create user interface",
            description="Design and implement user registration and login forms",
            task_type=TaskType.IMPLEMENTATION,
            priority=TaskPriority.MEDIUM,
            status=TaskStatus.NOT_STARTED,
            estimate_hours=12.0,
        )

        tasks = [task1, task2]
        formatted = agent._format_tasks_for_prompt(tasks)

        assert "task-1" in formatted
        assert "Implement authentication" in formatted
        assert "task-2" in formatted
        assert "Create user interface" in formatted
        assert "high" in formatted
        assert "medium" in formatted

    @pytest.mark.asyncio
    async def test_generate_timeline_response(self, agent):
        """Test timeline response generation."""
        query = "What's the timeline for user authentication?"
        requirements = ["Implement user login", "Create password reset"]
        search_results = []

        response = await agent._generate_timeline_response(query, requirements, search_results)

        assert isinstance(response, str)
        assert len(response) > 0
        assert "timeline" in response.lower() or "schedule" in response.lower()

    @pytest.mark.asyncio
    async def test_generate_dependency_response(self, agent):
        """Test dependency analysis response generation."""
        query = "What are the dependencies for user management?"
        requirements = ["Create user model", "Implement authentication", "Add user interface"]
        search_results = []

        response = await agent._generate_dependency_response(query, requirements, search_results)

        assert isinstance(response, str)
        assert len(response) > 0
        assert "dependencies" in response.lower() or "dependency" in response.lower()

    @pytest.mark.asyncio
    async def test_generate_risk_response(self, agent):
        """Test risk assessment response generation."""
        query = "What are the risks for this project?"
        requirements = ["Implement complex authentication", "Integrate with external API"]
        search_results = []

        response = await agent._generate_risk_response(query, requirements, search_results)

        assert isinstance(response, str)
        assert len(response) > 0
        assert "risk" in response.lower()

    @pytest.mark.asyncio
    async def test_generate_general_planning_response(self, agent):
        """Test general planning response generation."""
        query = "Plan the implementation"
        requirements = ["Create user system", "Add authentication"]
        search_results = []

        response = await agent._generate_general_planning_response(query, requirements, search_results)

        assert isinstance(response, str)
        assert len(response) > 0
        # Should delegate to comprehensive plan response
        assert "plan" in response.lower() or "implementation" in response.lower()

    def test_classify_query_type_comprehensive(self, agent):
        """Test comprehensive query type classification."""
        test_cases = [
            ("Break down this feature into tasks", "breakdown"),
            ("What's the timeline for this project?", "timeline"),
            ("Create a project plan", "planning"),
            ("What are the dependencies?", "dependencies"),
            ("Analyze the risks", "risks"),
            ("Some random query", "planning"),  # Default case
        ]

        for query, expected_type in test_cases:
            result = agent._classify_query_type(query)
            assert result == expected_type, f"Query '{query}' should be classified as '{expected_type}', got '{result}'"

    def test_extract_requirements_edge_cases(self, agent):
        """Test requirement extraction edge cases."""
        # Test with empty query - the method still returns the query as a requirement
        empty_requirements = agent._extract_requirements("", [])
        assert len(empty_requirements) >= 0  # Should handle gracefully

        # Test with very short query
        short_requirements = agent._extract_requirements("test", [])
        assert len(short_requirements) >= 0  # Should handle gracefully

        # Test with query containing no actionable requirements
        vague_requirements = agent._extract_requirements("What do you think?", [])
        assert len(vague_requirements) >= 0  # Should handle gracefully

    def test_format_search_results_edge_cases(self, agent):
        """Test search result formatting edge cases."""
        # Test with empty results
        empty_formatted = agent._format_search_results_for_prompt([])
        assert "No relevant context found" in empty_formatted

        # Test with malformed results
        mock_bad_result = Mock()
        # Remove chunk attribute to trigger exception
        del mock_bad_result.chunk

        bad_results = [mock_bad_result]
        formatted = agent._format_search_results_for_prompt(bad_results)
        assert "Content unavailable" in formatted

    def test_extract_sources_edge_cases(self, agent):
        """Test source extraction edge cases."""
        # Test with empty results
        empty_sources = agent._extract_sources([])
        assert len(empty_sources) == 0

        # Test with more than 10 results (should limit)
        many_results = []
        for i in range(15):
            mock_chunk = Mock()
            mock_chunk.file_metadata.file_path = f"file_{i}.py"
            mock_result = Mock()
            mock_result.chunk = mock_chunk
            many_results.append(mock_result)

        sources = agent._extract_sources(many_results)
        assert len(sources) <= 10  # Should be limited to 10

    @pytest.mark.asyncio
    async def test_response_generators_with_requirements(self, agent):
        """Test response generators with actual requirements."""
        query = "test query"
        requirements = ["Create user authentication", "Implement user interface"]
        search_results = []

        # Test generators that can handle requirements
        safe_generators = [
            agent._generate_breakdown_response,
            agent._generate_comprehensive_plan_response,
            agent._generate_general_planning_response,
        ]

        for generator in safe_generators:
            response = await generator(query, requirements, search_results)
            assert isinstance(response, str)
            assert len(response) > 0

    @pytest.mark.asyncio
    async def test_timeline_generator_with_requirements(self, agent):
        """Test timeline generator specifically with requirements."""
        query = "What's the timeline?"
        requirements = ["Create user authentication"]
        search_results = []

        response = await agent._generate_timeline_response(query, requirements, search_results)
        assert isinstance(response, str)
        assert len(response) > 0

    @pytest.mark.asyncio
    async def test_dependency_generator_with_requirements(self, agent):
        """Test dependency generator specifically with requirements."""
        query = "What are the dependencies?"
        requirements = ["Create user model", "Implement authentication"]
        search_results = []

        response = await agent._generate_dependency_response(query, requirements, search_results)
        assert isinstance(response, str)
        assert len(response) > 0

    @pytest.mark.asyncio
    async def test_risk_generator_with_requirements(self, agent):
        """Test risk generator specifically with requirements."""
        query = "What are the risks?"
        requirements = ["Implement complex authentication"]
        search_results = []

        response = await agent._generate_risk_response(query, requirements, search_results)
        assert isinstance(response, str)
        assert len(response) > 0
