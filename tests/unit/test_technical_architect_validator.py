"""
Unit tests for Technical Architect Validator module.
"""

import pytest
from unittest.mock import Mock

from src.technical_architect.analyzer import (
    ArchitectureAnalysis,
    ArchitecturePattern,
    ComponentAnalysis,
    SOLIDPrinciple,
)
from src.technical_architect.validator import (
    SOLIDValidator,
    ValidationIssue,
    ValidationReport,
    ValidationSeverity,
)


class TestSOLIDValidator:
    """Test cases for SOLIDValidator."""

    @pytest.fixture
    def validator(self):
        """Create validator instance."""
        return SOLIDValidator()

    @pytest.fixture
    def sample_analysis(self):
        """Create sample architecture analysis."""
        return ArchitectureAnalysis(
            patterns_detected=[ArchitecturePattern.FACTORY],
            solid_compliance={
                SOLIDPrinciple.SINGLE_RESPONSIBILITY: 0.7,
                SOLIDPrinciple.OPEN_CLOSED: 0.6,
                SOLIDPrinciple.LISKOV_SUBSTITUTION: 0.8,
                SOLIDPrinciple.INTERFACE_SEGREGATION: 0.7,
                SOLIDPrinciple.DEPENDENCY_INVERSION: 0.5,
            },
            code_quality_metrics={"total_components": 5},
            confidence=0.8,
        )

    @pytest.fixture
    def sample_components(self):
        """Create sample components for testing."""
        return [
            ComponentAnalysis(
                component_name="GoodComponent",
                file_path="src/good.py",
                component_type="class",
                responsibilities=["Single responsibility"],
                dependencies=["dep1", "dep2"],
                patterns_used=[ArchitecturePattern.DEPENDENCY_INJECTION],
                solid_violations=[],
                complexity_score=3.0,
            ),
            ComponentAnalysis(
                component_name="BadComponent",
                file_path="src/bad.py",
                component_type="class",
                responsibilities=["Multiple", "Different", "Responsibilities", "Too many"],
                dependencies=["dep1", "dep2", "dep3", "dep4", "dep5", "dep6", "dep7", "dep8", "dep9"],
                patterns_used=[],
                solid_violations=["Single Responsibility Principle violation", "Open/Closed violation"],
                complexity_score=9.0,
            ),
            ComponentAnalysis(
                component_name="MediumComponent",
                file_path="src/medium.py",
                component_type="class",
                responsibilities=["Primary responsibility", "Secondary responsibility"],
                dependencies=["dep1", "dep2", "dep3"],
                patterns_used=[ArchitecturePattern.STRATEGY],
                solid_violations=[],
                complexity_score=5.0,
            ),
        ]

    def test_validate_architecture(self, validator, sample_analysis, sample_components):
        """Test architecture validation."""
        report = validator.validate_architecture(sample_analysis, sample_components)

        assert isinstance(report, ValidationReport)
        assert 0 <= report.overall_score <= 1
        assert len(report.principle_scores) == 5  # All SOLID principles
        assert isinstance(report.issues, list)
        assert isinstance(report.recommendations, list)
        assert isinstance(report.compliant_components, list)
        assert isinstance(report.non_compliant_components, list)
        assert isinstance(report.summary, dict)

    def test_validate_srp(self, validator, sample_components):
        """Test Single Responsibility Principle validation."""
        issues, score = validator._validate_srp(sample_components)

        assert isinstance(issues, list)
        assert 0 <= score <= 1

        # Should find issues with BadComponent (too many responsibilities)
        bad_component_issues = [issue for issue in issues if issue.component == "BadComponent"]
        assert len(bad_component_issues) > 0

        # Check issue properties
        for issue in bad_component_issues:
            assert issue.principle == SOLIDPrinciple.SINGLE_RESPONSIBILITY
            assert isinstance(issue.severity, ValidationSeverity)
            assert len(issue.title) > 0
            assert len(issue.description) > 0
            assert len(issue.suggestion) > 0

    def test_validate_ocp(self, validator, sample_components):
        """Test Open/Closed Principle validation."""
        issues, score = validator._validate_ocp(sample_components)

        assert isinstance(issues, list)
        assert 0 <= score <= 1

        # Should find issues with BadComponent (has OCP violation)
        bad_component_issues = [issue for issue in issues if issue.component == "BadComponent"]
        assert len(bad_component_issues) > 0

        # Check for extensibility issues
        extensibility_issues = [issue for issue in issues if "extensibility" in issue.title.lower()]
        assert len(extensibility_issues) >= 0  # May or may not have extensibility issues

    def test_validate_lsp(self, validator, sample_components):
        """Test Liskov Substitution Principle validation."""
        issues, score = validator._validate_lsp(sample_components)

        assert isinstance(issues, list)
        assert 0 <= score <= 1

        # LSP is harder to detect statically, so we expect fewer issues
        # and reasonable scores for class components
        class_components = [c for c in sample_components if c.component_type == "class"]
        assert score >= 0.6  # Should have reasonable LSP compliance

    def test_validate_isp(self, validator, sample_components):
        """Test Interface Segregation Principle validation."""
        issues, score = validator._validate_isp(sample_components)

        assert isinstance(issues, list)
        assert 0 <= score <= 1

        # Should find issues with BadComponent (too many dependencies)
        bad_component_issues = [issue for issue in issues if issue.component == "BadComponent"]
        assert len(bad_component_issues) > 0

        # Check for large interface issues
        large_interface_issues = [issue for issue in issues if "large interface" in issue.title.lower()]
        assert len(large_interface_issues) > 0

    def test_validate_dip(self, validator, sample_components):
        """Test Dependency Inversion Principle validation."""
        issues, score = validator._validate_dip(sample_components)

        assert isinstance(issues, list)
        assert 0 <= score <= 1

        # Should find issues with components lacking dependency injection
        missing_di_issues = [issue for issue in issues if "dependency injection" in issue.title.lower()]
        assert len(missing_di_issues) >= 0  # May have DI issues

        # BadComponent should have DI issues (no DI pattern)
        bad_component_issues = [issue for issue in issues if issue.component == "BadComponent"]
        assert len(bad_component_issues) > 0

    def test_analyze_method_concerns(self, validator):
        """Test method concerns analysis."""
        component = ComponentAnalysis(
            component_name="MixedComponent",
            file_path="src/mixed.py",
            component_type="class",
            responsibilities=[
                "Implements save functionality",
                "Implements calculate business logic",
                "Implements render display logic",
                "Implements send communication",
            ],
            dependencies=[],
            patterns_used=[],
            solid_violations=[],
            complexity_score=5.0,
        )

        concerns = validator._analyze_method_concerns(component)

        assert isinstance(concerns, list)
        assert len(concerns) > 0
        # Should detect multiple concerns from the responsibilities
        assert len(concerns) >= 2

    def test_generate_recommendations(self, validator):
        """Test recommendations generation."""
        report = ValidationReport(
            overall_score=0.5,
            principle_scores={
                SOLIDPrinciple.SINGLE_RESPONSIBILITY: 0.4,
                SOLIDPrinciple.OPEN_CLOSED: 0.7,
                SOLIDPrinciple.DEPENDENCY_INVERSION: 0.3,
            },
            issues=[
                ValidationIssue(
                    principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                    severity=ValidationSeverity.CRITICAL,
                    title="Critical Issue",
                    description="Critical description",
                    component="TestComponent",
                    file_path="test.py",
                ),
                ValidationIssue(
                    principle=SOLIDPrinciple.OPEN_CLOSED,
                    severity=ValidationSeverity.HIGH,
                    title="High Issue",
                    description="High description",
                    component="TestComponent",
                    file_path="test.py",
                ),
            ],
        )

        recommendations = validator._generate_recommendations(report)

        assert isinstance(recommendations, list)
        assert len(recommendations) > 0

        # Should recommend improving low-scoring principles
        srp_recommendations = [rec for rec in recommendations if "single responsibility" in rec.lower()]
        assert len(srp_recommendations) > 0

        # Should recommend addressing critical issues
        critical_recommendations = [rec for rec in recommendations if "critical" in rec.lower()]
        assert len(critical_recommendations) > 0

    def test_categorize_components(self, validator, sample_components):
        """Test component categorization."""
        issues = [
            ValidationIssue(
                principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                severity=ValidationSeverity.HIGH,
                title="Test Issue",
                description="Test description",
                component="BadComponent",
                file_path="src/bad.py",
            ),
        ]

        compliant, non_compliant = validator._categorize_components(sample_components, issues)

        assert isinstance(compliant, list)
        assert isinstance(non_compliant, list)
        assert "BadComponent" in non_compliant
        assert "GoodComponent" in compliant
        assert "MediumComponent" in compliant

    def test_generate_summary(self, validator, sample_components):
        """Test summary generation."""
        report = ValidationReport(
            overall_score=0.7,
            principle_scores={
                SOLIDPrinciple.SINGLE_RESPONSIBILITY: 0.8,
                SOLIDPrinciple.OPEN_CLOSED: 0.6,
            },
            issues=[
                ValidationIssue(
                    principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                    severity=ValidationSeverity.CRITICAL,
                    title="Critical Issue",
                    description="Critical description",
                    component="TestComponent",
                    file_path="test.py",
                ),
                ValidationIssue(
                    principle=SOLIDPrinciple.OPEN_CLOSED,
                    severity=ValidationSeverity.HIGH,
                    title="High Issue",
                    description="High description",
                    component="TestComponent",
                    file_path="test.py",
                ),
                ValidationIssue(
                    principle=SOLIDPrinciple.DEPENDENCY_INVERSION,
                    severity=ValidationSeverity.MEDIUM,
                    title="Medium Issue",
                    description="Medium description",
                    component="TestComponent",
                    file_path="test.py",
                ),
            ],
            compliant_components=["GoodComponent"],
            non_compliant_components=["BadComponent"],
        )

        summary = validator._generate_summary(report, sample_components)

        assert isinstance(summary, dict)
        assert "total_components" in summary
        assert "compliant_components" in summary
        assert "non_compliant_components" in summary
        assert "total_issues" in summary
        assert "critical_issues" in summary
        assert "high_issues" in summary
        assert "medium_issues" in summary
        assert "low_issues" in summary
        assert "overall_score" in summary
        assert "principle_scores" in summary

        # Verify counts
        assert summary["total_components"] == len(sample_components)
        assert summary["total_issues"] == 3
        assert summary["critical_issues"] == 1
        assert summary["high_issues"] == 1
        assert summary["medium_issues"] == 1
        assert summary["overall_score"] == 0.7

    def test_validation_issue_creation(self):
        """Test ValidationIssue creation."""
        issue = ValidationIssue(
            principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
            severity=ValidationSeverity.HIGH,
            title="Test Issue",
            description="Test description",
            component="TestComponent",
            file_path="test.py",
            line_number=42,
            suggestion="Test suggestion",
            code_snippet="test code",
        )

        assert issue.principle == SOLIDPrinciple.SINGLE_RESPONSIBILITY
        assert issue.severity == ValidationSeverity.HIGH
        assert issue.title == "Test Issue"
        assert issue.component == "TestComponent"
        assert issue.line_number == 42

    def test_validation_report_creation(self):
        """Test ValidationReport creation."""
        report = ValidationReport(
            overall_score=0.8,
            principle_scores={SOLIDPrinciple.SINGLE_RESPONSIBILITY: 0.9},
            issues=[],
            recommendations=["Test recommendation"],
            compliant_components=["Component1"],
            non_compliant_components=["Component2"],
            summary={"test": "value"},
        )

        assert report.overall_score == 0.8
        assert len(report.principle_scores) == 1
        assert len(report.recommendations) == 1
        assert len(report.compliant_components) == 1
        assert len(report.non_compliant_components) == 1
        assert report.summary["test"] == "value"

    def test_empty_components_validation(self, validator, sample_analysis):
        """Test validation with empty components list."""
        report = validator.validate_architecture(sample_analysis, [])

        assert isinstance(report, ValidationReport)
        assert report.overall_score >= 0.0  # Should have some score from LSP default
        assert len(report.issues) == 0
        assert len(report.compliant_components) == 0
        assert len(report.non_compliant_components) == 0

    def test_individual_principle_validators(self, validator):
        """Test individual SOLID principle validators."""
        # Create components with specific violations
        component_with_srp_violation = ComponentAnalysis(
            component_name="MultiPurposeClass",
            component_type="class",
            file_path="multi.py",
            responsibilities=["user_management", "email_sending", "data_validation", "logging"],
            dependencies=["EmailService", "Database"],
            solid_violations=[SOLIDPrinciple.SINGLE_RESPONSIBILITY],
            complexity_score=8.5,
        )

        component_with_ocp_violation = ComponentAnalysis(
            component_name="PaymentProcessor",
            component_type="class",
            file_path="payment.py",
            responsibilities=["payment_processing"],
            dependencies=[],
            solid_violations=[SOLIDPrinciple.OPEN_CLOSED],
            complexity_score=6.0,
        )

        component_with_isp_violation = ComponentAnalysis(
            component_name="LargeInterface",
            component_type="interface",
            file_path="interface.py",
            responsibilities=["data_access", "user_interface", "business_logic"],
            dependencies=[],
            solid_violations=[SOLIDPrinciple.INTERFACE_SEGREGATION],
            complexity_score=7.0,
        )

        component_with_dip_violation = ComponentAnalysis(
            component_name="OrderService",
            component_type="class",
            file_path="order.py",
            responsibilities=["order_management"],
            dependencies=["MySQLDatabase", "SMTPEmailer"],  # Concrete dependencies
            solid_violations=[SOLIDPrinciple.DEPENDENCY_INVERSION],
            complexity_score=5.0,
        )

        # Test SRP validator
        srp_issues, srp_score = validator._validate_srp([component_with_srp_violation])
        assert len(srp_issues) > 0
        assert srp_score < 0.8
        assert any(issue.principle == SOLIDPrinciple.SINGLE_RESPONSIBILITY for issue in srp_issues)

        # Test OCP validator
        ocp_issues, ocp_score = validator._validate_ocp([component_with_ocp_violation])
        assert len(ocp_issues) > 0
        assert ocp_score < 0.8
        assert any(issue.principle == SOLIDPrinciple.OPEN_CLOSED for issue in ocp_issues)

        # Test ISP validator - may not generate issues for this component type
        isp_issues, isp_score = validator._validate_isp([component_with_isp_violation])
        assert isinstance(isp_issues, list)
        assert 0.0 <= isp_score <= 1.0

        # Test DIP validator
        dip_issues, dip_score = validator._validate_dip([component_with_dip_violation])
        assert len(dip_issues) > 0
        assert dip_score < 0.8
        assert any(issue.principle == SOLIDPrinciple.DEPENDENCY_INVERSION for issue in dip_issues)

        # Test LSP validator (should have reasonable score)
        lsp_issues, lsp_score = validator._validate_lsp([component_with_srp_violation])
        assert 0.0 <= lsp_score <= 1.0  # Should be a valid score

    def test_method_concerns_analysis(self, validator):
        """Test method concerns analysis."""
        # Create component with mixed concerns
        component = ComponentAnalysis(
            component_name="MixedConcernsClass",
            component_type="class",
            file_path="mixed.py",
            responsibilities=["user_management", "email_sending", "data_validation"],
            dependencies=[],
            solid_violations=[],
            complexity_score=5.0,
        )

        concerns = validator._analyze_method_concerns(component)
        assert len(concerns) >= 1  # Should identify at least one concern
        assert isinstance(concerns, list)

    def test_recommendations_generation(self, validator, sample_analysis, sample_components):
        """Test recommendation generation."""
        # Create a report with various issues
        report = ValidationReport(
            overall_score=0.6,
            principle_scores={
                SOLIDPrinciple.SINGLE_RESPONSIBILITY: 0.4,  # Low score
                SOLIDPrinciple.OPEN_CLOSED: 0.7,
                SOLIDPrinciple.DEPENDENCY_INVERSION: 0.3,  # Low score
            },
            issues=[],
            recommendations=[],
            compliant_components=["Component1"],
            non_compliant_components=["Component2", "Component3"],  # More non-compliant
            summary={},
        )

        recommendations = validator._generate_recommendations(report)

        assert len(recommendations) > 0
        # Should have recommendations for low-scoring principles
        assert any("Single Responsibility" in rec for rec in recommendations)
        assert any("Dependency Inversion" in rec for rec in recommendations)
        # Should have recommendation for overall architecture quality
        assert any("component-level refactoring" in rec for rec in recommendations)

    def test_component_categorization(self, validator):
        """Test component categorization as compliant/non-compliant."""
        components = [
            ComponentAnalysis(
                component_name="GoodComponent",
                component_type="class",
                file_path="good.py",
                responsibilities=["single_responsibility"],
                dependencies=[],
                solid_violations=[],
                complexity_score=2.0,
            ),
            ComponentAnalysis(
                component_name="BadComponent",
                component_type="class",
                file_path="bad.py",
                responsibilities=["multiple", "responsibilities"],
                dependencies=[],
                solid_violations=[SOLIDPrinciple.SINGLE_RESPONSIBILITY],
                complexity_score=8.0,
            ),
        ]

        issues = [
            ValidationIssue(
                principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                severity=ValidationSeverity.HIGH,
                title="SRP Violation",
                description="Multiple responsibilities",
                component="BadComponent",
                file_path="bad.py",
            )
        ]

        compliant, non_compliant = validator._categorize_components(components, issues)

        assert "GoodComponent" in compliant
        assert "BadComponent" in non_compliant
        assert len(compliant) == 1
        assert len(non_compliant) == 1

    def test_summary_generation(self, validator, sample_components):
        """Test validation summary generation."""
        report = ValidationReport(
            overall_score=0.75,
            principle_scores={
                SOLIDPrinciple.SINGLE_RESPONSIBILITY: 0.8,
                SOLIDPrinciple.OPEN_CLOSED: 0.7,
            },
            issues=[
                ValidationIssue(
                    principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                    severity=ValidationSeverity.CRITICAL,
                    title="Critical Issue",
                    description="Critical description",
                    component="Component1",
                    file_path="test.py",
                ),
                ValidationIssue(
                    principle=SOLIDPrinciple.OPEN_CLOSED,
                    severity=ValidationSeverity.HIGH,
                    title="High Issue",
                    description="High description",
                    component="Component2",
                    file_path="test2.py",
                ),
                ValidationIssue(
                    principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                    severity=ValidationSeverity.MEDIUM,
                    title="Medium Issue",
                    description="Medium description",
                    component="Component1",
                    file_path="test.py",
                ),
            ],
            recommendations=["Test recommendation"],
            compliant_components=["Component3"],
            non_compliant_components=["Component1", "Component2"],
            summary={},
        )

        summary = validator._generate_summary(report, sample_components)

        # Verify all expected fields are present
        expected_fields = [
            "total_components", "compliant_components", "non_compliant_components",
            "total_issues", "critical_issues", "high_issues", "medium_issues", "low_issues",
            "overall_score", "principle_scores"
        ]

        for field in expected_fields:
            assert field in summary

        # Verify counts
        assert summary["total_components"] == len(sample_components)
        assert summary["total_issues"] == 3
        assert summary["critical_issues"] == 1
        assert summary["high_issues"] == 1
        assert summary["medium_issues"] == 1
        assert summary["low_issues"] == 0
        assert summary["overall_score"] == 0.75

    def test_validation_with_no_violations(self, validator, sample_analysis):
        """Test validation with components that have no violations."""
        clean_components = [
            ComponentAnalysis(
                component_name="CleanComponent",
                component_type="class",
                file_path="clean.py",
                responsibilities=["single_responsibility"],
                dependencies=["AbstractInterface"],
                solid_violations=[],
                complexity_score=1.0,
            )
        ]

        report = validator.validate_architecture(sample_analysis, clean_components)

        assert isinstance(report, ValidationReport)
        assert report.overall_score > 0.7  # Should have reasonably high score
        # Even clean components might have some issues detected by the validator
        assert len(report.issues) >= 0  # Should handle gracefully
        assert len(report.compliant_components) >= 0
        assert len(report.non_compliant_components) >= 0

    def test_validation_issue_severity_levels(self):
        """Test all validation issue severity levels."""
        severities = [
            ValidationSeverity.CRITICAL,
            ValidationSeverity.HIGH,
            ValidationSeverity.MEDIUM,
            ValidationSeverity.LOW,
        ]

        for severity in severities:
            issue = ValidationIssue(
                principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                severity=severity,
                title=f"{severity.value} Issue",
                description=f"Test {severity.value} issue",
                component="TestComponent",
                file_path="test.py",
            )
            assert issue.severity == severity

    def test_edge_case_empty_methods_and_responsibilities(self, validator):
        """Test validation with components that have empty methods/responsibilities."""
        empty_component = ComponentAnalysis(
            component_name="EmptyComponent",
            component_type="class",
            file_path="empty.py",
            responsibilities=[],  # Empty responsibilities
            dependencies=[],
            solid_violations=[],
            complexity_score=0.0,
        )

        # Test individual validators with empty component
        srp_issues, srp_score = validator._validate_srp([empty_component])
        ocp_issues, ocp_score = validator._validate_ocp([empty_component])
        isp_issues, isp_score = validator._validate_isp([empty_component])
        dip_issues, dip_score = validator._validate_dip([empty_component])
        lsp_issues, lsp_score = validator._validate_lsp([empty_component])

        # Should handle empty components gracefully
        assert isinstance(srp_issues, list)
        assert isinstance(ocp_issues, list)
        assert isinstance(isp_issues, list)
        assert isinstance(dip_issues, list)
        assert isinstance(lsp_issues, list)

        # Scores should be reasonable
        assert 0.0 <= srp_score <= 1.0
        assert 0.0 <= ocp_score <= 1.0
        assert 0.0 <= isp_score <= 1.0
        assert 0.0 <= dip_score <= 1.0
        assert 0.0 <= lsp_score <= 1.0
