"""
Performance tests for the multi-agent system.

This module tests response times and performance characteristics:
- Query processing response times
- Agent routing performance
- Memory usage under load
- Concurrent request handling
"""

import asyncio
import time
import pytest
import pytest_asyncio
from statistics import mean, median
from unittest.mock import AsyncMock, MagicMock

from src.agents import AgentFactory, AgentRegistry, AgentType, AgentContext, ConversationContext
from src.config import get_settings


class TestResponseTimePerformance:
    """Test response time performance requirements."""

    @pytest_asyncio.fixture
    async def agent_factory(self):
        """Create agent factory for performance testing."""
        settings = get_settings()
        factory = AgentFactory(settings)
        await factory.initialize_shared_services()

        yield factory

        await factory.shutdown_shared_services()

    @pytest.mark.asyncio
    async def test_simple_query_response_time(self, agent_factory):
        """Test that simple queries respond within 3 seconds."""
        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)

        # Create context
        session = await agent_factory.context_manager.create_session(user_id="perf-test")
        agent_context = AgentContext(conversation_context=session)

        # Test simple factual query
        query = "What is OAuth authentication?"

        start_time = time.time()
        response = await orchestrator.process_query(query, agent_context)
        end_time = time.time()

        response_time = end_time - start_time

        # Verify response quality
        assert response is not None
        assert len(response.content) > 0
        assert response.confidence > 0.0

        # Verify response time requirement
        assert response_time < 3.0, f"Response time {response_time:.2f}s exceeds 3s requirement"

        print(f"Simple query response time: {response_time:.2f}s")

    @pytest.mark.asyncio
    async def test_complex_query_response_time(self, agent_factory):
        """Test that complex queries respond within reasonable time."""
        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)

        # Create context
        session = await agent_factory.context_manager.create_session(user_id="perf-test")
        agent_context = AgentContext(conversation_context=session)

        # Test complex architectural query
        query = "Design a microservices architecture for a high-traffic e-commerce platform with real-time inventory management"

        start_time = time.time()
        response = await orchestrator.process_query(query, agent_context)
        end_time = time.time()

        response_time = end_time - start_time

        # Verify response quality
        assert response is not None
        assert len(response.content) > 0
        assert response.confidence > 0.0

        # Complex queries should still be reasonable (under 10s)
        assert response_time < 10.0, f"Complex query response time {response_time:.2f}s exceeds 10s limit"

        print(f"Complex query response time: {response_time:.2f}s")

    @pytest.mark.asyncio
    async def test_multiple_queries_performance(self, agent_factory):
        """Test performance with multiple sequential queries."""
        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)

        # Create context
        session = await agent_factory.context_manager.create_session(user_id="perf-test")
        agent_context = AgentContext(conversation_context=session)

        # Test queries of different types
        test_queries = [
            "What is REST API?",
            "How to implement authentication?",
            "Design a user management system",
            "Break down the login feature into tasks",
            "What are microservices?",
        ]

        response_times = []

        for query in test_queries:
            start_time = time.time()
            response = await orchestrator.process_query(query, agent_context)
            end_time = time.time()

            response_time = end_time - start_time
            response_times.append(response_time)

            # Verify response quality
            assert response is not None
            assert len(response.content) > 0

            # Update context for next query
            updated_session = await agent_factory.context_manager.get_context(session.session_id)
            agent_context.conversation_context = updated_session

        # Analyze performance
        avg_response_time = mean(response_times)
        median_response_time = median(response_times)
        max_response_time = max(response_times)

        print(f"Multiple queries performance:")
        print(f"  Average: {avg_response_time:.2f}s")
        print(f"  Median: {median_response_time:.2f}s")
        print(f"  Max: {max_response_time:.2f}s")

        # Performance requirements
        assert avg_response_time < 5.0, f"Average response time {avg_response_time:.2f}s exceeds 5s"
        assert max_response_time < 10.0, f"Max response time {max_response_time:.2f}s exceeds 10s"

    @pytest.mark.asyncio
    async def test_concurrent_queries_performance(self, agent_factory):
        """Test performance with concurrent queries."""
        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)

        # Create multiple sessions for concurrent testing
        sessions = []
        for i in range(3):  # Test with 3 concurrent sessions
            session = await agent_factory.context_manager.create_session(user_id=f"perf-test-{i}")
            sessions.append(session)

        async def process_query_with_timing(query, session_id):
            """Process a query and return timing info."""
            session = next(s for s in sessions if s.session_id == session_id)
            agent_context = AgentContext(conversation_context=session)

            start_time = time.time()
            response = await orchestrator.process_query(query, agent_context)
            end_time = time.time()

            return {
                "query": query,
                "response_time": end_time - start_time,
                "response": response,
                "session_id": session_id,
            }

        # Concurrent queries
        concurrent_tasks = [
            process_query_with_timing("What is OAuth?", sessions[0].session_id),
            process_query_with_timing("Design a REST API", sessions[1].session_id),
            process_query_with_timing("Plan user authentication", sessions[2].session_id),
        ]

        # Execute concurrently
        start_time = time.time()
        results = await asyncio.gather(*concurrent_tasks)
        total_time = time.time() - start_time

        # Verify all responses
        for result in results:
            assert result["response"] is not None
            assert len(result["response"].content) > 0
            assert result["response_time"] < 10.0  # Individual query limit

        # Verify concurrent performance
        max_individual_time = max(r["response_time"] for r in results)

        print(f"Concurrent queries performance:")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Max individual: {max_individual_time:.2f}s")
        for result in results:
            print(f"  {result['query'][:30]}...: {result['response_time']:.2f}s")

        # Concurrent execution should be faster than sequential
        # (allowing some overhead for coordination)
        assert total_time < sum(r["response_time"] for r in results) + 2.0

    @pytest.mark.asyncio
    async def test_agent_routing_performance(self, agent_factory):
        """Test agent routing performance."""
        from src.orchestrator.classifier import QueryClassifier
        from src.orchestrator.router import AgentRouter

        classifier = QueryClassifier()
        router = AgentRouter(classifier)

        # Create context
        session = await agent_factory.context_manager.create_session(user_id="perf-test")
        agent_context = AgentContext(conversation_context=session)

        # Test queries for routing performance
        test_queries = [
            "What is OAuth?",
            "Design a microservices system",
            "Break down the implementation",
            "How to implement caching?",
            "What are design patterns?",
        ] * 10  # 50 total queries

        # Measure classification performance
        classification_times = []
        for query in test_queries:
            start_time = time.time()
            classification = classifier.classify_query(query)
            end_time = time.time()

            classification_times.append(end_time - start_time)
            assert classification.agent_type is not None

        # Measure routing performance
        routing_times = []
        available_agents = {AgentType.RAG_RETRIEVAL, AgentType.TECHNICAL_ARCHITECT, AgentType.TASK_PLANNER}

        for query in test_queries:
            start_time = time.time()
            decision = router.route_query(query, agent_context, available_agents)
            end_time = time.time()

            routing_times.append(end_time - start_time)
            assert decision.primary_agent is not None

        # Analyze performance
        avg_classification_time = mean(classification_times)
        avg_routing_time = mean(routing_times)

        print(f"Agent routing performance:")
        print(f"  Average classification time: {avg_classification_time*1000:.2f}ms")
        print(f"  Average routing time: {avg_routing_time*1000:.2f}ms")

        # Performance requirements (should be very fast)
        assert avg_classification_time < 0.1, f"Classification too slow: {avg_classification_time:.3f}s"
        assert avg_routing_time < 0.1, f"Routing too slow: {avg_routing_time:.3f}s"

    @pytest.mark.asyncio
    async def test_memory_usage_stability(self, agent_factory):
        """Test memory usage stability under repeated queries."""
        import psutil
        import os

        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)

        # Get initial memory usage
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # Create context
        session = await agent_factory.context_manager.create_session(user_id="memory-test")
        agent_context = AgentContext(conversation_context=session)

        # Process many queries
        for i in range(20):
            query = f"What is query number {i}?"
            response = await orchestrator.process_query(query, agent_context)
            assert response is not None

            # Update context
            updated_session = await agent_factory.context_manager.get_context(session.session_id)
            agent_context.conversation_context = updated_session

        # Check final memory usage
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        print(f"Memory usage:")
        print(f"  Initial: {initial_memory:.2f} MB")
        print(f"  Final: {final_memory:.2f} MB")
        print(f"  Increase: {memory_increase:.2f} MB")

        # Memory increase should be reasonable (less than 100MB for 20 queries)
        assert memory_increase < 100, f"Memory increase {memory_increase:.2f}MB too high"


class TestLoadPerformance:
    """Test system performance under load."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings for testing."""
        from src.config import get_settings
        settings = get_settings()
        settings.openai_api_key = "test-key"
        return settings

    @pytest.fixture
    def agent_factory(self, mock_settings):
        """Create agent factory for testing."""
        from src.agents.factory import AgentFactory
        return AgentFactory(mock_settings)

    @pytest.mark.asyncio
    async def test_stress_test_basic(self, agent_factory):
        """Basic stress test with rapid queries."""
        registry = AgentRegistry(agent_factory)
        orchestrator = registry.get_agent(AgentType.ORCHESTRATOR)

        # Create context
        session = await agent_factory.context_manager.create_session(user_id="stress-test")
        agent_context = AgentContext(conversation_context=session)

        # Rapid fire queries
        queries = ["What is API?"] * 10

        start_time = time.time()

        for query in queries:
            response = await orchestrator.process_query(query, agent_context)
            assert response is not None

            # Update context
            updated_session = await agent_factory.context_manager.get_context(session.session_id)
            agent_context.conversation_context = updated_session

        total_time = time.time() - start_time
        avg_time = total_time / len(queries)

        print(f"Stress test results:")
        print(f"  Total time: {total_time:.2f}s")
        print(f"  Average per query: {avg_time:.2f}s")
        print(f"  Queries per second: {len(queries)/total_time:.2f}")

        # Should maintain reasonable performance under load
        assert avg_time < 5.0, f"Average response time {avg_time:.2f}s too slow under load"
