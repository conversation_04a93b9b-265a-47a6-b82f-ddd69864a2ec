"""
Integration tests for agent orchestration system.
"""

import pytest
from unittest.mock import <PERSON><PERSON>, As<PERSON><PERSON>ock, patch

from src.agents import AgentFactory, AgentType
from src.agents.base import Agent<PERSON>ontext, ConversationContext
from src.config import Settings


class TestAgentOrchestration:
    """Test agent orchestration integration."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings for testing."""
        from src.config import get_settings
        # Use real settings but override sensitive values
        settings = get_settings()
        settings.openai_api_key = "test-key"
        return settings

    @pytest.fixture
    def agent_factory(self, mock_settings):
        """Create agent factory for testing."""
        return AgentFactory(mock_settings)

    def test_agent_factory_initialization(self, agent_factory):
        """Test that agent factory initializes correctly."""
        assert agent_factory is not None
        assert agent_factory.llm_client is not None
        assert agent_factory.formatter is not None
        assert agent_factory.context_manager is not None

    def test_rag_agent_creation(self, agent_factory):
        """Test RAG agent creation and basic functionality."""
        rag_agent = agent_factory.create_agent(AgentType.RAG_RETRIEVAL)

        assert rag_agent is not None
        assert rag_agent.agent_type == AgentType.RAG_RETRIEVAL

        # Test confidence scoring
        context = AgentContext(
            conversation_context=ConversationContext(session_id="test")
        )

        # Test different query types
        factual_confidence = rag_agent.can_handle_query("What is OAuth?", context)
        code_confidence = rag_agent.can_handle_query("Show me the login function", context)
        design_confidence = rag_agent.can_handle_query("Design a microservice architecture", context)

        assert factual_confidence > 0.8  # High confidence for factual queries
        assert code_confidence > 0.7     # High confidence for code queries
        assert design_confidence < 0.5   # Low confidence for design queries

    def test_agent_factory_available_agents(self, agent_factory):
        """Test getting available agents information."""
        available = agent_factory.get_available_agents()

        assert isinstance(available, dict)
        assert AgentType.RAG_RETRIEVAL in available
        assert AgentType.ORCHESTRATOR in available
        assert AgentType.TECHNICAL_ARCHITECT in available
        assert AgentType.TASK_PLANNER in available

        # Check agent information structure
        rag_info = available[AgentType.RAG_RETRIEVAL]
        assert "name" in rag_info
        assert "description" in rag_info
        assert "capabilities" in rag_info
        assert isinstance(rag_info["capabilities"], list)

    @patch('src.agents.llm_client.AsyncOpenAI')
    def test_llm_client_integration(self, mock_openai, agent_factory):
        """Test LLM client integration."""
        # Mock OpenAI response
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.content = "Test response"
        mock_response.usage.model_dump.return_value = {
            "prompt_tokens": 10,
            "completion_tokens": 5,
            "total_tokens": 15
        }

        mock_openai.return_value.chat.completions.create = AsyncMock(return_value=mock_response)

        llm_client = agent_factory.llm_client
        assert llm_client is not None

    def test_context_manager_integration(self, agent_factory):
        """Test conversation context manager integration."""
        context_manager = agent_factory.context_manager

        assert context_manager is not None
        assert context_manager.max_context_size == 50
        assert hasattr(context_manager, 'storage')

    @pytest.mark.asyncio
    async def test_context_manager_session_lifecycle(self, agent_factory):
        """Test context manager session lifecycle."""
        context_manager = agent_factory.context_manager

        # Create session
        session = await context_manager.create_session(
            user_id="test-user",
            repository="test-repo"
        )

        assert session.user_id == "test-user"
        assert session.current_repository == "test-repo"
        assert len(session.session_id) > 0

        # Retrieve session
        retrieved = await context_manager.get_context(session.session_id)
        assert retrieved is not None
        assert retrieved.session_id == session.session_id

    def test_formatter_integration(self, agent_factory):
        """Test response formatter integration."""
        formatter = agent_factory.formatter

        assert formatter is not None
        assert hasattr(formatter, 'format_response')
        assert hasattr(formatter, 'format_error')

    @pytest.mark.asyncio
    async def test_agent_factory_stats(self, agent_factory):
        """Test agent factory statistics."""
        stats = await agent_factory.get_stats()

        assert isinstance(stats, dict)
        assert "llm_client" in stats
        assert "settings" in stats

        # Check LLM client stats
        llm_stats = stats["llm_client"]
        assert "total_requests" in llm_stats
        assert "total_tokens" in llm_stats
        assert "total_cost" in llm_stats

    @pytest.mark.asyncio
    async def test_agent_registry_integration(self, agent_factory):
        """Test agent registry functionality."""
        from src.agents.factory import AgentRegistry

        registry = AgentRegistry(agent_factory)

        # Get agent (should create it)
        rag_agent = registry.get_agent(AgentType.RAG_RETRIEVAL)
        assert rag_agent is not None

        # Get same agent again (should return cached)
        rag_agent2 = registry.get_agent(AgentType.RAG_RETRIEVAL)
        assert rag_agent is rag_agent2  # Same instance

        # Check registry stats
        stats = await registry.get_registry_stats()
        assert "active_agents" in stats
        assert "agent_count" in stats
        assert stats["agent_count"] == 1

    def test_error_handling_integration(self, agent_factory):
        """Test error handling across the system."""
        from src.agents.exceptions import AgentConfigurationError
        from unittest.mock import patch

        # Test that agents can be created successfully (positive test)
        technical_agent = agent_factory.create_agent(AgentType.TECHNICAL_ARCHITECT)
        assert technical_agent is not None
        assert technical_agent.agent_type == AgentType.TECHNICAL_ARCHITECT

        # Test error handling when agent creation fails due to import error
        with patch('src.agents.factory.AgentFactory.create_agent') as mock_create:
            mock_create.side_effect = AgentConfigurationError("Test configuration error")

            with pytest.raises(AgentConfigurationError):
                agent_factory.create_agent(AgentType.TASK_PLANNER)

    def test_agent_base_class_integration(self, agent_factory):
        """Test agent base class functionality."""
        rag_agent = agent_factory.create_agent(AgentType.RAG_RETRIEVAL)

        # Test statistics
        initial_stats = rag_agent.get_stats()
        assert initial_stats["total_requests"] == 0

        # Test string representation
        str_repr = str(rag_agent)
        assert "RAGRetrievalAgent" in str_repr
        assert "rag_retrieval" in str_repr

    def test_query_classification_integration(self, agent_factory):
        """Test query classification integration."""
        from src.orchestrator.classifier import QueryClassifier

        classifier = QueryClassifier()

        # Test various query types
        test_cases = [
            ("How should I design the API?", "architecture_design"),
            ("Create a plan to implement authentication", "task_planning"),
            ("Show me the login function", "code_lookup"),
            ("What is OAuth?", "factual_query"),
            ("documentation for this API", "documentation"),
        ]

        for query, expected_intent in test_cases:
            result = classifier.classify_query(query)
            assert result.intent.value == expected_intent
            assert result.confidence > 0.0
            assert len(result.reasoning) > 0

    def test_agent_routing_integration(self, agent_factory):
        """Test agent routing integration."""
        from src.orchestrator.router import AgentRouter
        from src.orchestrator.classifier import QueryClassifier
        from src.agents.base import AgentContext, ConversationContext

        classifier = QueryClassifier()
        router = AgentRouter(classifier)

        context = AgentContext(
            conversation_context=ConversationContext(session_id="test")
        )

        available_agents = {AgentType.RAG_RETRIEVAL, AgentType.TECHNICAL_ARCHITECT}

        # Test routing decision
        decision = router.route_query(
            "What is OAuth?",
            context,
            available_agents
        )

        assert decision.primary_agent == AgentType.RAG_RETRIEVAL
        assert decision.confidence > 0.0
        assert len(decision.reasoning) > 0

    def test_response_synthesis_integration(self, agent_factory):
        """Test response synthesis integration."""
        from src.orchestrator.synthesizer import ResponseSynthesizer
        from src.agents.base import AgentResponse, AgentType

        synthesizer = ResponseSynthesizer()

        # Create test responses
        responses = [
            AgentResponse(
                agent_type=AgentType.RAG_RETRIEVAL,
                content="OAuth is an authorization framework.",
                confidence=0.9,
                sources=["oauth_spec.md"]
            )
        ]

        # Test synthesis
        result = synthesizer.synthesize_responses(responses, query="What is OAuth?")

        assert result.content == "OAuth is an authorization framework."
        assert result.confidence == 0.9
        assert "oauth_spec.md" in result.sources
        assert result.synthesis_strategy == "single_response"

    @pytest.mark.asyncio
    async def test_end_to_end_workflow(self, agent_factory):
        """Test end-to-end workflow without external dependencies."""
        # This test verifies the system can be initialized and basic operations work

        # 1. Create context
        context_manager = agent_factory.context_manager
        session = await context_manager.create_session(user_id="test")

        # 2. Create agent context
        agent_context = AgentContext(conversation_context=session)

        # 3. Create RAG agent
        rag_agent = agent_factory.create_agent(AgentType.RAG_RETRIEVAL)

        # 4. Test query handling capability
        confidence = rag_agent.can_handle_query("What is OAuth?", agent_context)
        assert confidence > 0.0

        # 5. Test classifier
        from src.orchestrator.classifier import QueryClassifier
        classifier = QueryClassifier()
        classification = classifier.classify_query("What is OAuth?")
        assert classification.agent_type == AgentType.RAG_RETRIEVAL

        # 6. Test router
        from src.orchestrator.router import AgentRouter
        router = AgentRouter(classifier)
        decision = router.route_query(
            "What is OAuth?",
            agent_context,
            {AgentType.RAG_RETRIEVAL}
        )
        assert decision.primary_agent == AgentType.RAG_RETRIEVAL

        print("✅ End-to-end workflow test completed successfully")
