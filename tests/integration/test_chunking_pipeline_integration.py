"""
Integration tests for the chunking pipeline.

Tests the complete chunking workflow including file loading, strategy selection,
chunking, and integration with the GitHub Connector.
"""

import pytest
import tempfile
import shutil
from pathlib import Path
from unittest.mock import Mock
from datetime import datetime

from src.ingestion import (
    DefaultChunkingPipeline,
    UniversalFileLoader,
    GitHubConnector,
    FileMetadata,
    ChunkType,
)
from src.config import Settings


@pytest.fixture
def mock_settings():
    """Create mock settings for testing."""
    settings = Mock(spec=Settings)
    settings.max_file_size = 1024 * 1024  # 1MB
    settings.allowed_file_types = ["py", "js", "md", "txt", "json", "yaml"]
    settings.temp_dir = tempfile.mkdtemp()
    settings.chunk_size = 500
    settings.chunk_overlap = 100
    settings.max_chunk_size = 1000
    settings.min_chunk_size = 10  # Lower minimum for testing
    settings.chunk_batch_size = 10
    return settings


@pytest.fixture
def temp_repo_dir():
    """Create a temporary directory with sample files for testing."""
    temp_dir = Path(tempfile.mkdtemp())

    # Create directory structure
    (temp_dir / "src").mkdir()
    (temp_dir / "docs").mkdir()
    (temp_dir / "config").mkdir()

    # Create sample Python file
    (temp_dir / "src" / "main.py").write_text('''"""
Main module for the application.
"""

import os
import sys
from typing import List, Dict, Any

class DataProcessor:
    """Processes data from various sources."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the processor with configuration."""
        self.config = config
        self.data = []
    
    def load_data(self, source: str) -> List[Dict]:
        """Load data from a source."""
        if source == "file":
            return self._load_from_file()
        elif source == "api":
            return self._load_from_api()
        else:
            raise ValueError(f"Unknown source: {source}")
    
    def _load_from_file(self) -> List[Dict]:
        """Load data from file."""
        # Implementation here
        return []
    
    def _load_from_api(self) -> List[Dict]:
        """Load data from API."""
        # Implementation here
        return []
    
    def process_data(self, data: List[Dict]) -> List[Dict]:
        """Process the loaded data."""
        processed = []
        for item in data:
            # Process each item
            processed_item = self._process_item(item)
            processed.append(processed_item)
        return processed
    
    def _process_item(self, item: Dict) -> Dict:
        """Process a single data item."""
        # Processing logic here
        return item

def main():
    """Main entry point."""
    config = {"source": "file", "output": "processed_data.json"}
    processor = DataProcessor(config)
    
    data = processor.load_data(config["source"])
    processed_data = processor.process_data(data)
    
    print(f"Processed {len(processed_data)} items")

if __name__ == "__main__":
    main()
''')

    # Create sample Markdown file
    (temp_dir / "docs" / "README.md").write_text('''# Project Documentation

This is the main documentation for our project.

## Overview

The project consists of several components:

- Data processing module
- API interface
- Configuration management
- Testing framework

## Getting Started

To get started with the project:

1. Install dependencies
2. Configure the application
3. Run the main script

### Installation

```bash
pip install -r requirements.txt
```

### Configuration

Create a configuration file:

```json
{
    "source": "file",
    "output": "processed_data.json",
    "debug": true
}
```

### Running

Execute the main script:

```bash
python src/main.py
```

## API Reference

### DataProcessor Class

The main class for data processing.

#### Methods

- `load_data(source)`: Load data from specified source
- `process_data(data)`: Process the loaded data
- `save_data(data, output)`: Save processed data

## Contributing

Please read our contributing guidelines before submitting pull requests.

## License

This project is licensed under the MIT License.
''')

    # Create sample JSON config
    (temp_dir / "config" / "settings.json").write_text('''{
    "database": {
        "host": "localhost",
        "port": 5432,
        "name": "myapp",
        "user": "admin",
        "password": "secret"
    },
    "api": {
        "version": "v1",
        "base_url": "https://api.example.com",
        "timeout": 30,
        "retries": 3
    },
    "logging": {
        "level": "INFO",
        "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        "file": "app.log",
        "max_size": "10MB",
        "backup_count": 5
    },
    "features": {
        "enable_caching": true,
        "cache_ttl": 3600,
        "enable_metrics": true,
        "metrics_port": 9090
    }
}''')

    # Create sample text file
    (temp_dir / "CHANGELOG.txt").write_text('''CHANGELOG

Version 2.1.0 (2024-01-15)
- Added new data processing features
- Improved error handling
- Updated documentation
- Fixed memory leak in processor

Version 2.0.0 (2023-12-01)
- Major refactoring of core components
- Breaking changes to API interface
- New configuration system
- Enhanced performance

Version 1.5.2 (2023-11-15)
- Bug fixes for edge cases
- Security updates
- Minor performance improvements

Version 1.5.1 (2023-11-01)
- Hotfix for critical bug
- Updated dependencies

Version 1.5.0 (2023-10-15)
- New features for data validation
- Improved logging system
- Better error messages
- Code cleanup and optimization
''')

    yield temp_dir

    # Cleanup
    shutil.rmtree(temp_dir)


@pytest.fixture
def sample_file_metadata_list(temp_repo_dir):
    """Create sample file metadata list."""
    return [
        FileMetadata(
            file_path="src/main.py",
            file_size=2048,
            file_type="text",
            last_modified=datetime.now(),
            language="python",
            priority=0.05,
        ),
        FileMetadata(
            file_path="docs/README.md",
            file_size=1024,
            file_type="text",
            last_modified=datetime.now(),
            priority=0.2,
        ),
        FileMetadata(
            file_path="config/settings.json",
            file_size=512,
            file_type="text",
            last_modified=datetime.now(),
            priority=0.1,
        ),
        FileMetadata(
            file_path="CHANGELOG.txt",
            file_size=800,
            file_type="text",
            last_modified=datetime.now(),
            priority=0.1,
        ),
    ]


class TestChunkingPipelineIntegration:
    """Integration test cases for the chunking pipeline."""

    @pytest.mark.asyncio
    async def test_process_files_complete_workflow(self, mock_settings, temp_repo_dir, sample_file_metadata_list):
        """Test complete file processing workflow."""
        pipeline = DefaultChunkingPipeline(mock_settings)

        chunks = await pipeline.process_files(sample_file_metadata_list, temp_repo_dir)

        # Verify chunks were created
        assert len(chunks) > 0

        # Verify different chunk types are present
        chunk_types = {chunk.chunk_type for chunk in chunks}
        assert ChunkType.CODE_CLASS in chunk_types or ChunkType.CODE_FUNCTION in chunk_types
        assert ChunkType.MARKDOWN_SECTION in chunk_types
        assert ChunkType.CONFIG_SECTION in chunk_types
        assert ChunkType.TEXT_PARAGRAPH in chunk_types

        # Verify all chunks have required metadata
        for chunk in chunks:
            assert chunk.chunk_id
            assert chunk.content.strip()
            assert chunk.file_metadata
            assert chunk.start_line > 0
            assert chunk.end_line >= chunk.start_line

    @pytest.mark.asyncio
    async def test_process_single_python_file(self, mock_settings, temp_repo_dir):
        """Test processing a single Python file."""
        pipeline = DefaultChunkingPipeline(mock_settings)

        file_metadata = FileMetadata(
            file_path="src/main.py",
            file_size=2048,
            file_type="text",
            last_modified=datetime.now(),
            language="python",
        )

        chunks = await pipeline.process_single_file(file_metadata, temp_repo_dir)

        assert len(chunks) > 0

        # Should have code chunks
        code_chunks = [c for c in chunks if c.chunk_type in [ChunkType.CODE_CLASS, ChunkType.CODE_FUNCTION]]
        assert len(code_chunks) > 0

        # Verify context information
        for chunk in code_chunks:
            if chunk.chunk_type == ChunkType.CODE_CLASS:
                assert chunk.context.class_name
            elif chunk.chunk_type == ChunkType.CODE_FUNCTION:
                assert chunk.context.function_name or chunk.context.class_name

    @pytest.mark.asyncio
    async def test_process_single_markdown_file(self, mock_settings, temp_repo_dir):
        """Test processing a single Markdown file."""
        pipeline = DefaultChunkingPipeline(mock_settings)

        file_metadata = FileMetadata(
            file_path="docs/README.md",
            file_size=1024,
            file_type="text",
            last_modified=datetime.now(),
        )

        chunks = await pipeline.process_single_file(file_metadata, temp_repo_dir)

        assert len(chunks) > 0

        # Should have markdown sections
        section_chunks = [c for c in chunks if c.chunk_type == ChunkType.MARKDOWN_SECTION]
        assert len(section_chunks) > 0

        # Should have code blocks
        code_chunks = [c for c in chunks if c.chunk_type == ChunkType.MARKDOWN_CODE_BLOCK]
        assert len(code_chunks) > 0

        # Verify hierarchical context
        for chunk in section_chunks:
            assert chunk.context.section_headers

    @pytest.mark.asyncio
    async def test_process_single_config_file(self, mock_settings, temp_repo_dir):
        """Test processing a single configuration file."""
        pipeline = DefaultChunkingPipeline(mock_settings)

        file_metadata = FileMetadata(
            file_path="config/settings.json",
            file_size=512,
            file_type="text",
            last_modified=datetime.now(),
        )

        chunks = await pipeline.process_single_file(file_metadata, temp_repo_dir)

        assert len(chunks) > 0

        # Should have config sections
        config_chunks = [c for c in chunks if c.chunk_type == ChunkType.CONFIG_SECTION]
        assert len(config_chunks) > 0

        # Verify namespace context
        for chunk in config_chunks:
            assert chunk.context.namespace or chunk.context.section_headers

    @pytest.mark.asyncio
    async def test_file_loader_encoding_detection(self, mock_settings, temp_repo_dir):
        """Test file loader encoding detection."""
        loader = UniversalFileLoader(mock_settings)

        # Test loading different file types
        python_file = temp_repo_dir / "src" / "main.py"
        content = await loader.load_file(python_file)

        assert content
        assert "DataProcessor" in content
        assert content.endswith('\n')  # Should be normalized

    @pytest.mark.asyncio
    async def test_processing_statistics(self, mock_settings, temp_repo_dir, sample_file_metadata_list):
        """Test processing statistics collection."""
        pipeline = DefaultChunkingPipeline(mock_settings)

        # Process files
        chunks = await pipeline.process_files(sample_file_metadata_list, temp_repo_dir)

        # Check statistics
        stats = pipeline.get_processing_stats()

        assert stats["files_processed"] == len(sample_file_metadata_list)
        assert stats["chunks_created"] == len(chunks)
        assert stats["processing_time"] > 0
        assert stats["errors_encountered"] >= 0

    @pytest.mark.asyncio
    async def test_chunk_validation(self, mock_settings, temp_repo_dir, sample_file_metadata_list):
        """Test chunk validation functionality."""
        pipeline = DefaultChunkingPipeline(mock_settings)

        chunks = await pipeline.process_files(sample_file_metadata_list, temp_repo_dir)

        # Validate chunks
        validation_results = await pipeline.validate_chunks(chunks)

        assert validation_results["total_chunks"] == len(chunks)
        assert validation_results["valid_chunks"] > 0
        assert validation_results["empty_chunks"] == 0
        assert validation_results["duplicate_ids"] == 0

    @pytest.mark.asyncio
    async def test_processing_estimation(self, mock_settings, temp_repo_dir, sample_file_metadata_list):
        """Test processing time and chunk estimation."""
        pipeline = DefaultChunkingPipeline(mock_settings)

        estimation = await pipeline.estimate_processing_time(sample_file_metadata_list, temp_repo_dir)

        assert estimation["total_files"] == len(sample_file_metadata_list)
        assert estimation["supported_files"] > 0
        assert estimation["estimated_chunks"] > 0
        assert estimation["estimated_time_seconds"] > 0

    @pytest.mark.asyncio
    async def test_error_handling_missing_file(self, mock_settings, temp_repo_dir):
        """Test error handling for missing files."""
        pipeline = DefaultChunkingPipeline(mock_settings)

        file_metadata = FileMetadata(
            file_path="nonexistent.py",
            file_size=100,
            file_type="text",
            last_modified=datetime.now(),
        )

        # Should not raise exception, just return empty list
        chunks = await pipeline.process_single_file(file_metadata, temp_repo_dir)
        assert chunks == []

    @pytest.mark.asyncio
    async def test_integration_with_github_connector_output(self, mock_settings, temp_repo_dir):
        """Test integration with GitHub Connector output format."""
        # Simulate GitHub Connector output
        github_output = {
            "file_metadata": [
                {
                    "file_path": "src/main.py",
                    "file_size": 2048,
                    "file_type": "text",
                    "last_modified": datetime.now().isoformat(),
                    "language": "python",
                    "priority": 0.05,
                },
                {
                    "file_path": "docs/README.md",
                    "file_size": 1024,
                    "file_type": "text",
                    "last_modified": datetime.now().isoformat(),
                    "priority": 0.2,
                },
            ]
        }

        # Convert to FileMetadata objects
        file_metadata_list = []
        for fm_dict in github_output["file_metadata"]:
            file_metadata = FileMetadata(
                file_path=fm_dict["file_path"],
                file_size=fm_dict["file_size"],
                file_type=fm_dict["file_type"],
                last_modified=datetime.fromisoformat(fm_dict["last_modified"]),
                language=fm_dict.get("language"),
                priority=fm_dict.get("priority", 0.0),
            )
            file_metadata_list.append(file_metadata)

        # Process with chunking pipeline
        pipeline = DefaultChunkingPipeline(mock_settings)
        chunks = await pipeline.process_files(file_metadata_list, temp_repo_dir)

        assert len(chunks) > 0

        # Verify chunks maintain file metadata
        for chunk in chunks:
            assert chunk.file_metadata.file_path in ["src/main.py", "docs/README.md"]
            assert chunk.file_metadata.priority > 0
