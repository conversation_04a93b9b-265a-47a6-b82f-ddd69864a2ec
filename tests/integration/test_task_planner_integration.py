"""
Integration tests for Task Planner Agent.
"""

import pytest
from unittest.mock import Async<PERSON>ock, Mock, patch

from src.agents.base import <PERSON><PERSON><PERSON>x<PERSON>, AgentType
from src.agents.exceptions import Agent<PERSON>rror
# from src.agents.factory import AgentFactory  # Not implemented yet
from src.agents.llm_client import <PERSON><PERSON>lient, LLMResponse
# from src.agents.orchestrator import OrchestratorAgent  # Not implemented yet
from src.config import Settings
from src.ingestion.integration_example import IntegratedIngestionPipeline
from src.task_planner.agent import TaskPlannerAgent


class TestTaskPlannerIntegration:
    """Integration tests for Task Planner Agent."""

    @pytest.fixture
    def mock_settings(self):
        """Create mock settings for testing."""
        settings = Mock(spec=Settings)
        settings.task_planner = {
            "working_hours_per_day": 8.0,
            "working_days_per_week": 5,
            "default_buffer_factor": 1.3,
            "max_task_complexity_hours": 80.0,
            "enable_risk_analysis": True,
            "enable_dependency_optimization": True,
        }
        settings.llm = Mock()
        settings.llm.model = "test-model"
        settings.llm.temperature = 0.7
        settings.llm.max_tokens = 2000
        return settings

    @pytest.fixture
    def mock_llm_client(self):
        """Create a mock LLM client."""
        client = Mock(spec=LLMClient)
        client.generate_response = AsyncMock(return_value=LLMResponse(
            content="This is a comprehensive task planning response with detailed breakdown and timeline analysis.",
            usage={"prompt_tokens": 150, "completion_tokens": 300, "total_tokens": 450},
            model="test-model",
            processing_time=1.2,
            metadata={}
        ))
        return client

    @pytest.fixture
    def mock_ingestion_pipeline(self):
        """Create a mock ingestion pipeline."""
        pipeline = Mock(spec=IntegratedIngestionPipeline)

        # Create realistic mock search results
        mock_chunks = []
        for i in range(3):
            chunk = Mock()
            chunk.content = f"Sample code content {i} related to user authentication and task planning"
            chunk.file_metadata.file_path = f"src/auth/module_{i}.py"
            mock_chunks.append(chunk)

        mock_results = []
        for chunk in mock_chunks:
            result = Mock()
            result.chunk = chunk
            result.similarity_score = 0.8 - (0.1 * len(mock_results))
            mock_results.append(result)

        pipeline.search_content = AsyncMock(return_value=mock_results)
        return pipeline

    @pytest.fixture
    def task_planner_agent(self, mock_settings, mock_llm_client, mock_ingestion_pipeline):
        """Create a Task Planner agent directly."""
        return TaskPlannerAgent(
            llm_client=mock_llm_client,
            ingestion_pipeline=mock_ingestion_pipeline,
            settings=mock_settings,
        )

    # @pytest.fixture
    # def orchestrator_agent(self, agent_factory):
    #     """Create an Orchestrator agent for integration testing."""
    #     return agent_factory.create_agent(AgentType.ORCHESTRATOR)

    @pytest.fixture
    def sample_context(self):
        """Create sample context for testing."""
        from src.agents.base import ConversationContext
        conversation_context = ConversationContext(
            session_id="integration-test-123",
            user_id="test-user",
            conversation_history=[],
            shared_state={"previous_queries": []}
        )
        return AgentContext(
            conversation_context=conversation_context,
            repository_context={"repo_name": "test-repo"},
            search_results=[],
        )

    def test_task_planner_agent_creation(self, task_planner_agent):
        """Test that Task Planner agent can be created directly."""
        assert isinstance(task_planner_agent, TaskPlannerAgent)
        assert task_planner_agent.agent_type == AgentType.TASK_PLANNER
        assert task_planner_agent.breakdown_engine is not None
        assert task_planner_agent.timeline_generator is not None
        assert task_planner_agent.dependency_analyzer is not None
        assert task_planner_agent.risk_analyzer is not None

    @pytest.mark.asyncio
    async def test_task_planner_end_to_end_breakdown(self, task_planner_agent, sample_context):
        """Test end-to-end task breakdown functionality."""
        query = "Break down the implementation of a user authentication system with OAuth integration"

        response = await task_planner_agent.process_query(query, sample_context)

        # Verify response structure
        assert response.agent_type == AgentType.TASK_PLANNER
        assert response.content is not None
        assert len(response.content) > 100  # Should be substantial
        assert response.confidence > 0.5
        assert response.processing_time > 0

        # Verify metadata
        assert response.metadata["query_type"] == "breakdown"
        assert response.metadata["methodology"] == "5-phase"
        assert "requirements_count" in response.metadata

        # Verify content includes expected elements
        content_lower = response.content.lower()
        assert any(keyword in content_lower for keyword in ["task", "breakdown", "authentication", "oauth"])
        assert "```json" in response.content  # Should include structured data

    @pytest.mark.asyncio
    async def test_task_planner_end_to_end_timeline(self, task_planner_agent, sample_context):
        """Test end-to-end timeline generation functionality."""
        query = "What's the timeline for implementing user authentication with testing and documentation?"

        response = await task_planner_agent.process_query(query, sample_context)

        # Verify response structure
        assert response.agent_type == AgentType.TASK_PLANNER
        assert response.metadata["query_type"] == "timeline"

        # Verify timeline-specific content
        content_lower = response.content.lower()
        assert any(keyword in content_lower for keyword in ["timeline", "duration", "hours", "days"])
        assert "start date" in content_lower or "end date" in content_lower

    @pytest.mark.asyncio
    async def test_task_planner_end_to_end_comprehensive_plan(self, task_planner_agent, sample_context):
        """Test end-to-end comprehensive planning functionality."""
        query = "Create a comprehensive project plan for implementing a user dashboard with authentication"

        response = await task_planner_agent.process_query(query, sample_context)

        # Verify response structure
        assert response.agent_type == AgentType.TASK_PLANNER
        assert response.metadata["query_type"] == "planning"

        # Verify comprehensive plan content
        content_lower = response.content.lower()
        assert any(keyword in content_lower for keyword in ["project plan", "comprehensive", "risk", "timeline"])
        assert "risk assessment" in content_lower or "risk level" in content_lower

    # @pytest.mark.asyncio
    # async def test_orchestrator_routes_to_task_planner(self, orchestrator_agent, sample_context):
    #     """Test that orchestrator correctly routes task planning queries."""
    #     task_planning_queries = [
    #         "Break down this feature into tasks",
    #         "Create a project timeline",
    #         "Plan the implementation roadmap",
    #         "Estimate effort for this project",
    #     ]
    #
    #     for query in task_planning_queries:
    #         response = await orchestrator_agent.process_query(query, sample_context)
    #
    #         # Should route to task planner
    #         assert response.agent_type == AgentType.TASK_PLANNER
    #         assert response.confidence > 0.5

    @pytest.mark.asyncio
    async def test_task_planner_with_search_integration(self, task_planner_agent, mock_ingestion_pipeline):
        """Test Task Planner integration with search functionality."""
        from src.agents.base import ConversationContext
        conversation_context = ConversationContext(
            session_id="search-test-123",
            user_id="test-user",
            conversation_history=[],
            shared_state={}
        )
        context = AgentContext(
            conversation_context=conversation_context,
            search_results=[],  # Empty to trigger search
        )

        query = "Plan implementation of authentication based on existing codebase"

        response = await task_planner_agent.process_query(query, context)

        # Verify search was performed
        mock_ingestion_pipeline.search_content.assert_called_once()

        # Verify search parameters
        call_args = mock_ingestion_pipeline.search_content.call_args
        assert call_args[1]["query"] == query
        assert call_args[1]["limit"] == 20
        assert call_args[1]["similarity_threshold"] == 0.3

        # Verify response includes sources
        assert response.sources is not None
        assert len(response.sources) > 0

    @pytest.mark.asyncio
    async def test_task_planner_error_handling_integration(self, task_planner_agent, sample_context, mock_llm_client):
        """Test error handling in integrated environment."""
        # Simulate LLM failure
        mock_llm_client.generate_response.side_effect = Exception("LLM service unavailable")

        query = "Plan the implementation"

        with pytest.raises(AgentError) as exc_info:
            await task_planner_agent.process_query(query, sample_context)

        # Verify error details
        assert "Failed to process task planning query" in str(exc_info.value)
        assert exc_info.value.agent_type == AgentType.TASK_PLANNER.value
        assert "details" in exc_info.value.__dict__

    @pytest.mark.asyncio
    async def test_task_planner_with_existing_search_results(self, task_planner_agent, mock_ingestion_pipeline):
        """Test Task Planner with pre-existing search results."""
        # Create context with existing search results
        mock_chunk = Mock()
        mock_chunk.content = "Existing authentication code with user management"
        mock_chunk.file_metadata.file_path = "src/auth/existing_auth.py"

        mock_result = Mock()
        mock_result.chunk = mock_chunk
        mock_result.similarity_score = 0.9

        from src.agents.base import ConversationContext
        conversation_context = ConversationContext(
            session_id="existing-search-test",
            user_id="test-user",
            conversation_history=[],
            shared_state={}
        )
        context = AgentContext(
            conversation_context=conversation_context,
            search_results=[mock_result],
        )

        query = "Plan authentication enhancement"

        response = await task_planner_agent.process_query(query, context)

        # Should not perform new search
        mock_ingestion_pipeline.search_content.assert_not_called()

        # Should still include sources from existing results
        assert response.sources is not None
        assert "src/auth/existing_auth.py" in response.sources

    @pytest.mark.asyncio
    async def test_multiple_query_types_in_sequence(self, task_planner_agent, sample_context):
        """Test processing multiple different query types in sequence."""
        queries_and_types = [
            ("Break down user authentication implementation", "breakdown"),
            ("What's the timeline for this project?", "timeline"),
            ("Analyze the dependencies between tasks", "dependencies"),
            ("What are the risks for this implementation?", "risks"),
            ("Create a comprehensive project plan", "planning"),
        ]

        for query, expected_type in queries_and_types:
            response = await task_planner_agent.process_query(query, sample_context)

            assert response.agent_type == AgentType.TASK_PLANNER
            assert response.metadata["query_type"] == expected_type
            assert response.confidence > 0.3
            assert len(response.content) > 50

    @pytest.mark.asyncio
    async def test_task_planner_performance_metrics(self, task_planner_agent, sample_context):
        """Test that performance metrics are properly tracked."""
        query = "Plan the implementation of user authentication"

        # Get initial stats
        initial_stats = task_planner_agent.get_stats()
        initial_queries = initial_stats.get("total_queries", 0)

        # Process query
        response = await task_planner_agent.process_query(query, sample_context)

        # Verify performance metrics
        assert response.processing_time > 0
        assert response.processing_time < 10.0  # Should be reasonable

        # Verify stats are updated
        updated_stats = task_planner_agent.get_stats()
        assert updated_stats["total_queries"] == initial_queries + 1
        assert "average_processing_time" in updated_stats

    @pytest.mark.asyncio
    async def test_task_planner_with_complex_requirements(self, task_planner_agent, sample_context):
        """Test Task Planner with complex, multi-faceted requirements."""
        complex_query = """
        Plan the implementation of a comprehensive user management system that includes:
        - User authentication with OAuth and 2FA
        - Role-based access control
        - User profile management
        - Audit logging
        - Integration with existing systems
        - Comprehensive testing strategy
        - Documentation and deployment
        """

        response = await task_planner_agent.process_query(complex_query, sample_context)

        # Verify handling of complex requirements
        assert response.confidence > 0.6  # Should be confident with detailed requirements
        assert response.metadata["requirements_count"] > 1  # Should extract multiple requirements

        # Verify comprehensive response
        content_lower = response.content.lower()
        expected_elements = ["oauth", "2fa", "role", "access", "audit", "testing", "documentation"]
        found_elements = sum(1 for element in expected_elements if element in content_lower)
        assert found_elements >= 4  # Should address most requirements

    @pytest.mark.asyncio
    async def test_task_planner_json_output_validity(self, task_planner_agent, sample_context):
        """Test that Task Planner produces valid JSON output."""
        query = "Break down user authentication into tasks"

        response = await task_planner_agent.process_query(query, sample_context)

        # Extract JSON from response
        assert "```json" in response.content

        json_start = response.content.find("```json") + 7
        json_end = response.content.find("```", json_start)
        json_content = response.content[json_start:json_end].strip()

        # Verify JSON is valid and contains expected structure
        import json
        try:
            parsed_json = json.loads(json_content)

            # Verify required fields
            assert "title" in parsed_json
            assert "tasks" in parsed_json
            assert "timeline" in parsed_json
            assert "confidence" in parsed_json
            assert "methodology" in parsed_json

            # Verify tasks structure
            if parsed_json["tasks"]:
                task = parsed_json["tasks"][0]
                assert "id" in task
                assert "title" in task
                assert "description" in task
                assert "type" in task
                assert "estimate_hours" in task

        except json.JSONDecodeError as e:
            pytest.fail(f"Invalid JSON in response: {e}")

    def test_task_planner_configuration_integration(self, task_planner_agent):
        """Test that Task Planner properly uses configuration settings."""
        # Verify configuration is applied
        assert task_planner_agent.timeline_generator.working_hours_per_day == 8.0
        assert task_planner_agent.timeline_generator.working_days_per_week == 5

        # Verify buffer factors are configured
        buffer_factors = task_planner_agent.timeline_generator.buffer_factors
        assert "implementation" in buffer_factors
        assert buffer_factors["implementation"] == 1.5

    # @pytest.mark.asyncio
    # async def test_orchestrator_task_planner_coordination(self, orchestrator_agent, sample_context):
    #     """Test coordination between Orchestrator and Task Planner."""
    #     # Query that should definitely go to Task Planner
    #     planning_query = "Create a detailed project plan with timeline and risk assessment"
    #
    #     response = await orchestrator_agent.process_query(planning_query, sample_context)
    #
    #     # Verify routing
    #     assert response.agent_type == AgentType.TASK_PLANNER
    #
    #     # Verify orchestrator adds value
    #     assert response.confidence > 0.7  # Should be high confidence for clear planning query
    #
    #     # Verify response quality
    #     content_lower = response.content.lower()
    #     assert any(keyword in content_lower for keyword in ["project plan", "timeline", "risk"])

    @pytest.mark.asyncio
    async def test_task_planner_source_attribution(self, task_planner_agent, sample_context, mock_ingestion_pipeline):
        """Test that Task Planner properly attributes sources."""
        query = "Plan authentication based on existing code patterns"

        response = await task_planner_agent.process_query(query, sample_context)

        # Verify sources are included
        assert response.sources is not None
        assert len(response.sources) > 0

        # Verify source format
        for source in response.sources:
            assert isinstance(source, str)
            assert len(source) > 0
            # Should be file paths
            assert "/" in source or "\\" in source or source == "Unknown file"
