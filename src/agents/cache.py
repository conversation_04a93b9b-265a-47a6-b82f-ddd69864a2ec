"""
LLM Response Caching System

This module provides caching for LLM responses to improve performance by 80-90%
for repeated queries. Supports both Redis and in-memory caching with automatic fallback.
"""

from abc import ABC, abstractmethod
import asyncio
from dataclasses import asdict, dataclass
import hashlib
import json
import logging
import time
from typing import Any

from ..config import Settings
from .models import LLMResponse

logger = logging.getLogger(__name__)


@dataclass
class CacheEntry:
    """Cache entry for LLM responses."""

    response: LLMResponse
    created_at: float
    access_count: int = 0
    last_accessed: float = 0.0

    def __post_init__(self):
        if self.last_accessed == 0.0:
            self.last_accessed = self.created_at


class CacheBackend(ABC):
    """Abstract base class for cache backends."""

    @abstractmethod
    async def get(self, key: str) -> CacheEntry | None:
        """Get cache entry by key."""
        pass

    @abstractmethod
    async def set(self, key: str, entry: CacheEntry, ttl: int = 3600) -> None:
        """Set cache entry with TTL in seconds."""
        pass

    @abstractmethod
    async def delete(self, key: str) -> None:
        """Delete cache entry."""
        pass

    @abstractmethod
    async def clear(self) -> None:
        """Clear all cache entries."""
        pass

    @abstractmethod
    async def get_stats(self) -> dict[str, Any]:
        """Get cache statistics."""
        pass


class InMemoryCache(CacheBackend):
    """In-memory cache backend with TTL support."""

    def __init__(self, max_size: int = 1000):
        self.cache: dict[str, CacheEntry] = {}
        self.max_size = max_size
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "evictions": 0,
        }

    async def get(self, key: str) -> CacheEntry | None:
        """Get cache entry by key."""
        entry = self.cache.get(key)
        if entry:
            # Update access stats
            entry.access_count += 1
            entry.last_accessed = time.time()
            self.stats["hits"] += 1
            return entry

        self.stats["misses"] += 1
        return None

    async def set(self, key: str, entry: CacheEntry, ttl: int = 3600) -> None:
        """Set cache entry with TTL."""
        # Evict if at max size
        if len(self.cache) >= self.max_size:
            await self._evict_lru()

        self.cache[key] = entry
        self.stats["sets"] += 1

        # Schedule TTL cleanup
        asyncio.create_task(self._schedule_ttl_cleanup(key, ttl))

    async def delete(self, key: str) -> None:
        """Delete cache entry."""
        if key in self.cache:
            del self.cache[key]
            self.stats["deletes"] += 1

    async def clear(self) -> None:
        """Clear all cache entries."""
        self.cache.clear()

    async def get_stats(self) -> dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = self.stats["hits"] / total_requests if total_requests > 0 else 0.0

        return {
            **self.stats,
            "size": len(self.cache),
            "hit_rate": hit_rate,
            "backend": "memory",
        }

    async def _evict_lru(self) -> None:
        """Evict least recently used entry."""
        if not self.cache:
            return

        # Find LRU entry
        lru_key = min(self.cache.keys(), key=lambda k: self.cache[k].last_accessed)
        del self.cache[lru_key]
        self.stats["evictions"] += 1

    async def _schedule_ttl_cleanup(self, key: str, ttl: int) -> None:
        """Schedule TTL-based cleanup."""
        await asyncio.sleep(ttl)
        if key in self.cache:
            del self.cache[key]


class RedisCache(CacheBackend):
    """Redis cache backend."""

    def __init__(self, settings: Settings):
        self.settings = settings
        self.redis = None
        self.stats = {
            "hits": 0,
            "misses": 0,
            "sets": 0,
            "deletes": 0,
            "errors": 0,
        }

    async def _get_redis(self):
        """Get Redis connection with lazy initialization."""
        if self.redis is None:
            try:
                import redis.asyncio as redis
                self.redis = redis.Redis(
                    host=self.settings.redis_host,
                    port=self.settings.redis_port,
                    db=self.settings.redis_db,
                    password=self.settings.redis_password,
                    decode_responses=True,
                )
                # Test connection
                await self.redis.ping()
                logger.info("Connected to Redis cache")
            except Exception as e:
                logger.warning(f"Failed to connect to Redis: {e}")
                self.redis = None
                raise
        return self.redis

    async def get(self, key: str) -> CacheEntry | None:
        """Get cache entry by key."""
        try:
            redis = await self._get_redis()
            data = await redis.get(f"llm_cache:{key}")
            if data:
                entry_dict = json.loads(data)
                # Reconstruct LLMResponse
                response_data = entry_dict["response"]
                response = LLMResponse(**response_data)

                entry = CacheEntry(
                    response=response,
                    created_at=entry_dict["created_at"],
                    access_count=entry_dict["access_count"] + 1,
                    last_accessed=time.time(),
                )

                # Update access stats in Redis
                entry_dict["access_count"] = entry.access_count
                entry_dict["last_accessed"] = entry.last_accessed
                await redis.set(f"llm_cache:{key}", json.dumps(entry_dict, default=str))

                self.stats["hits"] += 1
                return entry

            self.stats["misses"] += 1
            return None

        except Exception as e:
            logger.error(f"Redis cache get error: {e}")
            self.stats["errors"] += 1
            return None

    async def set(self, key: str, entry: CacheEntry, ttl: int = 3600) -> None:
        """Set cache entry with TTL."""
        try:
            redis = await self._get_redis()

            # Convert to serializable format
            entry_dict = {
                "response": asdict(entry.response),
                "created_at": entry.created_at,
                "access_count": entry.access_count,
                "last_accessed": entry.last_accessed,
            }

            await redis.setex(f"llm_cache:{key}", ttl, json.dumps(entry_dict, default=str))
            self.stats["sets"] += 1

        except Exception as e:
            logger.error(f"Redis cache set error: {e}")
            self.stats["errors"] += 1

    async def delete(self, key: str) -> None:
        """Delete cache entry."""
        try:
            redis = await self._get_redis()
            await redis.delete(f"llm_cache:{key}")
            self.stats["deletes"] += 1
        except Exception as e:
            logger.error(f"Redis cache delete error: {e}")
            self.stats["errors"] += 1

    async def clear(self) -> None:
        """Clear all cache entries."""
        try:
            redis = await self._get_redis()
            keys = await redis.keys("llm_cache:*")
            if keys:
                await redis.delete(*keys)
        except Exception as e:
            logger.error(f"Redis cache clear error: {e}")
            self.stats["errors"] += 1

    async def get_stats(self) -> dict[str, Any]:
        """Get cache statistics."""
        total_requests = self.stats["hits"] + self.stats["misses"]
        hit_rate = self.stats["hits"] / total_requests if total_requests > 0 else 0.0

        stats = {
            **self.stats,
            "hit_rate": hit_rate,
            "backend": "redis",
        }

        try:
            redis = await self._get_redis()
            info = await redis.info("memory")
            stats["memory_usage"] = info.get("used_memory_human", "unknown")
        except Exception:
            pass

        return stats


class LLMResponseCache:
    """Main cache interface with automatic fallback."""

    def __init__(self, settings: Settings):
        self.settings = settings
        self.primary_backend: CacheBackend | None = None
        self.fallback_backend = InMemoryCache(max_size=500)  # Smaller fallback
        self.enabled = settings.enable_llm_cache

        if self.enabled:
            self._initialize_backends()

    def _initialize_backends(self) -> None:
        """Initialize cache backends."""
        try:
            # Try Redis first
            self.primary_backend = RedisCache(self.settings)
            logger.info("Initialized Redis cache as primary backend")
        except Exception as e:
            logger.warning(f"Redis cache unavailable, using memory cache: {e}")
            self.primary_backend = None

    def _generate_cache_key(self, prompt: str, system_prompt: str | None, **kwargs) -> str:
        """Generate cache key from request parameters."""
        # Create a deterministic key from all parameters that affect the response
        key_data = {
            "prompt": prompt,
            "system_prompt": system_prompt or "",
            "model": kwargs.get("model", self.settings.openai_model),
            "temperature": kwargs.get("temperature", self.settings.openai_temperature),
            "max_tokens": kwargs.get("max_tokens", self.settings.openai_max_tokens),
        }

        # Create hash of the key data
        key_str = json.dumps(key_data, sort_keys=True)
        return hashlib.sha256(key_str.encode()).hexdigest()[:32]

    async def get(self, prompt: str, system_prompt: str | None = None, **kwargs) -> LLMResponse | None:
        """Get cached response."""
        if not self.enabled:
            return None

        key = self._generate_cache_key(prompt, system_prompt, **kwargs)

        # Try primary backend first
        if self.primary_backend:
            try:
                entry = await self.primary_backend.get(key)
                if entry:
                    logger.debug(f"Cache hit (primary): {key[:8]}...")
                    return entry.response
            except Exception as e:
                logger.warning(f"Primary cache error: {e}")

        # Try fallback backend
        entry = await self.fallback_backend.get(key)
        if entry:
            logger.debug(f"Cache hit (fallback): {key[:8]}...")
            return entry.response

        logger.debug(f"Cache miss: {key[:8]}...")
        return None

    async def set(self, response: LLMResponse, prompt: str, system_prompt: str | None = None, **kwargs) -> None:
        """Cache response."""
        if not self.enabled:
            return

        key = self._generate_cache_key(prompt, system_prompt, **kwargs)
        entry = CacheEntry(
            response=response,
            created_at=time.time(),
        )

        # Cache in both backends
        if self.primary_backend:
            try:
                await self.primary_backend.set(key, entry, ttl=self.settings.redis_ttl)
            except Exception as e:
                logger.warning(f"Primary cache set error: {e}")

        await self.fallback_backend.set(key, entry, ttl=self.settings.redis_ttl)
        logger.debug(f"Cached response: {key[:8]}...")

    async def get_stats(self) -> dict[str, Any]:
        """Get combined cache statistics."""
        stats = {"enabled": self.enabled}

        if self.enabled:
            if self.primary_backend:
                stats["primary"] = await self.primary_backend.get_stats()
            stats["fallback"] = await self.fallback_backend.get_stats()

        return stats

    async def clear(self) -> None:
        """Clear all caches."""
        if not self.enabled:
            return

        if self.primary_backend:
            await self.primary_backend.clear()
        await self.fallback_backend.clear()
        logger.info("Cleared all caches")
