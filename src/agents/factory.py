"""
Agent Factory

This module provides factory classes for creating and managing agent instances
with proper dependency injection and configuration.
"""

import logging
from typing import Any

from ..config import Settings
from ..ingestion.integration_example import IntegratedIngestionPipeline
from .base import Agent, AgentType
from .context import ConversationContextManager, InMemoryContextStorage
from .exceptions import AgentConfigurationError
from .formatters import MarkdownFormatter
from .llm_client import LLMClientFactory

logger = logging.getLogger(__name__)


class AgentFactory:
    """Factory for creating agent instances with dependency injection."""

    def __init__(self, settings: Settings):
        self.settings = settings

        # Initialize shared dependencies
        self.llm_client = LLMClientFactory.create_client(settings)
        self.formatter = MarkdownFormatter()
        self.context_manager = ConversationContextManager(storage=InMemoryContextStorage(), max_context_size=50)
        self.ingestion_pipeline = IntegratedIngestionPipeline(settings)

        logger.info("Initialized agent factory with shared dependencies")

    def create_agent(self, agent_type: AgentType) -> Agent:
        """Create an agent instance of the specified type."""
        try:
            if agent_type == AgentType.ORCHESTRATOR:
                from ..orchestrator.orchestrator import OrchestratorAgent

                return OrchestratorAgent(
                    settings=self.settings,
                    llm_client=self.llm_client,
                    formatter=self.formatter,
                    context_manager=self.context_manager,
                    agent_factory=self,  # For creating sub-agents
                )

            if agent_type == AgentType.TECHNICAL_ARCHITECT:
                from ..technical_architect.agent import TechnicalArchitectAgent

                return TechnicalArchitectAgent(
                    settings=self.settings,
                    llm_client=self.llm_client,
                    formatter=self.formatter,
                    ingestion_pipeline=self.ingestion_pipeline,
                )

            if agent_type == AgentType.TASK_PLANNER:
                from ..task_planner.agent import TaskPlannerAgent

                return TaskPlannerAgent(
                    llm_client=self.llm_client,
                    ingestion_pipeline=self.ingestion_pipeline,
                    settings=self.settings,
                )

            if agent_type == AgentType.RAG_RETRIEVAL:
                from .rag_agent import RAGRetrievalAgent

                return RAGRetrievalAgent(
                    settings=self.settings,
                    formatter=self.formatter,
                    ingestion_pipeline=self.ingestion_pipeline,
                )

            raise AgentConfigurationError(f"Unknown agent type: {agent_type}", agent_type=agent_type.value)

        except ImportError as e:
            raise AgentConfigurationError(
                f"Failed to import agent class for {agent_type.value}: {e}",
                agent_type=agent_type.value,
                cause=e,
            ) from e
        except Exception as e:
            raise AgentConfigurationError(
                f"Failed to create agent {agent_type.value}: {e}",
                agent_type=agent_type.value,
                cause=e,
            ) from e

    def get_available_agents(self) -> dict[AgentType, dict[str, Any]]:
        """Get information about available agents."""
        return {
            AgentType.ORCHESTRATOR: {
                "name": "Orchestrator Agent",
                "description": "Routes queries and coordinates multi-agent workflows",
                "capabilities": [
                    "Query classification",
                    "Agent routing",
                    "Response synthesis",
                    "Conversation management",
                ],
            },
            AgentType.TECHNICAL_ARCHITECT: {
                "name": "Technical Design Architect",
                "description": "Generates system designs and architecture documentation",
                "capabilities": [
                    "Architecture analysis",
                    "Design generation",
                    "Standards compliance",
                    "Pattern recognition",
                ],
            },
            AgentType.TASK_PLANNER: {
                "name": "Task Planner",
                "description": "Breaks down requirements into actionable tasks",
                "capabilities": [
                    "Requirement breakdown",
                    "Timeline generation",
                    "Dependency analysis",
                    "Resource planning",
                ],
            },
            AgentType.RAG_RETRIEVAL: {
                "name": "RAG Retrieval Agent",
                "description": "Performs direct retrieval and factual lookups",
                "capabilities": [
                    "Semantic search",
                    "Code lookup",
                    "Documentation retrieval",
                    "Factual queries",
                ],
            },
        }

    async def initialize_shared_services(self) -> None:
        """Initialize shared services that require async setup."""
        try:
            # Start context cleanup task
            await self.context_manager.start_cleanup_task(cleanup_interval_hours=6)
            logger.info("Started shared services")
        except Exception as e:
            logger.error(f"Failed to initialize shared services: {e}")
            raise

    async def shutdown_shared_services(self) -> None:
        """Shutdown shared services gracefully."""
        try:
            # Stop context cleanup task
            await self.context_manager.stop_cleanup_task()
            logger.info("Shutdown shared services")
        except Exception as e:
            logger.error(f"Error during shutdown: {e}")

    async def get_stats(self) -> dict[str, Any]:
        """Get factory and shared service statistics."""
        return {
            "llm_client": await self.llm_client.get_stats(),
            "context_manager": await self.context_manager.get_stats(),
            "settings": {
                "model": self.settings.openai_model,
                "temperature": self.settings.openai_temperature,
                "max_tokens": self.settings.openai_max_tokens,
            },
        }


class AgentRegistry:
    """Registry for managing active agent instances."""

    def __init__(self, factory: AgentFactory):
        self.factory = factory
        self._agents: dict[AgentType, Agent] = {}
        logger.info("Initialized agent registry")

    def get_agent(self, agent_type: AgentType) -> Agent:
        """Get or create an agent instance."""
        if agent_type not in self._agents:
            self._agents[agent_type] = self.factory.create_agent(agent_type)
            logger.info(f"Created new agent instance: {agent_type.value}")

        return self._agents[agent_type]

    def get_all_agents(self) -> dict[AgentType, Agent]:
        """Get all registered agent instances."""
        return self._agents.copy()

    def remove_agent(self, agent_type: AgentType) -> bool:
        """Remove an agent instance from the registry."""
        if agent_type in self._agents:
            del self._agents[agent_type]
            logger.info(f"Removed agent instance: {agent_type.value}")
            return True
        return False

    def clear_all_agents(self) -> None:
        """Clear all agent instances."""
        self._agents.clear()
        logger.info("Cleared all agent instances")

    async def get_registry_stats(self) -> dict[str, Any]:
        """Get registry statistics."""
        agent_stats = {}
        for agent_type, agent in self._agents.items():
            agent_stats[agent_type.value] = agent.get_stats()

        return {
            "active_agents": list(self._agents.keys()),
            "agent_count": len(self._agents),
            "agent_stats": agent_stats,
            "factory_stats": await self.factory.get_stats(),
        }
