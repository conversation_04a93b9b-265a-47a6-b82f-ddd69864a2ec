"""
Base Agent Classes and Interfaces

This module defines the abstract base classes and data structures for the agent system,
following SOLID principles for extensibility and maintainability.
"""

from abc import ABC, abstractmethod
import asyncio
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import logging
from typing import Any
from uuid import uuid4

from ..ingestion.base import SearchResult
from .exceptions import AgentTimeoutError

logger = logging.getLogger(__name__)


class AgentType(Enum):
    """Enumeration of available agent types."""

    ORCHESTRATOR = "orchestrator"
    TECHNICAL_ARCHITECT = "technical_architect"
    TASK_PLANNER = "task_planner"
    RAG_RETRIEVAL = "rag_retrieval"


class MessageRole(Enum):
    """Enumeration of message roles in conversation."""

    USER = "user"
    ASSISTANT = "assistant"
    SYSTEM = "system"


@dataclass
class Message:
    """Represents a single message in a conversation."""

    role: MessageRole
    content: str
    timestamp: datetime = field(default_factory=datetime.now)
    metadata: dict[str, Any] = field(default_factory=dict)
    message_id: str = field(default_factory=lambda: str(uuid4()))


@dataclass
class ConversationContext:
    """Manages conversation state and history."""

    session_id: str
    user_id: str | None = None
    conversation_history: list[Message] = field(default_factory=list)
    current_repository: str | None = None
    active_agents: set[AgentType] = field(default_factory=set)
    shared_state: dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.now)
    last_updated: datetime = field(default_factory=datetime.now)

    def add_message(self, message: Message) -> None:
        """Add a message to the conversation history."""
        self.conversation_history.append(message)
        self.last_updated = datetime.now()

    def get_recent_messages(self, count: int = 10) -> list[Message]:
        """Get the most recent messages."""
        return self.conversation_history[-count:] if self.conversation_history else []

    def get_context_summary(self) -> str:
        """Get a summary of the conversation context."""
        recent_messages = self.get_recent_messages(5)
        if not recent_messages:
            return "No conversation history"

        summary_parts = []
        for msg in recent_messages:
            role = msg.role.value.title()
            content_preview = msg.content[:100] + "..." if len(msg.content) > 100 else msg.content
            summary_parts.append(f"{role}: {content_preview}")

        return "\n".join(summary_parts)


@dataclass
class AgentContext:
    """Context information passed to agents for processing."""

    conversation_context: ConversationContext
    repository_context: dict[str, Any] | None = None
    search_results: list[SearchResult] = field(default_factory=list)
    previous_agent_outputs: dict[AgentType, "AgentResponse"] = field(default_factory=dict)
    execution_metadata: dict[str, Any] = field(default_factory=dict)

    def add_search_results(self, results: list[SearchResult]) -> None:
        """Add search results to the context."""
        self.search_results.extend(results)

    def get_relevant_content(self, max_chunks: int = 10) -> str:
        """Get relevant content from search results."""
        if not self.search_results:
            return "No relevant content found."

        content_parts = []
        for i, result in enumerate(self.search_results[:max_chunks]):
            file_path = result.chunk.file_metadata.file_path
            content = result.content[:500] + "..." if len(result.content) > 500 else result.content
            score = result.similarity_score

            content_parts.append(f"[{i+1}] {file_path} (Score: {score:.3f})\n{content}\n")

        return "\n".join(content_parts)


@dataclass
class AgentResponse:
    """Response from an agent."""

    agent_type: AgentType
    content: str
    confidence: float = 1.0
    sources: list[str] = field(default_factory=list)
    metadata: dict[str, Any] = field(default_factory=dict)
    processing_time: float | None = None
    timestamp: datetime = field(default_factory=datetime.now)

    def add_source(self, source: str) -> None:
        """Add a source citation to the response."""
        if source not in self.sources:
            self.sources.append(source)

    def format_sources(self) -> str:
        """Format sources for display."""
        if not self.sources:
            return ""

        return "\n\n**Sources:**\n" + "\n".join(f"- {source}" for source in self.sources)


class Agent(ABC):
    """Abstract base class for all agents."""

    def __init__(self, agent_type: AgentType, settings: Any):
        self.agent_type = agent_type
        self.settings = settings
        self.logger = logging.getLogger(f"{__name__}.{agent_type.value}")

        # Statistics
        self._stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_processing_time": 0.0,
            "total_processing_time": 0.0,
        }

    @abstractmethod
    async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
        """Process a query and return a response."""
        pass

    @abstractmethod
    def can_handle_query(self, query: str, context: AgentContext) -> float:
        """Return confidence score (0-1) for handling this query."""
        pass

    async def _execute_with_timeout(self, coro, timeout_seconds: float = 30.0) -> Any:
        """Execute a coroutine with timeout."""
        try:
            return await asyncio.wait_for(coro, timeout=timeout_seconds)
        except TimeoutError as e:
            raise AgentTimeoutError(
                f"Agent {self.agent_type.value} timed out",
                agent_type=self.agent_type.value,
                timeout_seconds=timeout_seconds,
            ) from e

    def _update_stats(self, processing_time: float, success: bool) -> None:
        """Update agent statistics."""
        self._stats["total_requests"] += 1
        self._stats["total_processing_time"] += processing_time

        if success:
            self._stats["successful_requests"] += 1
        else:
            self._stats["failed_requests"] += 1

        # Update average
        if self._stats["total_requests"] > 0:
            self._stats["average_processing_time"] = (
                self._stats["total_processing_time"] / self._stats["total_requests"]
            )

    def get_stats(self) -> dict[str, Any]:
        """Get agent statistics."""
        return self._stats.copy()

    def reset_stats(self) -> None:
        """Reset agent statistics."""
        self._stats = {
            "total_requests": 0,
            "successful_requests": 0,
            "failed_requests": 0,
            "average_processing_time": 0.0,
            "total_processing_time": 0.0,
        }

    def __str__(self) -> str:
        return f"{self.__class__.__name__}({self.agent_type.value})"

    def __repr__(self) -> str:
        return self.__str__()
