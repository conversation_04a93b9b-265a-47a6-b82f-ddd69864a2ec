"""
LLM Client for Agent System

This module provides a unified interface for LLM interactions across all agents,
with consistent error handling, rate limiting, and response formatting.
"""

import asyncio
import logging
import time
from typing import Any

from openai import AsyncOpenAI

from ..config import Settings
from .cache import LLMResponseCache
from .exceptions import <PERSON><PERSON><PERSON><PERSON>rror
from .model_selector import ModelSelector
from .models import LLMResponse

logger = logging.getLogger(__name__)



class LLMClient:
    """Unified LLM client for agent interactions."""

    def __init__(self, settings: Settings):
        """Initialize LLM client."""
        self.settings = settings
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.model = settings.openai_model
        self.temperature = settings.openai_temperature
        self.max_tokens = settings.openai_max_tokens

        # Initialize cache and model selector
        self.cache = LLMResponseCache(settings)
        self.model_selector = ModelSelector(settings)

        # Rate limiting
        self._last_request_time = 0.0
        self._min_request_interval = 0.1  # 100ms between requests

        # Statistics
        self._stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
        }

        logger.info(f"Initialized LLM client with model {self.model}, cache enabled: {settings.enable_llm_cache}")

    async def generate_response(
        self,
        prompt: str,
        system_prompt: str | None = None,
        temperature: float | None = None,
        max_tokens: int | None = None,
        context: dict[str, Any] | None = None,
    ) -> LLMResponse:
        """Generate response from LLM with caching."""
        start_time = time.time()

        try:
            # Use provided parameters or defaults
            temp = temperature if temperature is not None else self.temperature
            max_tok = max_tokens if max_tokens is not None else self.max_tokens

            # Select optimal model based on query complexity
            selected_model, complexity_analysis = self.model_selector.select_model(
                query=prompt,
                context=context
            )

            # Check cache first (using selected model)
            cached_response = await self.cache.get(
                prompt=prompt,
                system_prompt=system_prompt,
                model=selected_model,
                temperature=temp,
                max_tokens=max_tok,
            )

            if cached_response:
                # Update cache hit stats
                self._stats["cache_hits"] += 1
                self._stats["total_requests"] += 1

                processing_time = time.time() - start_time
                logger.debug(f"Cache hit! Response retrieved in {processing_time:.3f}s")

                # Return cached response with updated processing time
                cached_response.processing_time = processing_time
                cached_response.metadata["cached"] = True
                cached_response.metadata["cache_retrieval_time"] = processing_time

                return cached_response

            # Cache miss - generate new response
            self._stats["cache_misses"] += 1

            # Rate limiting
            await self._enforce_rate_limit()

            # Prepare messages
            messages = []
            if system_prompt:
                messages.append({"role": "system", "content": system_prompt})
            messages.append({"role": "user", "content": prompt})

            # Make API call with selected model
            response = await self.client.chat.completions.create(
                model=selected_model,
                messages=messages,
                temperature=temp,
                max_tokens=max_tok,
                timeout=60.0,
            )

            processing_time = time.time() - start_time

            # Extract response content
            content = response.choices[0].message.content or ""

            # Create response object
            llm_response = LLMResponse(
                content=content,
                model=selected_model,
                usage=response.usage.model_dump() if response.usage else {},
                processing_time=processing_time,
                metadata={
                    "temperature": temp,
                    "max_tokens": max_tok,
                    "context": context or {},
                    "cached": False,
                    "complexity_analysis": {
                        "score": complexity_analysis.complexity_score,
                        "reasoning": complexity_analysis.reasoning,
                        "selected_model": selected_model,
                    },
                },
            )

            # Cache the response (using selected model)
            await self.cache.set(
                response=llm_response,
                prompt=prompt,
                system_prompt=system_prompt,
                model=selected_model,
                temperature=temp,
                max_tokens=max_tok,
            )

            # Update statistics
            self._update_stats(llm_response, success=True)

            logger.debug(f"LLM response generated in {processing_time:.2f}s, " f"{llm_response.token_count} tokens")

            return llm_response

        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(None, success=False)

            logger.error(f"LLM client error: {e}")
            raise LLMClientError(f"Failed to generate LLM response: {e}", model=self.model, cause=e) from e

    async def _enforce_rate_limit(self) -> None:
        """Enforce rate limiting between requests."""
        current_time = time.time()
        time_since_last = current_time - self._last_request_time

        if time_since_last < self._min_request_interval:
            sleep_time = self._min_request_interval - time_since_last
            await asyncio.sleep(sleep_time)

        self._last_request_time = time.time()

    def _update_stats(self, response: LLMResponse | None, success: bool) -> None:
        """Update client statistics."""
        self._stats["total_requests"] += 1

        if success and response:
            self._stats["total_tokens"] += response.token_count
            self._stats["total_cost"] += response.cost_estimate

            # Update average response time
            total_time = (
                self._stats["average_response_time"] * (self._stats["total_requests"] - 1) + response.processing_time
            )
            self._stats["average_response_time"] = total_time / self._stats["total_requests"]
        else:
            self._stats["failed_requests"] += 1

    async def get_stats(self) -> dict[str, Any]:
        """Get client statistics including cache stats."""
        stats = self._stats.copy()

        # Add cache hit rate
        total_requests = stats["cache_hits"] + stats["cache_misses"]
        if total_requests > 0:
            stats["cache_hit_rate"] = stats["cache_hits"] / total_requests
        else:
            stats["cache_hit_rate"] = 0.0

        # Add cache backend stats
        stats["cache"] = await self.cache.get_stats()

        # Add model selection stats
        stats["model_selection"] = self.model_selector.get_stats()

        return stats

    def reset_stats(self) -> None:
        """Reset client statistics."""
        self._stats = {
            "total_requests": 0,
            "total_tokens": 0,
            "total_cost": 0.0,
            "failed_requests": 0,
            "average_response_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
        }


class LLMClientFactory:
    """Factory for creating LLM clients."""

    @staticmethod
    def create_client(settings: Settings) -> LLMClient:
        """Create an LLM client based on settings."""
        # For now, only OpenAI is supported
        # Future: Add support for other providers (Anthropic, Cohere, etc.)
        return LLMClient(settings)

    @staticmethod
    def get_available_providers() -> list[str]:
        """Get list of available LLM providers."""
        return ["openai"]  # Add more as they're implemented
