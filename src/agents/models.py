"""
Agent Models and Data Structures

This module contains shared data models used across the agent system
to avoid circular imports.
"""

from dataclasses import dataclass, field
from typing import Any


@dataclass
class LLMResponse:
    """Response from an LLM."""

    content: str
    model: str
    usage: dict[str, Any] = field(default_factory=dict)
    processing_time: float = 0.0
    metadata: dict[str, Any] = field(default_factory=dict)

    @property
    def token_count(self) -> int:
        """Get total token count from usage."""
        return int(self.usage.get("total_tokens", 0))

    @property
    def prompt_tokens(self) -> int:
        """Get prompt token count from usage."""
        return int(self.usage.get("prompt_tokens", 0))

    @property
    def completion_tokens(self) -> int:
        """Get completion token count from usage."""
        return int(self.usage.get("completion_tokens", 0))

    def estimate_cost(self) -> float:
        """Estimate cost based on token usage and model."""
        # Simplified cost estimation (would need actual pricing)
        if "gpt-5" in self.model.lower() and "mini" not in self.model.lower():
            # GPT-5 pricing (estimated based on GPT-4 pricing)
            prompt_cost = self.prompt_tokens * 0.00003  # $0.03 per 1K tokens
            completion_cost = self.completion_tokens * 0.00006  # $0.06 per 1K tokens
        elif "gpt-5-mini" in self.model.lower():
            # GPT-5-mini pricing (estimated based on GPT-3.5-turbo pricing)
            prompt_cost = self.prompt_tokens * 0.0000015  # $0.0015 per 1K tokens
            completion_cost = self.completion_tokens * 0.000002  # $0.002 per 1K tokens
        elif "gpt-4" in self.model.lower():
            # Legacy GPT-4 pricing (approximate)
            prompt_cost = self.prompt_tokens * 0.00003  # $0.03 per 1K tokens
            completion_cost = self.completion_tokens * 0.00006  # $0.06 per 1K tokens
        else:
            # Default/legacy GPT-3.5-turbo pricing (approximate)
            prompt_cost = self.prompt_tokens * 0.0000015  # $0.0015 per 1K tokens
            completion_cost = self.completion_tokens * 0.000002  # $0.002 per 1K tokens

        return prompt_cost + completion_cost
