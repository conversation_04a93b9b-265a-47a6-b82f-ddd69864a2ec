"""
Dependency Analysis Engine

This module identifies task dependencies, builds dependency graphs, analyzes critical paths,
and detects potential bottlenecks in implementation plans.
"""

import logging
import re
from typing import Any

from .models import (
    DependencyGraph,
    DependencyType,
    RiskLevel,
    Task,
    TaskType,
)

logger = logging.getLogger(__name__)


class DependencyAnalyzer:
    """Analyzes task dependencies and builds dependency graphs."""

    def __init__(self):
        """Initialize the dependency analyzer."""
        # Define implicit dependency rules based on task types and patterns
        self.implicit_dependencies = {
            # Analysis must come before design
            (TaskType.ANALYSIS, TaskType.DESIGN): DependencyType.FINISH_TO_START,
            # Design must come before implementation
            (TaskType.DESIGN, TaskType.IMPLEMENTATION): DependencyType.FINISH_TO_START,
            # Implementation must come before testing
            (TaskType.IMPLEMENTATION, TaskType.TESTING): DependencyType.FINISH_TO_START,
            # Testing must come before deployment
            (TaskType.TESTING, TaskType.DEPLOYMENT): DependencyType.FINISH_TO_START,
            # Implementation should come before documentation
            (TaskType.IMPLEMENTATION, TaskType.DOCUMENTATION): DependencyType.FINISH_TO_START,
        }

        # Define task patterns that suggest dependencies
        self.dependency_patterns = {
            "component_creation": [
                r"create.*component",
                r"implement.*component",
                r"build.*component",
            ],
            "api_creation": [
                r"create.*api",
                r"implement.*api",
                r"build.*api",
                r"add.*endpoint",
            ],
            "database_setup": [
                r"setup.*database",
                r"create.*schema",
                r"migrate.*database",
            ],
            "testing_setup": [
                r"setup.*test",
                r"create.*test",
                r"write.*test",
            ],
        }

        logger.info("Initialized dependency analyzer")

    def analyze_dependencies(self, tasks: list[Task]) -> DependencyGraph:
        """Analyze task dependencies and build a dependency graph.

        Args:
            tasks: List of tasks to analyze

        Returns:
            DependencyGraph with analyzed dependencies
        """
        logger.info(f"Analyzing dependencies for {len(tasks)} tasks")

        # Create dependency graph
        graph = DependencyGraph()

        # Add all tasks to the graph
        for task in tasks:
            graph.add_task(task)

        # Add explicit dependencies from tasks
        self._add_explicit_dependencies(graph, tasks)

        # Infer implicit dependencies
        self._add_implicit_dependencies(graph, tasks)

        # Analyze and add pattern-based dependencies
        self._add_pattern_dependencies(graph, tasks)

        # Calculate critical path
        graph.critical_path = self._calculate_critical_path(graph)
        graph.total_duration = self._calculate_total_duration(graph)

        # Validate the graph
        self._validate_dependency_graph(graph)

        logger.info(f"Dependency analysis complete: {len(graph.edges)} dependencies, critical path: {len(graph.critical_path)} tasks")
        return graph

    def detect_bottlenecks(self, graph: DependencyGraph) -> list[dict[str, Any]]:
        """Detect potential bottlenecks in the dependency graph.

        Args:
            graph: Dependency graph to analyze

        Returns:
            List of bottleneck descriptions
        """
        logger.info("Detecting bottlenecks in dependency graph")
        bottlenecks = []

        # Find tasks with many dependencies (fan-in)
        for task_id, task in graph.nodes.items():
            predecessors = graph.get_predecessors(task_id)
            if len(predecessors) > 3:  # Threshold for high fan-in
                bottlenecks.append({
                    "type": "high_fan_in",
                    "task_id": task_id,
                    "task_title": task.title,
                    "description": f"Task has {len(predecessors)} dependencies, may cause delays",
                    "severity": "medium" if len(predecessors) <= 5 else "high",
                    "predecessors": predecessors,
                    "recommendation": "Consider breaking down dependencies or parallelizing work"
                })

        # Find tasks that many others depend on (fan-out)
        for task_id, task in graph.nodes.items():
            successors = graph.get_successors(task_id)
            if len(successors) > 4:  # Threshold for high fan-out
                bottlenecks.append({
                    "type": "high_fan_out",
                    "task_id": task_id,
                    "task_title": task.title,
                    "description": f"Task blocks {len(successors)} other tasks",
                    "severity": "high",
                    "successors": successors,
                    "recommendation": "Prioritize this task and consider adding buffer time"
                })

        # Find long chains of dependencies
        chains = self._find_dependency_chains(graph)
        for chain in chains:
            if len(chain) > 5:  # Threshold for long chain
                bottlenecks.append({
                    "type": "long_chain",
                    "chain": chain,
                    "description": f"Long dependency chain of {len(chain)} tasks",
                    "severity": "medium",
                    "recommendation": "Look for opportunities to parallelize or reduce dependencies"
                })

        # Find critical path bottlenecks
        if graph.critical_path:
            critical_tasks = [graph.nodes[task_id] for task_id in graph.critical_path]
            high_effort_critical = [task for task in critical_tasks if task.estimate_hours > 40]

            if high_effort_critical:
                bottlenecks.append({
                    "type": "critical_path_bottleneck",
                    "tasks": [{"id": task.id, "title": task.title, "hours": task.estimate_hours} for task in high_effort_critical],
                    "description": f"{len(high_effort_critical)} high-effort tasks on critical path",
                    "severity": "high",
                    "recommendation": "Break down large tasks or add resources to critical path"
                })

        logger.info(f"Detected {len(bottlenecks)} potential bottlenecks")
        return bottlenecks

    def suggest_optimizations(self, graph: DependencyGraph) -> list[dict[str, Any]]:
        """Suggest optimizations for the dependency graph.

        Args:
            graph: Dependency graph to optimize

        Returns:
            List of optimization suggestions
        """
        logger.info("Analyzing dependency graph for optimization opportunities")
        optimizations = []

        # Find tasks that could be parallelized
        parallel_opportunities = self._find_parallelization_opportunities(graph)
        if parallel_opportunities:
            optimizations.append({
                "type": "parallelization",
                "description": f"Found {len(parallel_opportunities)} groups of tasks that could run in parallel",
                "opportunities": parallel_opportunities,
                "potential_savings": self._calculate_parallelization_savings(graph, parallel_opportunities),
                "recommendation": "Consider running these tasks in parallel to reduce timeline"
            })

        # Find unnecessary dependencies
        unnecessary_deps = self._find_unnecessary_dependencies(graph)
        if unnecessary_deps:
            optimizations.append({
                "type": "dependency_reduction",
                "description": f"Found {len(unnecessary_deps)} potentially unnecessary dependencies",
                "dependencies": unnecessary_deps,
                "recommendation": "Review these dependencies to see if they can be removed"
            })

        # Find tasks that could be merged
        merge_opportunities = self._find_merge_opportunities(graph)
        if merge_opportunities:
            optimizations.append({
                "type": "task_merging",
                "description": f"Found {len(merge_opportunities)} opportunities to merge related tasks",
                "opportunities": merge_opportunities,
                "recommendation": "Consider merging small related tasks to reduce overhead"
            })

        logger.info(f"Generated {len(optimizations)} optimization suggestions")
        return optimizations

    def _add_explicit_dependencies(self, graph: DependencyGraph, tasks: list[Task]) -> None:
        """Add explicitly defined dependencies from tasks."""
        for task in tasks:
            for dependency in task.dependencies:
                if dependency.task_id in graph.nodes:
                    graph.add_dependency(dependency.task_id, task.id)

    def _add_implicit_dependencies(self, graph: DependencyGraph, tasks: list[Task]) -> None:
        """Add implicit dependencies based on task types."""
        # Group tasks by type
        tasks_by_type = {}
        for task in tasks:
            task_type = task.task_type
            if task_type not in tasks_by_type:
                tasks_by_type[task_type] = []
            tasks_by_type[task_type].append(task)

        # Add implicit dependencies between task types
        for (from_type, to_type), dep_type in self.implicit_dependencies.items():
            if from_type in tasks_by_type and to_type in tasks_by_type:
                for from_task in tasks_by_type[from_type]:
                    for to_task in tasks_by_type[to_type]:
                        # Only add if not already explicitly defined
                        if not any(dep.task_id == from_task.id for dep in to_task.dependencies):
                            # Add implicit dependency
                            to_task.add_dependency(from_task.id, dep_type)
                            graph.add_dependency(from_task.id, to_task.id)

    def _add_pattern_dependencies(self, graph: DependencyGraph, tasks: list[Task]) -> None:
        """Add dependencies based on task title/description patterns."""
        # Find tasks that match specific patterns
        pattern_tasks = self._categorize_tasks_by_patterns(tasks)

        # Add different types of pattern-based dependencies
        self._add_component_api_dependencies(graph, pattern_tasks)
        self._add_database_dependencies(graph, pattern_tasks, tasks)

    def _categorize_tasks_by_patterns(self, tasks: list[Task]) -> dict[str, list[Task]]:
        """Categorize tasks based on dependency patterns."""
        pattern_tasks = {}
        for pattern_name, patterns in self.dependency_patterns.items():
            pattern_tasks[pattern_name] = []
            for task in tasks:
                task_text = f"{task.title} {task.description}".lower()
                if any(re.search(pattern, task_text) for pattern in patterns):
                    pattern_tasks[pattern_name].append(task)
        return pattern_tasks

    def _add_component_api_dependencies(self, graph: DependencyGraph, pattern_tasks: dict[str, list[Task]]) -> None:
        """Add dependencies between component creation and API creation tasks."""
        if "component_creation" not in pattern_tasks or "api_creation" not in pattern_tasks:
            return

        for comp_task in pattern_tasks["component_creation"]:
            for api_task in pattern_tasks["api_creation"]:
                if self._should_add_component_dependency(comp_task, api_task):
                    api_task.add_dependency(comp_task.id)
                    graph.add_dependency(comp_task.id, api_task.id)

    def _should_add_component_dependency(self, comp_task: Task, api_task: Task) -> bool:
        """Check if API task should depend on component task."""
        # Check if API task mentions the component
        comp_name = comp_task.title.lower()
        api_text = f"{api_task.title} {api_task.description}".lower()

        # Check if component words appear in API task and dependency doesn't already exist
        has_component_reference = any(word in api_text for word in comp_name.split() if len(word) > 3)
        dependency_exists = any(dep.task_id == comp_task.id for dep in api_task.dependencies)

        return has_component_reference and not dependency_exists

    def _add_database_dependencies(self, graph: DependencyGraph, pattern_tasks: dict[str, list[Task]], all_tasks: list[Task]) -> None:
        """Add dependencies for database setup tasks."""
        if "database_setup" not in pattern_tasks:
            return

        for db_task in pattern_tasks["database_setup"]:
            for task in all_tasks:
                if self._should_add_database_dependency(db_task, task):
                    task.add_dependency(db_task.id)
                    graph.add_dependency(db_task.id, task.id)

    def _should_add_database_dependency(self, db_task: Task, task: Task) -> bool:
        """Check if task should depend on database setup task."""
        is_different_task = task.id != db_task.id
        mentions_database = "database" in f"{task.title} {task.description}".lower()
        dependency_exists = any(dep.task_id == db_task.id for dep in task.dependencies)

        return is_different_task and mentions_database and not dependency_exists

    def _calculate_critical_path(self, graph: DependencyGraph) -> list[str]:
        """Calculate the critical path through the dependency graph."""
        if not graph.nodes:
            return []

        # Calculate earliest and latest times for each task
        earliest_times = {}
        latest_times = {}

        # Forward pass - calculate earliest times
        sorted_tasks = self._topological_sort(list(graph.nodes.values()))
        for task in sorted_tasks:
            earliest_start = 0.0
            predecessors = graph.get_predecessors(task.id)

            for pred_id in predecessors:
                if pred_id in earliest_times:
                    pred_finish = earliest_times[pred_id]["finish"]
                    earliest_start = max(earliest_start, pred_finish)

            earliest_finish = earliest_start + task.estimate_hours
            earliest_times[task.id] = {"start": earliest_start, "finish": earliest_finish}

        # Find project end time
        if not earliest_times:
            return []

        project_end = max(times["finish"] for times in earliest_times.values())

        # Backward pass - calculate latest times
        for task in reversed(sorted_tasks):
            latest_finish = project_end
            successors = graph.get_successors(task.id)

            for succ_id in successors:
                if succ_id in latest_times:
                    succ_start = latest_times[succ_id]["start"]
                    latest_finish = min(latest_finish, succ_start)

            latest_start = latest_finish - task.estimate_hours
            latest_times[task.id] = {"start": latest_start, "finish": latest_finish}

        # Find critical tasks (where earliest = latest)
        critical_path = []
        for task in sorted_tasks:
            task_id = task.id
            if (abs(earliest_times[task_id]["start"] - latest_times[task_id]["start"]) < 0.01 and
                abs(earliest_times[task_id]["finish"] - latest_times[task_id]["finish"]) < 0.01):
                critical_path.append(task_id)

        return critical_path

    def _calculate_total_duration(self, graph: DependencyGraph) -> float:
        """Calculate the total project duration."""
        if not graph.nodes:
            return 0.0

        # Calculate the longest path through the graph
        sorted_tasks = self._topological_sort(list(graph.nodes.values()))
        earliest_times = {}

        for task in sorted_tasks:
            earliest_start = 0.0
            predecessors = graph.get_predecessors(task.id)

            for pred_id in predecessors:
                if pred_id in earliest_times:
                    pred_finish = earliest_times[pred_id]["finish"]
                    earliest_start = max(earliest_start, pred_finish)

            earliest_finish = earliest_start + task.estimate_hours
            earliest_times[task.id] = {"start": earliest_start, "finish": earliest_finish}

        return max(times["finish"] for times in earliest_times.values()) if earliest_times else 0.0

    def _validate_dependency_graph(self, graph: DependencyGraph) -> None:
        """Validate the dependency graph for cycles and other issues."""
        # Check for cycles
        if graph.has_cycle():
            logger.warning("Dependency graph contains cycles")
            # Add risks to tasks involved in cycles
            self._add_cycle_risks(graph)

        # Check for orphaned tasks
        orphaned_tasks = []
        for task_id, _task in graph.nodes.items():
            predecessors = graph.get_predecessors(task_id)
            successors = graph.get_successors(task_id)
            if not predecessors and not successors and len(graph.nodes) > 1:
                orphaned_tasks.append(task_id)

        if orphaned_tasks:
            logger.warning(f"Found {len(orphaned_tasks)} orphaned tasks with no dependencies")

    def _add_cycle_risks(self, graph: DependencyGraph) -> None:
        """Add risks to tasks that are part of dependency cycles."""
        # This is a simplified cycle detection - in practice, you'd want more sophisticated cycle analysis
        for task_id, task in graph.nodes.items():
            # Check if task can reach itself through dependencies
            if self._can_reach(graph, task_id, task_id, set()):
                task.add_risk(
                    description="Task is part of a dependency cycle",
                    level=RiskLevel.HIGH,
                    probability=1.0,
                    impact="May cause scheduling conflicts and delays",
                    mitigation="Review and resolve circular dependencies"
                )

    def _can_reach(self, graph: DependencyGraph, start: str, target: str, visited: set[str]) -> bool:
        """Check if start task can reach target task through dependencies."""
        if start in visited:
            return False

        visited.add(start)
        successors = graph.get_successors(start)

        for successor in successors:
            if successor == target:
                return True
            if self._can_reach(graph, successor, target, visited.copy()):
                return True

        return False

    def _topological_sort(self, tasks: list[Task]) -> list[Task]:
        """Sort tasks in dependency order."""
        # Build in-degree count
        {task.id: task for task in tasks}
        in_degree = {task.id: 0 for task in tasks}

        for task in tasks:
            for dependency in task.dependencies:
                if dependency.task_id in in_degree:
                    in_degree[task.id] += 1

        # Find tasks with no dependencies
        queue = [task for task in tasks if in_degree[task.id] == 0]
        sorted_tasks = []

        while queue:
            current_task = queue.pop(0)
            sorted_tasks.append(current_task)

            # Remove outgoing edges
            for task in tasks:
                for dependency in task.dependencies:
                    if dependency.task_id == current_task.id:
                        in_degree[task.id] -= 1
                        if in_degree[task.id] == 0:
                            queue.append(task)

        return sorted_tasks

    def _find_dependency_chains(self, graph: DependencyGraph) -> list[list[str]]:
        """Find long chains of dependencies."""
        chains = []
        visited = set()

        for task_id in graph.nodes:
            if task_id not in visited:
                chain = self._build_chain(graph, task_id, visited)
                if len(chain) > 1:
                    chains.append(chain)

        return chains

    def _build_chain(self, graph: DependencyGraph, start_task: str, visited: set[str]) -> list[str]:
        """Build a dependency chain starting from a task."""
        chain = [start_task]
        visited.add(start_task)

        successors = graph.get_successors(start_task)
        if len(successors) == 1 and successors[0] not in visited:
            # Continue the chain if there's exactly one successor
            chain.extend(self._build_chain(graph, successors[0], visited))

        return chain

    def _find_parallelization_opportunities(self, graph: DependencyGraph) -> list[list[str]]:
        """Find groups of tasks that could run in parallel."""
        # Find tasks at the same "level" (same distance from start)
        levels = {}

        # Calculate levels using BFS
        start_tasks = [task_id for task_id in graph.nodes if not graph.get_predecessors(task_id)]

        for start_task in start_tasks:
            queue = [(start_task, 0)]
            visited = set()

            while queue:
                task_id, level = queue.pop(0)
                if task_id in visited:
                    continue

                visited.add(task_id)
                if level not in levels:
                    levels[level] = []
                levels[level].append(task_id)

                for successor in graph.get_successors(task_id):
                    queue.append((successor, level + 1))

        # Return levels with multiple tasks as parallelization opportunities
        return [task_list for task_list in levels.values() if len(task_list) > 1]

    def _calculate_parallelization_savings(self, graph: DependencyGraph, opportunities: list[list[str]]) -> float:
        """Calculate potential time savings from parallelization."""
        total_savings = 0.0

        for group in opportunities:
            if len(group) > 1:
                # Calculate current sequential time
                sequential_time = sum(graph.nodes[task_id].estimate_hours for task_id in group)

                # Calculate parallel time (longest task in group)
                parallel_time = max(graph.nodes[task_id].estimate_hours for task_id in group)

                savings = sequential_time - parallel_time
                total_savings += savings

        return total_savings

    def _find_unnecessary_dependencies(self, graph: DependencyGraph) -> list[dict[str, str]]:
        """Find potentially unnecessary dependencies."""
        # This is a simplified heuristic - in practice, you'd need domain knowledge
        unnecessary = []

        for task_id, _task in graph.nodes.items():
            predecessors = graph.get_predecessors(task_id)

            # Check for transitive dependencies that might be unnecessary
            for pred_id in predecessors:
                pred_predecessors = graph.get_predecessors(pred_id)
                for indirect_pred in pred_predecessors:
                    if indirect_pred in predecessors:
                        # Direct dependency exists where transitive would suffice
                        unnecessary.append({
                            "from_task": indirect_pred,
                            "to_task": task_id,
                            "reason": f"Transitive dependency through {pred_id}",
                            "recommendation": "Consider removing direct dependency"
                        })

        return unnecessary

    def _find_merge_opportunities(self, graph: DependencyGraph) -> list[list[str]]:
        """Find opportunities to merge related tasks."""
        merge_opportunities = []

        # Find small tasks that could be merged
        small_tasks = [task_id for task_id, task in graph.nodes.items() if task.estimate_hours < 2.0]

        # Group small tasks by type and dependencies
        for i, task_id in enumerate(small_tasks):
            task = graph.nodes[task_id]
            similar_tasks = [task_id]

            for _j, other_task_id in enumerate(small_tasks[i+1:], i+1):
                other_task = graph.nodes[other_task_id]

                # Check if tasks are similar (same type, similar dependencies)
                if (task.task_type == other_task.task_type and
                    set(graph.get_predecessors(task_id)) == set(graph.get_predecessors(other_task_id))):
                    similar_tasks.append(other_task_id)

            if len(similar_tasks) > 1:
                merge_opportunities.append(similar_tasks)

        return merge_opportunities
