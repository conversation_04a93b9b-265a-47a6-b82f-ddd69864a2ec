"""
Task Planner Agent

This module implements the main Task Planner agent that coordinates requirement breakdown,
timeline generation, dependency analysis, and risk assessment for comprehensive project planning.
"""

import json
import logging
import re
import time
from typing import Any

from ..agents.base import Agent, AgentContext, AgentResponse, AgentType
from ..agents.exceptions import AgentError
from ..agents.formatters import MarkdownFormatter
from ..agents.llm_client import LLMClient
from ..config import Settings
from ..ingestion.integration_example import IntegratedIngestionPipeline
from .breakdown import RequirementBreakdownEngine
from .dependencies import DependencyAnalyzer
from .models import TaskPlan
from .risk_analyzer import RiskAnalyzer
from .timeline import TimelineGenerator

logger = logging.getLogger(__name__)


class TaskPlannerAgent(Agent):
    """Task Planner Agent for breaking down requirements into actionable development tasks."""

    def __init__(
        self,
        llm_client: LLMClient,
        ingestion_pipeline: IntegratedIngestionPipeline,
        settings: Settings,
    ):
        """Initialize the Task Planner agent.

        Args:
            llm_client: LLM client for generating responses
            ingestion_pipeline: Pipeline for searching relevant content
            settings: Application settings
        """
        super().__init__(AgentType.TASK_PLANNER, settings)

        self.llm_client = llm_client

        self.ingestion_pipeline = ingestion_pipeline
        self.formatter = MarkdownFormatter()

        # Initialize task planning components
        self.breakdown_engine = RequirementBreakdownEngine()

        # Get task planner configuration
        task_planner_config = getattr(settings, 'task_planner', {})
        if callable(task_planner_config):
            task_planner_config = task_planner_config()

        self.timeline_generator = TimelineGenerator(
            working_hours_per_day=task_planner_config.get("working_hours_per_day", 8.0),
            working_days_per_week=task_planner_config.get("working_days_per_week", 5),
        )
        self.dependency_analyzer = DependencyAnalyzer()
        self.risk_analyzer = RiskAnalyzer()

        # Query classification patterns
        self.query_patterns = {
            "breakdown": [
                r"break\s+down",
                r"decompose",
                r"split\s+into\s+tasks",
                r"create\s+tasks",
                r"task\s+list",
            ],
            "timeline": [
                r"timeline",
                r"schedule",
                r"when\s+will",
                r"how\s+long",
                r"estimate",
                r"duration",
            ],
            "planning": [
                r"plan",
                r"planning",
                r"project\s+plan",
                r"implementation\s+plan",
                r"roadmap",
            ],
            "dependencies": [
                r"dependencies",
                r"dependency",
                r"depends\s+on",
                r"order",
                r"sequence",
            ],
            "risks": [
                r"risk",
                r"risks",
                r"challenges",
                r"issues",
                r"problems",
            ],
        }

        logger.info("Initialized Task Planner agent")

    def can_handle_query(self, query: str, context: AgentContext) -> float:
        """Determine if this agent can handle the given query.

        Args:
            query: User query to evaluate
            context: Current conversation context

        Returns:
            Confidence score (0.0 to 1.0)
        """
        query_lower = query.lower()

        # High confidence keywords
        high_confidence_keywords = [
            "task", "tasks", "plan", "planning", "breakdown", "timeline",
            "schedule", "estimate", "dependencies", "roadmap", "project plan",
            "implementation plan", "work breakdown", "effort estimation",
        ]

        # Medium confidence keywords
        medium_confidence_keywords = [
            "how long", "when will", "steps", "phases", "milestones",
            "deliverables", "requirements", "scope", "organize",
        ]

        # Calculate confidence based on keyword matches
        confidence = 0.0

        # Check for high confidence keywords
        high_matches = sum(1 for keyword in high_confidence_keywords if keyword in query_lower)
        confidence += min(high_matches * 0.3, 0.9)

        # Check for medium confidence keywords
        medium_matches = sum(1 for keyword in medium_confidence_keywords if keyword in query_lower)
        confidence += min(medium_matches * 0.2, 0.6)

        # Check for query patterns
        for _pattern_type, patterns in self.query_patterns.items():
            if any(re.search(pattern, query_lower) for pattern in patterns):
                confidence += 0.25

        # Boost confidence if query mentions specific planning concepts
        planning_concepts = ["5-phase", "methodology", "agile", "waterfall", "sprint"]
        if any(concept in query_lower for concept in planning_concepts):
            confidence += 0.2

        # Cap confidence at 1.0
        confidence = min(confidence, 1.0)

        logger.debug(f"Task planner confidence for query '{query[:50]}...': {confidence:.2f}")
        return confidence

    async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
        """Process a task planning query.

        Args:
            query: User query to process
            context: Current conversation context

        Returns:
            Agent response with task planning results
        """
        start_time = time.time()

        try:
            logger.info(f"Processing task planning query: {query[:100]}...")

            # Classify query type
            query_type = self._classify_query_type(query)

            # Perform search for relevant context
            search_results = await self._perform_search(query, context)

            # Extract requirements from query and context
            requirements = self._extract_requirements(query, search_results)

            # Generate task plan based on query type
            if query_type == "breakdown":
                response_content = await self._generate_breakdown_response(query, requirements, search_results)
            elif query_type == "timeline":
                response_content = await self._generate_timeline_response(query, requirements, search_results)
            elif query_type == "planning":
                response_content = await self._generate_comprehensive_plan_response(query, requirements, search_results)
            elif query_type == "dependencies":
                response_content = await self._generate_dependency_response(query, requirements, search_results)
            elif query_type == "risks":
                response_content = await self._generate_risk_response(query, requirements, search_results)
            else:
                response_content = await self._generate_general_planning_response(query, requirements, search_results)

            # Extract sources
            sources = self._extract_sources(search_results)

            processing_time = time.time() - start_time

            # Create response
            response = AgentResponse(
                agent_type=self.agent_type,
                content=response_content,
                confidence=self.can_handle_query(query, context),
                sources=sources,
                processing_time=processing_time,
                metadata={
                    "query_type": query_type,
                    "requirements_count": len(requirements),
                    "search_results_count": len(search_results),
                    "methodology": "5-phase",
                },
            )

            # Update statistics
            self._update_stats(processing_time, True)

            logger.info(f"Task planning query processed successfully in {processing_time:.2f}s")
            return response

        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(processing_time, False)

            logger.error(f"Error processing task planning query: {e}")
            raise AgentError(
                f"Failed to process task planning query: {e}",
                agent_type=self.agent_type.value,
                details={"query": query[:100]},
            ) from e

    def _classify_query_type(self, query: str) -> str:
        """Classify the type of task planning query.

        Args:
            query: User query to classify

        Returns:
            Query type string
        """
        query_lower = query.lower()

        # Check each pattern type
        for query_type, patterns in self.query_patterns.items():
            if any(re.search(pattern, query_lower) for pattern in patterns):
                return query_type

        # Default to general planning
        return "planning"

    async def _perform_search(self, query: str, context: AgentContext) -> list[Any]:
        """Perform search for relevant content.

        Args:
            query: Search query
            context: Current context

        Returns:
            List of search results
        """
        try:
            # Use existing search results if available
            if context.search_results:
                logger.debug("Using existing search results from context")
                return context.search_results

            # Perform new search
            logger.debug(f"Performing search for: {query[:50]}...")
            search_results = await self.ingestion_pipeline.search_content(
                query=query,
                limit=20,  # Get more results for comprehensive planning
                similarity_threshold=0.3,  # Lower threshold for broader context
            )

            logger.debug(f"Found {len(search_results)} search results")
            return search_results

        except Exception as e:
            logger.warning(f"Search failed: {e}")
            return []

    def _extract_requirements(self, query: str, search_results: list[Any]) -> list[str]:
        """Extract requirements from query and search results.

        Args:
            query: User query
            search_results: Search results for context

        Returns:
            List of extracted requirements
        """
        requirements = []

        # Primary requirement from query
        requirements.append(query.strip())

        # Extract additional requirements from search results
        # This is a simplified extraction - could be enhanced with NLP
        for result in search_results[:5]:  # Limit to top 5 results
            try:
                content = result.chunk.content
                # Look for requirement-like patterns in content
                req_patterns = [
                    r"must\s+([^.]+)",
                    r"should\s+([^.]+)",
                    r"need\s+to\s+([^.]+)",
                    r"requirement:\s*([^.]+)",
                ]

                for pattern in req_patterns:
                    matches = re.finditer(pattern, content, re.IGNORECASE)
                    for match in matches:
                        req_text = match.group(1).strip()
                        if len(req_text) > 10 and len(req_text) < 200:  # Reasonable length
                            requirements.append(req_text)

            except Exception as e:
                logger.debug(f"Error extracting requirements from search result: {e}")
                continue

        # Remove duplicates while preserving order
        seen = set()
        unique_requirements = []
        for req in requirements:
            if req.lower() not in seen:
                seen.add(req.lower())
                unique_requirements.append(req)

        logger.debug(f"Extracted {len(unique_requirements)} requirements")
        return unique_requirements[:10]  # Limit to 10 requirements

    async def _generate_breakdown_response(self, query: str, requirements: list[str], search_results: list[Any]) -> str:
        """Generate task breakdown response.

        Args:
            query: Original query
            requirements: Extracted requirements
            search_results: Search results for context

        Returns:
            Formatted response content
        """
        # Break down requirements into tasks
        all_tasks = []
        for requirement in requirements:
            tasks = self.breakdown_engine.break_down_requirement(requirement)
            all_tasks.extend(tasks)

        # Generate timeline
        timeline = self.timeline_generator.generate_timeline(
            tasks=all_tasks,
            title="Task Breakdown Timeline",
            description=f"Timeline for: {query}"
        )

        # Create task plan
        dependency_graph = self.dependency_analyzer.analyze_dependencies(all_tasks)

        task_plan = TaskPlan(
            title="Task Breakdown Plan",
            summary=f"Breakdown of requirements into {len(all_tasks)} actionable tasks",
            timeline=timeline,
            dependency_graph=dependency_graph,
            tasks=all_tasks,
            confidence=0.8,
        )

        # Generate LLM response
        system_prompt = """You are a Task Planner creating detailed task breakdowns.

Your role is to:
- Break down complex requirements into actionable tasks
- Provide clear task descriptions and acceptance criteria
- Estimate effort and identify dependencies
- Follow the 5-phase development methodology
- Ensure tasks are specific, measurable, and achievable

Focus on:
- Clear task definitions
- Realistic effort estimates
- Proper dependency identification
- Risk awareness
- Practical implementation steps

Provide comprehensive task planning guidance."""

        user_prompt = f"""
# Task Breakdown Request

**Query:** {query}

**Requirements:**
{chr(10).join(f"- {req}" for req in requirements)}

**Generated Task Plan:**
- **Total Tasks:** {len(all_tasks)}
- **Estimated Hours:** {timeline.total_estimated_hours:.1f}
- **Timeline:** {timeline.start_date.date()} to {timeline.end_date.date()}
- **Critical Path:** {len(timeline.critical_path)} tasks

**Task Details:**
{self._format_tasks_for_prompt(all_tasks[:10])}  # Show first 10 tasks

**Context from Codebase:**
{self._format_search_results_for_prompt(search_results[:3])}

Please provide a comprehensive task breakdown analysis addressing the query.
Include task prioritization, effort justification, and implementation guidance.
"""

        # Generate response using LLM
        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        # Combine LLM response with structured data
        structured_data = task_plan.to_dict()

        return f"""{llm_response.content}

## Structured Task Plan

```json
{json.dumps(structured_data, indent=2, default=str)}
```

## Task Summary

- **Total Tasks:** {len(all_tasks)}
- **Estimated Effort:** {timeline.total_estimated_hours:.1f} hours
- **Timeline:** {(timeline.end_date - timeline.start_date).days} days
- **Critical Path:** {len(timeline.critical_path)} tasks
- **Confidence:** {task_plan.confidence:.1%}
"""


    async def _generate_comprehensive_plan_response(self, query: str, requirements: list[str], search_results: list[Any]) -> str:
        """Generate comprehensive project plan response.

        Args:
            query: Original query
            requirements: Extracted requirements
            search_results: Search results for context

        Returns:
            Formatted response content
        """
        # Break down requirements
        all_tasks = []
        for requirement in requirements:
            tasks = self.breakdown_engine.break_down_requirement(requirement)
            all_tasks.extend(tasks)

        # Generate timeline
        timeline = self.timeline_generator.generate_timeline(
            tasks=all_tasks,
            title="Comprehensive Project Plan",
            description=f"Complete plan for: {query}"
        )

        # Analyze dependencies
        dependency_graph = self.dependency_analyzer.analyze_dependencies(all_tasks)

        # Create task plan
        task_plan = TaskPlan(
            title="Comprehensive Project Plan",
            summary=f"Complete project plan with {len(all_tasks)} tasks",
            timeline=timeline,
            dependency_graph=dependency_graph,
            tasks=all_tasks,
            confidence=0.85,
        )

        # Analyze risks
        risk_analysis = self.risk_analyzer.analyze_task_plan_risks(task_plan)

        # Generate LLM response
        system_prompt = """You are a Task Planner creating comprehensive project plans.

Your role is to:
- Create complete project plans with tasks, timelines, and dependencies
- Assess risks and provide mitigation strategies
- Apply the 5-phase development methodology
- Provide realistic estimates and schedules
- Ensure project success through proper planning

Focus on:
- Comprehensive project coverage
- Risk assessment and mitigation
- Resource planning
- Timeline optimization
- Quality assurance

Provide thorough project planning guidance."""

        user_prompt = f"""
# Comprehensive Project Planning Request

**Query:** {query}

**Project Overview:**
- **Requirements:** {len(requirements)} identified
- **Tasks:** {len(all_tasks)} generated
- **Timeline:** {(timeline.end_date - timeline.start_date).days} days
- **Effort:** {timeline.total_estimated_hours:.1f} hours
- **Risk Level:** {risk_analysis['overall_risk_level'].value}

**Risk Analysis:**
- **Risk Score:** {risk_analysis['risk_score']:.2f}
- **Key Risks:** {len(risk_analysis['task_risks'])} task risks, {len(risk_analysis['timeline_risks'])} timeline risks
- **Mitigation Plans:** {len(risk_analysis['mitigation_plan'])} strategies identified

**Context from Codebase:**
{self._format_search_results_for_prompt(search_results[:3])}

Please provide a comprehensive project plan addressing the query.
Include executive summary, detailed planning, risk assessment, and recommendations.
"""

        # Generate response using LLM
        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        # Format comprehensive response
        return f"""{llm_response.content}

## Project Plan Summary

### Overview
- **Total Tasks:** {len(all_tasks)}
- **Estimated Effort:** {timeline.total_estimated_hours:.1f} hours ({timeline.total_estimated_hours/8:.1f} person-days)
- **Timeline:** {timeline.start_date.date()} to {timeline.end_date.date()} ({(timeline.end_date - timeline.start_date).days} days)
- **Milestones:** {len(timeline.milestones)}
- **Critical Path:** {len(timeline.critical_path)} tasks

### Risk Assessment
- **Overall Risk Level:** {risk_analysis['overall_risk_level'].value.title()}
- **Risk Score:** {risk_analysis['risk_score']:.1%}
- **High-Risk Tasks:** {sum(1 for tr in risk_analysis['task_risks'] for r in tr['identified_risks'] if r['level'].value == 'high')}

### Key Recommendations
{chr(10).join(f"- {rec}" for rec in risk_analysis['recommendations'][:5])}

### Structured Data

```json
{json.dumps(task_plan.to_dict(), indent=2, default=str)}
```
"""


    async def _generate_timeline_response(self, query: str, requirements: list[str], search_results: list[Any]) -> str:
        """Generate timeline estimation response."""
        # Break down requirements
        all_tasks = []
        for requirement in requirements:
            tasks = self.breakdown_engine.break_down_requirement(requirement)
            all_tasks.extend(tasks)

        # Generate timeline
        timeline = self.timeline_generator.generate_timeline(
            tasks=all_tasks,
            title="Project Timeline",
            description=f"Timeline estimation for: {query}"
        )

        # Calculate critical path
        critical_path = self.timeline_generator.calculate_critical_path(all_tasks)

        system_prompt = """You are a Task Planner providing timeline estimates.

Your role is to:
- Provide realistic timeline estimates
- Identify critical path and bottlenecks
- Suggest timeline optimizations
- Account for risks and uncertainties
- Provide milestone planning

Focus on:
- Accurate effort estimation
- Dependency-aware scheduling
- Risk-adjusted timelines
- Resource considerations
- Practical delivery dates"""

        user_prompt = f"""
# Timeline Estimation Request

**Query:** {query}

**Timeline Analysis:**
- **Total Tasks:** {len(all_tasks)}
- **Estimated Effort:** {timeline.total_estimated_hours:.1f} hours
- **Duration:** {(timeline.end_date - timeline.start_date).days} days
- **Critical Path:** {len(critical_path)} tasks
- **Confidence:** {timeline.confidence:.1%}

**Key Milestones:**
{chr(10).join(f"- {m.title}: {m.target_date.date()}" for m in timeline.milestones[:5])}

Please provide timeline analysis and recommendations addressing the query.
"""

        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        return f"""{llm_response.content}

## Timeline Summary

- **Start Date:** {timeline.start_date.date()}
- **End Date:** {timeline.end_date.date()}
- **Duration:** {(timeline.end_date - timeline.start_date).days} days
- **Total Effort:** {timeline.total_estimated_hours:.1f} hours ({timeline.total_estimated_hours/8:.1f} person-days)
- **Critical Path:** {len(critical_path)} tasks
- **Confidence Level:** {timeline.confidence:.1%}
"""

    async def _generate_dependency_response(self, query: str, requirements: list[str], search_results: list[Any]) -> str:
        """Generate dependency analysis response."""
        # Break down requirements
        all_tasks = []
        for requirement in requirements:
            tasks = self.breakdown_engine.break_down_requirement(requirement)
            all_tasks.extend(tasks)

        # Analyze dependencies
        dependency_graph = self.dependency_analyzer.analyze_dependencies(all_tasks)
        bottlenecks = self.dependency_analyzer.detect_bottlenecks(dependency_graph)
        optimizations = self.dependency_analyzer.suggest_optimizations(dependency_graph)

        system_prompt = """You are a Task Planner analyzing task dependencies.

Your role is to:
- Analyze task dependencies and relationships
- Identify bottlenecks and critical paths
- Suggest dependency optimizations
- Recommend parallelization opportunities
- Provide scheduling guidance

Focus on:
- Clear dependency mapping
- Bottleneck identification
- Optimization opportunities
- Risk mitigation
- Practical scheduling advice"""

        user_prompt = f"""
# Dependency Analysis Request

**Query:** {query}

**Dependency Analysis:**
- **Total Tasks:** {len(all_tasks)}
- **Dependencies:** {len(dependency_graph.edges)} relationships
- **Critical Path:** {len(dependency_graph.critical_path)} tasks
- **Bottlenecks:** {len(bottlenecks)} identified
- **Optimizations:** {len(optimizations)} opportunities

**Key Bottlenecks:**
{chr(10).join(f"- {b['description']}" for b in bottlenecks[:3])}

Please provide dependency analysis and optimization recommendations.
"""

        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        return f"""{llm_response.content}

## Dependency Analysis

- **Total Dependencies:** {len(dependency_graph.edges)}
- **Critical Path Length:** {len(dependency_graph.critical_path)} tasks
- **Identified Bottlenecks:** {len(bottlenecks)}
- **Optimization Opportunities:** {len(optimizations)}
"""

    async def _generate_risk_response(self, query: str, requirements: list[str], search_results: list[Any]) -> str:
        """Generate risk assessment response."""
        # Break down requirements
        all_tasks = []
        for requirement in requirements:
            tasks = self.breakdown_engine.break_down_requirement(requirement)
            all_tasks.extend(tasks)

        # Generate timeline and dependencies
        timeline = self.timeline_generator.generate_timeline(all_tasks)
        dependency_graph = self.dependency_analyzer.analyze_dependencies(all_tasks)

        # Create task plan for risk analysis
        task_plan = TaskPlan(
            title="Risk Assessment Plan",
            summary="Plan for risk analysis",
            timeline=timeline,
            dependency_graph=dependency_graph,
            tasks=all_tasks,
            confidence=0.8,
        )

        # Analyze risks
        risk_analysis = self.risk_analyzer.analyze_task_plan_risks(task_plan)

        system_prompt = """You are a Task Planner conducting risk assessment.

Your role is to:
- Identify project risks and challenges
- Assess risk probability and impact
- Provide mitigation strategies
- Recommend risk monitoring approaches
- Suggest contingency planning

Focus on:
- Comprehensive risk identification
- Practical mitigation strategies
- Proactive risk management
- Contingency planning
- Risk monitoring"""

        user_prompt = f"""
# Risk Assessment Request

**Query:** {query}

**Risk Analysis:**
- **Overall Risk Level:** {risk_analysis['overall_risk_level'].value}
- **Risk Score:** {risk_analysis['risk_score']:.2f}
- **Task Risks:** {len(risk_analysis['task_risks'])}
- **Timeline Risks:** {len(risk_analysis['timeline_risks'])}
- **Dependency Risks:** {len(risk_analysis['dependency_risks'])}

**Key Recommendations:**
{chr(10).join(f"- {rec}" for rec in risk_analysis['recommendations'][:5])}

Please provide comprehensive risk assessment and mitigation guidance.
"""

        llm_response = await self.llm_client.generate_response(
            prompt=user_prompt, system_prompt=system_prompt
        )

        return f"""{llm_response.content}

## Risk Assessment Summary

- **Overall Risk Level:** {risk_analysis['overall_risk_level'].value.title()}
- **Risk Score:** {risk_analysis['risk_score']:.1%}
- **Mitigation Strategies:** {len(risk_analysis['mitigation_plan'])}
- **Key Recommendations:** {len(risk_analysis['recommendations'])}
"""

    async def _generate_general_planning_response(self, query: str, requirements: list[str], search_results: list[Any]) -> str:
        """Generate general planning response."""
        return await self._generate_comprehensive_plan_response(query, requirements, search_results)

    def _format_tasks_for_prompt(self, tasks: list[Any]) -> str:
        """Format tasks for LLM prompt."""
        formatted_tasks = []
        for task in tasks:
            formatted_tasks.append(f"""
**{task.id}: {task.title}**
- Type: {task.task_type.value}
- Estimate: {task.estimate_hours:.1f} hours
- Priority: {task.priority.value}
- Dependencies: {len(task.dependencies)}
- Description: {task.description[:100]}...
""")
        return "\n".join(formatted_tasks)

    def _format_search_results_for_prompt(self, search_results: list[Any]) -> str:
        """Format search results for LLM prompt."""
        if not search_results:
            return "No relevant context found."

        formatted_results = []
        for i, result in enumerate(search_results, 1):
            try:
                file_path = result.chunk.file_metadata.file_path
                content = result.chunk.content[:200]
                formatted_results.append(f"""
**Source {i}: {file_path}**
{content}...
""")
            except Exception:
                formatted_results.append(f"**Source {i}:** Content unavailable")

        return "\n".join(formatted_results)

    def _extract_sources(self, search_results: list[Any]) -> list[str]:
        """Extract source citations from search results."""
        sources = []
        for result in search_results[:10]:  # Limit to top 10 sources
            try:
                file_path = result.chunk.file_metadata.file_path
            except AttributeError:
                file_path = "Unknown file"
            sources.append(file_path)
        return sources
