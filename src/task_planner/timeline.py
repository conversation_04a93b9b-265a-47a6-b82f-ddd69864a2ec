"""
Timeline Generation and Estimation

This module generates realistic implementation schedules, estimates effort for tasks,
and creates dependency-aware timelines with resource allocation.
"""

from datetime import datetime, timedelta
import logging

from .models import (
    DependencyType,
    Milestone,
    Task,
    Timeline,
)

logger = logging.getLogger(__name__)


class TimelineGenerator:
    """Generates realistic implementation schedules and timelines."""

    def __init__(self, working_hours_per_day: float = 8.0, working_days_per_week: int = 5):
        """Initialize the timeline generator.

        Args:
            working_hours_per_day: Number of working hours per day
            working_days_per_week: Number of working days per week
        """
        self.working_hours_per_day = working_hours_per_day
        self.working_days_per_week = working_days_per_week
        self.hours_per_week = working_hours_per_day * working_days_per_week

        # Buffer factors for different task types
        self.buffer_factors = {
            "analysis": 1.2,      # 20% buffer for analysis tasks
            "design": 1.3,        # 30% buffer for design tasks
            "implementation": 1.5, # 50% buffer for implementation tasks
            "testing": 1.4,       # 40% buffer for testing tasks
            "documentation": 1.1,  # 10% buffer for documentation tasks
            "deployment": 1.6,    # 60% buffer for deployment tasks
            "review": 1.2,        # 20% buffer for review tasks
            "refactoring": 1.4,   # 40% buffer for refactoring tasks
        }

        logger.info(f"Initialized timeline generator with {working_hours_per_day}h/day, {working_days_per_week} days/week")

    def generate_timeline(
        self,
        tasks: list[Task],
        start_date: datetime | None = None,
        title: str = "Project Timeline",
        description: str = "Generated project timeline"
    ) -> Timeline:
        """Generate a timeline from a list of tasks.

        Args:
            tasks: List of tasks to schedule
            start_date: Project start date (defaults to now)
            title: Timeline title
            description: Timeline description

        Returns:
            Timeline object with scheduled tasks
        """
        if start_date is None:
            start_date = datetime.now()

        logger.info(f"Generating timeline for {len(tasks)} tasks starting {start_date.date()}")

        # Apply effort estimation buffers
        buffered_tasks = self._apply_effort_buffers(tasks)

        # Schedule tasks considering dependencies
        scheduled_tasks = self._schedule_tasks(buffered_tasks, start_date)

        # Calculate total duration and end date
        end_date = self._calculate_end_date(scheduled_tasks, start_date)

        # Calculate total estimated hours
        total_hours = sum(task.estimate_hours for task in scheduled_tasks)

        # Create timeline
        timeline = Timeline(
            title=title,
            description=description,
            start_date=start_date,
            end_date=end_date,
            tasks=scheduled_tasks,
            total_estimated_hours=total_hours,
            confidence=self._calculate_timeline_confidence(scheduled_tasks)
        )

        # Add milestones
        milestones = self._generate_milestones(scheduled_tasks, start_date, end_date)
        for milestone in milestones:
            timeline.add_milestone(milestone)

        logger.info(f"Generated timeline: {total_hours:.1f} hours, {(end_date - start_date).days} days")
        return timeline

    def estimate_task_duration(self, task: Task, include_buffer: bool = True) -> float:
        """Estimate the duration of a task in hours.

        Args:
            task: Task to estimate
            include_buffer: Whether to include buffer time

        Returns:
            Estimated duration in hours
        """
        base_estimate = task.estimate_hours

        if not include_buffer:
            return base_estimate

        # Apply buffer based on task type
        task_type_str = task.task_type.value
        buffer_factor = self.buffer_factors.get(task_type_str, 1.3)  # Default 30% buffer

        # Apply additional buffer based on task complexity (inferred from estimate)
        if base_estimate > 40:  # > 1 week
            buffer_factor *= 1.2  # Additional 20% for complex tasks
        elif base_estimate > 80:  # > 2 weeks
            buffer_factor *= 1.4  # Additional 40% for very complex tasks

        buffered_estimate = base_estimate * buffer_factor

        logger.debug(f"Task {task.id} estimate: {base_estimate}h -> {buffered_estimate:.1f}h (buffer: {buffer_factor:.1f}x)")
        return buffered_estimate

    def calculate_critical_path(self, tasks: list[Task]) -> list[str]:
        """Calculate the critical path through the task dependencies.

        Args:
            tasks: List of tasks with dependencies

        Returns:
            List of task IDs in the critical path
        """
        logger.info(f"Calculating critical path for {len(tasks)} tasks")

        # Build task lookup
        task_lookup = {task.id: task for task in tasks}

        # Calculate earliest start and finish times
        earliest_times = self._calculate_earliest_times(tasks, task_lookup)

        # Calculate latest start and finish times
        latest_times = self._calculate_latest_times(tasks, task_lookup, earliest_times)

        # Find critical tasks (where earliest = latest)
        critical_tasks = []
        for task in tasks:
            task_id = task.id
            if (earliest_times[task_id]["start"] == latest_times[task_id]["start"] and
                earliest_times[task_id]["finish"] == latest_times[task_id]["finish"]):
                critical_tasks.append(task_id)

        logger.info(f"Critical path contains {len(critical_tasks)} tasks")
        return critical_tasks

    def _apply_effort_buffers(self, tasks: list[Task]) -> list[Task]:
        """Apply effort estimation buffers to tasks."""
        buffered_tasks = []

        for task in tasks:
            # Create a copy of the task with buffered estimate
            buffered_task = Task(
                id=task.id,
                title=task.title,
                description=task.description,
                task_type=task.task_type,
                priority=task.priority,
                status=task.status,
                estimate_hours=self.estimate_task_duration(task, include_buffer=True),
                actual_hours=task.actual_hours,
                dependencies=task.dependencies.copy(),
                assignee=task.assignee,
                tags=task.tags.copy(),
                files_to_change=task.files_to_change.copy(),
                test_cases=task.test_cases.copy(),
                acceptance_criteria=task.acceptance_criteria.copy(),
                risks=task.risks.copy(),
                notes=task.notes,
                created_at=task.created_at,
                updated_at=task.updated_at,
            )
            buffered_tasks.append(buffered_task)

        return buffered_tasks

    def _schedule_tasks(self, tasks: list[Task], start_date: datetime) -> list[Task]:
        """Schedule tasks considering dependencies and resource constraints."""
        # For now, implement a simple sequential scheduling
        # In a more advanced version, this could consider parallel execution and resource allocation

        task_lookup = {task.id: task for task in tasks}
        scheduled_tasks = []
        task_start_dates = {}

        # Sort tasks by dependencies (topological sort)
        sorted_tasks = self._topological_sort(tasks)

        current_date = start_date

        for task in sorted_tasks:
            # Find the latest dependency finish date
            dependency_finish_date = current_date

            for dependency in task.dependencies:
                if dependency.dependency_type == DependencyType.FINISH_TO_START:
                    dep_task = task_lookup.get(dependency.task_id)
                    if dep_task and dependency.task_id in task_start_dates:
                        dep_start = task_start_dates[dependency.task_id]
                        dep_duration_days = self._hours_to_days(dep_task.estimate_hours)
                        dep_finish = dep_start + timedelta(days=dep_duration_days)

                        # Add lag time if specified
                        if dependency.lag_hours > 0:
                            lag_days = self._hours_to_days(dependency.lag_hours)
                            dep_finish += timedelta(days=lag_days)

                        dependency_finish_date = max(dependency_finish_date, dep_finish)

            # Schedule this task
            task_start_dates[task.id] = dependency_finish_date

            # Update current date for next task
            task_duration_days = self._hours_to_days(task.estimate_hours)
            current_date = dependency_finish_date + timedelta(days=task_duration_days)

            scheduled_tasks.append(task)

        return scheduled_tasks

    def _calculate_end_date(self, tasks: list[Task], start_date: datetime) -> datetime:
        """Calculate the project end date based on scheduled tasks."""
        if not tasks:
            return start_date

        # Find the task that finishes latest
        latest_finish = start_date

        for task in tasks:
            task_duration_days = self._hours_to_days(task.estimate_hours)
            task_finish = start_date + timedelta(days=task_duration_days)
            latest_finish = max(latest_finish, task_finish)

        return latest_finish

    def _generate_milestones(self, tasks: list[Task], start_date: datetime, end_date: datetime) -> list[Milestone]:
        """Generate project milestones based on task phases."""
        milestones = []

        # Group tasks by phase (based on tags)
        phase_tasks = {
            "phase-1": [],
            "phase-2": [],
            "phase-3": [],
            "phase-4": [],
            "phase-5": [],
        }

        for task in tasks:
            for tag in task.tags:
                if tag in phase_tasks:
                    phase_tasks[tag].append(task.id)

        # Create milestones for each phase
        phase_names = {
            "phase-1": "Discovery & Analysis Complete",
            "phase-2": "Task Planning Complete",
            "phase-3": "Implementation Complete",
            "phase-4": "Verification Complete",
            "phase-5": "Documentation & Handover Complete",
        }

        milestone_id = 1
        for phase, task_ids in phase_tasks.items():
            if task_ids:
                # Calculate milestone date (end of phase)
                phase_duration = sum(
                    task.estimate_hours for task in tasks if task.id in task_ids
                )
                phase_days = self._hours_to_days(phase_duration)
                milestone_date = start_date + timedelta(days=phase_days)

                milestone = Milestone(
                    id=f"M{milestone_id:02d}",
                    title=phase_names.get(phase, f"Phase {phase} Complete"),
                    description=f"All tasks in {phase} are completed",
                    target_date=milestone_date,
                    task_ids=task_ids,
                )
                milestones.append(milestone)
                milestone_id += 1

        # Add project completion milestone
        milestones.append(Milestone(
            id=f"M{milestone_id:02d}",
            title="Project Complete",
            description="All project tasks are completed",
            target_date=end_date,
            task_ids=[task.id for task in tasks],
        ))

        return milestones

    def _calculate_timeline_confidence(self, tasks: list[Task]) -> float:
        """Calculate confidence score for the timeline."""
        if not tasks:
            return 0.0

        # Base confidence starts high
        confidence = 0.9

        # Reduce confidence based on task complexity and risks
        for task in tasks:
            # Reduce confidence for high-effort tasks
            if task.estimate_hours > 40:  # > 1 week
                confidence *= 0.95
            elif task.estimate_hours > 80:  # > 2 weeks
                confidence *= 0.9

            # Reduce confidence based on number of dependencies
            if len(task.dependencies) > 3:
                confidence *= 0.95

            # Reduce confidence based on risks
            high_risk_count = sum(1 for risk in task.risks if risk.level.value in ["high", "critical"])
            if high_risk_count > 0:
                confidence *= (0.9 ** high_risk_count)

        return max(0.1, confidence)  # Minimum 10% confidence

    def _hours_to_days(self, hours: float) -> float:
        """Convert hours to working days."""
        return hours / self.working_hours_per_day

    def _topological_sort(self, tasks: list[Task]) -> list[Task]:
        """Sort tasks in dependency order using topological sort."""
        # Build dependency graph
        {task.id: task for task in tasks}
        in_degree = {task.id: 0 for task in tasks}

        # Count incoming edges (dependencies)
        for task in tasks:
            for dependency in task.dependencies:
                if dependency.task_id in in_degree:
                    in_degree[task.id] += 1

        # Find tasks with no dependencies
        queue = [task for task in tasks if in_degree[task.id] == 0]
        sorted_tasks = []

        while queue:
            # Take a task with no dependencies
            current_task = queue.pop(0)
            sorted_tasks.append(current_task)

            # Remove this task's outgoing edges
            for task in tasks:
                for dependency in task.dependencies:
                    if dependency.task_id == current_task.id:
                        in_degree[task.id] -= 1
                        if in_degree[task.id] == 0:
                            queue.append(task)

        # If we couldn't sort all tasks, there might be a cycle
        if len(sorted_tasks) != len(tasks):
            logger.warning("Possible dependency cycle detected, using original order")
            return tasks

        return sorted_tasks

    def _calculate_earliest_times(self, tasks: list[Task], task_lookup: dict[str, Task]) -> dict[str, dict[str, float]]:
        """Calculate earliest start and finish times for tasks."""
        earliest = {}

        # Sort tasks in dependency order
        sorted_tasks = self._topological_sort(tasks)

        for task in sorted_tasks:
            earliest_start = 0.0

            # Find the latest dependency finish time
            for dependency in task.dependencies:
                if (dependency.dependency_type == DependencyType.FINISH_TO_START and
                    dependency.task_id in earliest):
                    dep_finish = earliest[dependency.task_id]["finish"]
                    earliest_start = max(earliest_start, dep_finish + dependency.lag_hours)

            earliest_finish = earliest_start + task.estimate_hours

            earliest[task.id] = {
                "start": earliest_start,
                "finish": earliest_finish
            }

        return earliest

    def _calculate_latest_times(
        self,
        tasks: list[Task],
        task_lookup: dict[str, Task],
        earliest_times: dict[str, dict[str, float]]
    ) -> dict[str, dict[str, float]]:
        """Calculate latest start and finish times for tasks."""
        latest = {}

        # Find project end time
        project_end = max(earliest_times[task.id]["finish"] for task in tasks)

        # Sort tasks in reverse dependency order
        sorted_tasks = list(reversed(self._topological_sort(tasks)))

        for task in sorted_tasks:
            latest_finish = project_end

            # Find the earliest successor start time
            for other_task in tasks:
                for dependency in other_task.dependencies:
                    is_successor = (dependency.task_id == task.id and
                                  dependency.dependency_type == DependencyType.FINISH_TO_START)
                    if is_successor and other_task.id in latest:
                        successor_start = latest[other_task.id]["start"]
                        latest_finish = min(latest_finish, successor_start - dependency.lag_hours)

            latest_start = latest_finish - task.estimate_hours

            latest[task.id] = {
                "start": latest_start,
                "finish": latest_finish
            }

        return latest
