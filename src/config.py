"""
Configuration management for the LLM RAG Codebase Query System.

This module handles loading and validation of environment variables
and configuration settings across different environments.
"""

import json
import tempfile
from typing import Any

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    model_config = SettingsConfigDict(env_file=".env", env_file_encoding="utf-8", case_sensitive=False, extra="ignore")

    # Application Settings
    environment: str = Field(default="development", alias="ENVIRONMENT")
    debug: bool = Field(default=True, alias="DEBUG")
    log_level: str = Field(default="INFO", alias="LOG_LEVEL")

    # API Configuration
    api_host: str = Field(default="127.0.0.1", alias="API_HOST", description="API host binding (use 0.0.0.0 for all interfaces in production)")
    api_port: int = Field(default=8000, alias="API_PORT")
    api_workers: int = Field(default=1, alias="API_WORKERS")

    # Frontend Configuration
    frontend_url: str = Field(default="http://localhost:3000", alias="FRONTEND_URL")

    # LLM Provider Configuration
    openai_api_key: str = Field(alias="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-5", alias="OPENAI_MODEL")
    openai_fast_model: str = Field(default="gpt-5-mini", alias="OPENAI_FAST_MODEL")
    openai_temperature: float = Field(default=0.1, alias="OPENAI_TEMPERATURE")
    openai_max_tokens: int = Field(default=4000, alias="OPENAI_MAX_TOKENS")

    # Model Selection Configuration
    enable_smart_model_selection: bool = Field(default=True, alias="ENABLE_SMART_MODEL_SELECTION")
    complexity_threshold: float = Field(default=0.7, alias="COMPLEXITY_THRESHOLD")  # Above this uses GPT-5

    # Vector Database Configuration
    vector_store_provider: str = Field(
        default="chromadb", alias="VECTOR_STORE_PROVIDER", description="Vector store provider (chromadb, weaviate)"
    )
    chroma_host: str = Field(default="localhost", alias="CHROMA_HOST", description="ChromaDB host")
    chroma_port: int = Field(default=8001, alias="CHROMA_PORT", description="ChromaDB port")
    chroma_collection_name: str = Field(
        default="codebase_embeddings", alias="CHROMA_COLLECTION_NAME", description="ChromaDB collection name"
    )
    chroma_persist_directory: str = Field(
        default="./chroma_db", alias="CHROMA_PERSIST_DIRECTORY", description="ChromaDB persistence directory"
    )
    vector_search_top_k: int = Field(
        default=10, alias="VECTOR_SEARCH_TOP_K", description="Default number of results for vector search"
    )
    vector_search_threshold: float = Field(
        default=0.7, alias="VECTOR_SEARCH_THRESHOLD", description="Minimum similarity threshold for search results"
    )

    # Embedding Configuration
    embedding_provider: str = Field(
        default="openai", alias="EMBEDDING_PROVIDER", description="Embedding provider (openai, local)"
    )
    embedding_model: str = Field(
        default="text-embedding-3-large", alias="EMBEDDING_MODEL", description="Embedding model name"
    )
    embedding_dimension: int = Field(
        default=3072, alias="EMBEDDING_DIMENSION", description="Embedding vector dimension"
    )
    embedding_batch_size: int = Field(
        default=100, alias="EMBEDDING_BATCH_SIZE", description="Batch size for embedding generation"
    )
    embedding_max_retries: int = Field(
        default=3, alias="EMBEDDING_MAX_RETRIES", description="Maximum retries for embedding API calls"
    )
    embedding_timeout: int = Field(
        default=60, alias="EMBEDDING_TIMEOUT", description="Timeout for embedding API calls in seconds"
    )

    # GitHub Integration
    github_token: str | None = Field(default=None, alias="GITHUB_TOKEN")
    github_api_url: str = Field(default="https://api.github.com", alias="GITHUB_API_URL")

    # Redis Configuration
    redis_host: str = Field(default="localhost", alias="REDIS_HOST")
    redis_port: int = Field(default=6379, alias="REDIS_PORT")
    redis_db: int = Field(default=0, alias="REDIS_DB")
    redis_password: str | None = Field(default=None, alias="REDIS_PASSWORD")
    redis_ttl: int = Field(default=3600, alias="REDIS_TTL")

    # LLM Cache Configuration
    enable_llm_cache: bool = Field(default=True, alias="ENABLE_LLM_CACHE")
    cache_ttl: int = Field(default=3600, alias="CACHE_TTL")  # 1 hour default

    # Security Settings
    secret_key: str | None = Field(default=None, alias="SECRET_KEY")
    jwt_secret: str | None = Field(default=None, alias="JWT_SECRET")
    jwt_expiration: int = Field(default=3600, alias="JWT_EXPIRATION")
    cors_origins: list[str] = Field(default=["http://localhost:3000"], alias="CORS_ORIGINS")

    # File Processing
    max_file_size: int = Field(default=10485760, alias="MAX_FILE_SIZE")  # 10MB
    allowed_file_types: list[str] = Field(
        default=["py", "js", "ts", "md", "txt", "json", "yaml", "yml"],
        alias="ALLOWED_FILE_TYPES",
    )
    temp_dir: str = Field(default_factory=lambda: tempfile.gettempdir() + "/rag_processing", alias="TEMP_DIR")

    # Chunking Configuration
    chunk_size: int = Field(default=1000, alias="CHUNK_SIZE", description="Default chunk size in tokens")
    chunk_overlap: int = Field(default=200, alias="CHUNK_OVERLAP", description="Overlap between chunks in tokens")
    max_chunk_size: int = Field(default=2000, alias="MAX_CHUNK_SIZE", description="Maximum chunk size in tokens")
    min_chunk_size: int = Field(default=100, alias="MIN_CHUNK_SIZE", description="Minimum chunk size in tokens")
    chunk_batch_size: int = Field(
        default=20, alias="CHUNK_BATCH_SIZE", description="Number of files to process in parallel for chunking"
    )
    preserve_code_structure: bool = Field(
        default=True, alias="PRESERVE_CODE_STRUCTURE", description="Use AST-based chunking for code files"
    )
    markdown_header_split: bool = Field(
        default=True, alias="MARKDOWN_HEADER_SPLIT", description="Split markdown files on headers"
    )

    # Embedding Pipeline Configuration
    embedding_pipeline_batch_size: int = Field(
        default=50,
        alias="EMBEDDING_PIPELINE_BATCH_SIZE",
        description="Number of chunks to process in parallel for embedding",
    )
    embedding_quality_check: bool = Field(
        default=True, alias="EMBEDDING_QUALITY_CHECK", description="Enable embedding quality validation"
    )
    embedding_cache_enabled: bool = Field(
        default=True, alias="EMBEDDING_CACHE_ENABLED", description="Enable embedding caching for duplicate content"
    )
    embedding_normalize: bool = Field(
        default=True, alias="EMBEDDING_NORMALIZE", description="Normalize embeddings to unit vectors"
    )

    # Monitoring & Observability
    enable_metrics: bool = Field(default=True, alias="ENABLE_METRICS")
    metrics_port: int = Field(default=9090, alias="METRICS_PORT")
    sentry_dsn: str | None = Field(default=None, alias="SENTRY_DSN")

    # Rate Limiting
    rate_limit_requests: int = Field(default=100, alias="RATE_LIMIT_REQUESTS")
    rate_limit_window: int = Field(default=60, alias="RATE_LIMIT_WINDOW")

    # Development Settings
    dev_mode: bool = Field(default=True, alias="DEV_MODE")
    reload_on_change: bool = Field(default=True, alias="RELOAD_ON_CHANGE")

    # Task Planner Configuration
    task_planner_working_hours_per_day: float = Field(default=8.0, alias="TASK_PLANNER_WORKING_HOURS_PER_DAY")
    task_planner_working_days_per_week: int = Field(default=5, alias="TASK_PLANNER_WORKING_DAYS_PER_WEEK")
    task_planner_default_buffer_factor: float = Field(default=1.3, alias="TASK_PLANNER_DEFAULT_BUFFER_FACTOR")
    task_planner_max_task_complexity_hours: float = Field(default=80.0, alias="TASK_PLANNER_MAX_TASK_COMPLEXITY_HOURS")
    task_planner_enable_risk_analysis: bool = Field(default=True, alias="TASK_PLANNER_ENABLE_RISK_ANALYSIS")
    task_planner_enable_dependency_optimization: bool = Field(default=True, alias="TASK_PLANNER_ENABLE_DEPENDENCY_OPTIMIZATION")

    @property
    def task_planner(self) -> dict[str, Any]:
        """Get task planner configuration as a dictionary."""
        return {
            "working_hours_per_day": self.task_planner_working_hours_per_day,
            "working_days_per_week": self.task_planner_working_days_per_week,
            "default_buffer_factor": self.task_planner_default_buffer_factor,
            "max_task_complexity_hours": self.task_planner_max_task_complexity_hours,
            "enable_risk_analysis": self.task_planner_enable_risk_analysis,
            "enable_dependency_optimization": self.task_planner_enable_dependency_optimization,
        }

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level is one of the allowed values."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of: {allowed_levels}")
        return v.upper()

    @field_validator("environment")
    @classmethod
    def validate_environment(cls, v: str) -> str:
        """Validate environment is one of the allowed values."""
        allowed_envs = ["development", "staging", "production"]
        if v.lower() not in allowed_envs:
            raise ValueError(f"Environment must be one of: {allowed_envs}")
        return v.lower()

    @field_validator("cors_origins", mode="before")
    @classmethod
    def parse_cors_origins(cls, v: Any) -> list[str]:
        """Parse CORS origins from string or list."""
        if isinstance(v, str):
            # Handle JSON-like string format
            if v.startswith("[") and v.endswith("]"):
                return json.loads(v)
            # Handle comma-separated string
            return [origin.strip() for origin in v.split(",")]
        return v

    @field_validator("allowed_file_types", mode="before")
    @classmethod
    def parse_allowed_file_types(cls, v: Any) -> list[str]:
        """Parse allowed file types from string or list."""
        if isinstance(v, str):
            # Handle JSON-like string format
            if v.startswith("[") and v.endswith("]"):
                return json.loads(v)
            # Handle comma-separated string
            return [file_type.strip() for file_type in v.split(",")]
        return v

    @property
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return self.environment == "development"

    @property
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return self.environment == "production"

    @property
    def chroma_url(self) -> str:
        """Get the full ChromaDB URL."""
        return f"http://{self.chroma_host}:{self.chroma_port}"

    @property
    def redis_url(self) -> str:
        """Get the full Redis URL."""
        if self.redis_password:
            return f"redis://:{self.redis_password}@{self.redis_host}:{self.redis_port}/{self.redis_db}"
        return f"redis://{self.redis_host}:{self.redis_port}/{self.redis_db}"

    model_config = SettingsConfigDict(
        env_file=".env",
        env_file_encoding="utf-8",
        case_sensitive=False,
    )


def get_settings() -> Settings:
    """Get application settings instance."""
    return Settings()


# Global settings instance
settings = get_settings()
