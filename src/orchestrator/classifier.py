"""
Query Classification and Intent Detection

This module provides query classification capabilities to determine the appropriate
agent for handling different types of queries.
"""

from dataclasses import dataclass
from enum import Enum
import logging

from ..agents.base import AgentType

logger = logging.getLogger(__name__)


class QueryIntent(Enum):
    """Enumeration of query intents."""

    ARCHITECTURE_DESIGN = "architecture_design"
    TASK_PLANNING = "task_planning"
    CODE_LOOKUP = "code_lookup"
    FACTUAL_QUERY = "factual_query"
    DOCUMENTATION = "documentation"
    IMPLEMENTATION = "implementation"
    TROUBLESHOOTING = "troubleshooting"
    GENERAL = "general"


@dataclass
class ClassificationResult:
    """Result of query classification."""

    intent: QueryIntent
    agent_type: AgentType
    confidence: float
    reasoning: str
    keywords_matched: list[str]


class QueryClassifier:
    """Classifies queries to determine appropriate agent routing."""

    def __init__(self):
        # Define keyword patterns for different intents
        self.intent_patterns = {
            QueryIntent.ARCHITECTURE_DESIGN: {
                "keywords": [
                    "architecture",
                    "design",
                    "pattern",
                    "structure",
                    "system",
                    "component",
                    "module",
                    "interface",
                    "api design",
                    "schema",
                    "database design",
                    "microservice",
                    "monolith",
                    "scalability",
                    "performance",
                    "security architecture",
                    "deployment",
                ],
                "phrases": [
                    "how should i design",
                    "what architecture",
                    "design pattern",
                    "system design",
                    "best practices for",
                    "recommended approach",
                    "architectural decision",
                    "design principles",
                ],
                "agent": AgentType.TECHNICAL_ARCHITECT,
                "base_confidence": 0.8,
            },
            QueryIntent.TASK_PLANNING: {
                "keywords": [
                    "plan",
                    "task",
                    "step",
                    "implement",
                    "build",
                    "create",
                    "develop",
                    "timeline",
                    "roadmap",
                    "milestone",
                    "phase",
                    "requirement",
                    "feature",
                    "epic",
                    "story",
                    "backlog",
                ],
                "phrases": [
                    "how to implement",
                    "step by step",
                    "break down",
                    "plan for",
                    "roadmap for",
                    "timeline for",
                    "how do i build",
                    "create a plan",
                    "implementation plan",
                    "development steps",
                ],
                "agent": AgentType.TASK_PLANNER,
                "base_confidence": 0.8,
            },
            QueryIntent.CODE_LOOKUP: {
                "keywords": [
                    "function",
                    "class",
                    "method",
                    "variable",
                    "import",
                    "module",
                    "file",
                    "code",
                    "implementation",
                    "example",
                    "snippet",
                    "syntax",
                    "usage",
                    "api",
                    "library",
                    "package",
                ],
                "phrases": [
                    "show me",
                    "find the",
                    "where is",
                    "how does",
                    "what does",
                    "code for",
                    "implementation of",
                    "example of",
                    "usage of",
                ],
                "agent": AgentType.RAG_RETRIEVAL,
                "base_confidence": 0.9,
            },
            QueryIntent.FACTUAL_QUERY: {
                "keywords": [
                    "what is",
                    "what are",
                    "define",
                    "explain",
                    "describe",
                    "tell me about",
                    "information",
                    "details",
                    "overview",
                    "summary",
                    "list",
                    "enumerate",
                ],
                "phrases": [
                    "what is",
                    "what are",
                    "tell me about",
                    "explain the",
                    "describe the",
                    "give me information",
                    "overview of",
                ],
                "agent": AgentType.RAG_RETRIEVAL,
                "base_confidence": 0.8,
            },
            QueryIntent.DOCUMENTATION: {
                "keywords": [
                    "documentation",
                    "docs",
                    "readme",
                    "guide",
                    "tutorial",
                    "manual",
                    "reference",
                    "help",
                    "instructions",
                    "how to",
                ],
                "phrases": [
                    "documentation for",
                    "how to use",
                    "user guide",
                    "reference for",
                    "instructions for",
                    "tutorial on",
                    "guide for",
                ],
                "agent": AgentType.RAG_RETRIEVAL,
                "base_confidence": 0.7,
            },

            QueryIntent.IMPLEMENTATION: {
                "keywords": [
                    "implement",
                    "build",
                    "create",
                    "develop",
                    "code",
                    "write",
                    "make",
                    "construct",
                    "setup",
                    "configure",
                ],
                "phrases": [
                    "how to implement",
                    "how to build",
                    "how to create",
                    "implementation of",
                    "build this",
                    "create this",
                ],
                "agent": AgentType.TASK_PLANNER,
                "base_confidence": 0.7,
            },

            QueryIntent.TROUBLESHOOTING: {
                "keywords": [
                    "error",
                    "bug",
                    "issue",
                    "problem",
                    "fix",
                    "debug",
                    "troubleshoot",
                    "broken",
                    "not working",
                    "fails",
                ],
                "phrases": [
                    "how to fix",
                    "why is",
                    "what's wrong",
                    "not working",
                    "error with",
                    "problem with",
                ],
                "agent": AgentType.RAG_RETRIEVAL,
                "base_confidence": 0.8,
            },

            QueryIntent.GENERAL: {
                "keywords": [
                    "help",
                    "question",
                    "general",
                    "about",
                    "overview",
                ],
                "phrases": [
                    "can you help",
                    "i need help",
                    "general question",
                ],
                "agent": AgentType.RAG_RETRIEVAL,
                "base_confidence": 0.5,
            },
        }

        logger.info("Initialized query classifier with intent patterns")

    def classify_query(self, query: str) -> ClassificationResult:
        """Classify a query and determine the appropriate agent."""
        query_lower = query.lower().strip()

        # Calculate scores for each intent
        intent_scores = {}
        matched_keywords = {}

        for intent, patterns in self.intent_patterns.items():
            score, keywords = self._calculate_intent_score(query_lower, patterns)
            intent_scores[intent] = score
            matched_keywords[intent] = keywords

        # Find the best matching intent
        best_intent = max(intent_scores.keys(), key=lambda x: intent_scores[x])
        best_score = intent_scores[best_intent]

        # Apply confidence adjustments
        adjusted_confidence = self._adjust_confidence(
            query_lower, best_intent, best_score
        )

        # Get corresponding agent type
        agent_type = self.intent_patterns[best_intent]["agent"]

        # Generate reasoning
        reasoning = self._generate_reasoning(
            query, best_intent, adjusted_confidence, matched_keywords[best_intent]
        )

        return ClassificationResult(
            intent=best_intent,
            agent_type=agent_type,
            confidence=adjusted_confidence,
            reasoning=reasoning,
            keywords_matched=matched_keywords[best_intent],
        )

    def _calculate_intent_score(
        self, query: str, patterns: dict
    ) -> tuple[float, list[str]]:
        """Calculate score for a specific intent."""
        score = 0.0
        matched_keywords = []

        # Check keyword matches
        for keyword in patterns["keywords"]:
            if keyword in query:
                score += 1.0
                matched_keywords.append(keyword)

        # Check phrase matches (higher weight)
        for phrase in patterns["phrases"]:
            if phrase in query:
                score += 2.0
                matched_keywords.append(phrase)

        # Normalize score
        total_patterns = len(patterns["keywords"]) + len(patterns["phrases"])
        normalized_score = min(score / total_patterns, 1.0) if total_patterns > 0 else 0.0

        # Apply base confidence
        final_score = normalized_score * patterns["base_confidence"]

        return final_score, matched_keywords

    def _adjust_confidence(
        self, query: str, intent: QueryIntent, base_score: float
    ) -> float:
        """Apply confidence adjustments based on query characteristics."""
        confidence = base_score

        # Intent-specific confidence adjustments
        if intent == QueryIntent.ARCHITECTURE_DESIGN:
            # Boost confidence for design-related queries
            design_terms = ["architecture", "design", "pattern", "structure", "component"]
            if any(term in query.lower() for term in design_terms):
                confidence += 0.1
        elif intent == QueryIntent.TASK_PLANNING:
            # Boost confidence for task-related queries
            task_terms = ["task", "step", "plan", "implement", "build", "create"]
            if any(term in query.lower() for term in task_terms):
                confidence += 0.1

        # Boost confidence for clear question patterns
        if query.endswith("?"):
            confidence += 0.1

        # Boost confidence for specific technical terms
        technical_terms = [
            "api",
            "database",
            "server",
            "client",
            "framework",
            "library",
            "algorithm",
            "data structure",
            "protocol",
        ]
        if any(term in query for term in technical_terms):
            confidence += 0.05

        # Reduce confidence for very short queries
        if len(query.split()) < 3:
            confidence *= 0.8

        # Reduce confidence for very long queries (might be complex)
        if len(query.split()) > 20:
            confidence *= 0.9

        # Boost confidence for imperative statements
        imperative_starters = ["show", "find", "get", "list", "explain", "describe"]
        if any(query.startswith(starter) for starter in imperative_starters):
            confidence += 0.1

        return min(confidence, 0.95)  # Cap at 95%

    def _generate_reasoning(
        self, query: str, intent: QueryIntent, confidence: float, keywords: list[str]
    ) -> str:
        """Generate human-readable reasoning for the classification."""
        reasoning_parts = [f"Classified as {intent.value.replace('_', ' ').title()}"]

        # Include query characteristics in reasoning
        query_length = len(query.split())
        if query_length < 3:
            reasoning_parts.append("Short query")
        elif query_length > 20:
            reasoning_parts.append("Complex query")

        if keywords:
            reasoning_parts.append(f"Matched keywords: {', '.join(keywords[:3])}")

        if confidence > 0.8:
            reasoning_parts.append("High confidence classification")
        elif confidence > 0.6:
            reasoning_parts.append("Medium confidence classification")
        else:
            reasoning_parts.append("Low confidence classification")

        return " | ".join(reasoning_parts)

    def get_classification_stats(self) -> dict[str, int]:
        """Get statistics about available classification patterns."""
        stats = {}
        for intent, patterns in self.intent_patterns.items():
            stats[intent.value] = {
                "keywords_count": len(patterns["keywords"]),
                "phrases_count": len(patterns["phrases"]),
                "agent_type": patterns["agent"].value,
            }
        return stats
