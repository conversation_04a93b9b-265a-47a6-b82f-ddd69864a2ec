"""
Agent Router

This module provides routing logic to determine which agent(s) should handle
a query, with fallback strategies and multi-agent coordination.
"""

from dataclasses import dataclass
import logging
from typing import Any

from ..agents.base import AgentContext, AgentType
from ..agents.exceptions import AgentRoutingError
from .classifier import ClassificationResult, QueryClassifier

logger = logging.getLogger(__name__)


@dataclass
class RoutingDecision:
    """Decision about how to route a query."""

    primary_agent: AgentType
    secondary_agents: list[AgentType]
    routing_strategy: str
    confidence: float
    reasoning: str
    fallback_agents: list[AgentType]


class AgentRouter:
    """Routes queries to appropriate agents with fallback strategies."""

    def __init__(self, classifier: QueryClassifier | None = None):
        self.classifier = classifier or QueryClassifier()

        # Define routing strategies
        self.routing_strategies = {
            "single_agent": self._route_single_agent,
            "multi_agent": self._route_multi_agent,
            "fallback_chain": self._route_fallback_chain,
        }

        # Define agent capabilities and fallback chains
        self.agent_capabilities = {
            AgentType.ORCHESTRATOR: {
                "primary_capabilities": ["coordination", "synthesis", "routing"],
                "fallback_for": [],  # Orchestrator doesn't fallback to others
                "can_fallback_to": [],
            },
            AgentType.TECHNICAL_ARCHITECT: {
                "primary_capabilities": [
                    "architecture",
                    "design",
                    "patterns",
                    "standards",
                ],
                "fallback_for": [AgentType.TASK_PLANNER],
                "can_fallback_to": [AgentType.RAG_RETRIEVAL],
            },
            AgentType.TASK_PLANNER: {
                "primary_capabilities": [
                    "planning",
                    "breakdown",
                    "timeline",
                    "dependencies",
                ],
                "fallback_for": [AgentType.TECHNICAL_ARCHITECT],
                "can_fallback_to": [AgentType.RAG_RETRIEVAL],
            },
            AgentType.RAG_RETRIEVAL: {
                "primary_capabilities": [
                    "search",
                    "retrieval",
                    "factual",
                    "code_lookup",
                ],
                "fallback_for": [AgentType.TECHNICAL_ARCHITECT, AgentType.TASK_PLANNER],
                "can_fallback_to": [],  # RAG is the final fallback
            },
        }

        logger.info("Initialized agent router with routing strategies")

    def route_query(self, query: str, context: AgentContext, available_agents: set[AgentType]) -> RoutingDecision:
        """Route a query to the appropriate agent(s)."""
        try:
            # Classify the query
            classification = self.classifier.classify_query(query)

            # Determine routing strategy
            strategy = self._determine_routing_strategy(query, classification, context)

            # Execute routing strategy
            routing_func = self.routing_strategies[strategy]
            decision = routing_func(query, classification, context, available_agents)

            # Validate routing decision
            self._validate_routing_decision(decision, available_agents)

            logger.info(
                f"Routed query to {decision.primary_agent.value} "
                f"(strategy: {strategy}, confidence: {decision.confidence:.2f})"
            )

            return decision

        except Exception as e:
            logger.error(f"Routing failed: {e}")
            raise AgentRoutingError(
                f"Failed to route query: {e}",
                query_type=getattr(classification, "intent", "unknown"),
                available_agents=list(available_agents),
                cause=e,
            ) from e

    def _determine_routing_strategy(
        self, query: str, classification: ClassificationResult, context: AgentContext
    ) -> str:
        """Determine the appropriate routing strategy."""
        # Consider conversation context for routing decisions
        has_previous_context = (
            context.conversation_context and
            len(context.conversation_context.conversation_history) > 0
        )

        # If we have previous agent outputs, prefer continuing with same agent type
        if context.previous_agent_outputs and classification.confidence > 0.7:
            return "single_agent"

        # Check if query requires multiple agents
        if self._requires_multi_agent(query, classification):
            return "multi_agent"

        # Use fallback chain for low confidence or when context suggests complexity
        if classification.confidence < 0.6 or (has_previous_context and classification.confidence < 0.8):
            return "fallback_chain"

        # Default to single agent
        return "single_agent"

    def _requires_multi_agent(self, query: str, classification: ClassificationResult) -> bool:
        """Check if query requires multiple agents."""
        # If classification confidence is low, might benefit from multiple perspectives
        if classification.confidence < 0.7:
            return True

        # Complex queries that mention multiple domains
        multi_domain_indicators = [
            ("design", "implement"),  # Architecture + Planning
            ("plan", "architecture"),  # Planning + Architecture
            ("create", "design", "implement"),  # All three
        ]

        query_lower = query.lower()
        for indicators in multi_domain_indicators:
            if all(indicator in query_lower for indicator in indicators):
                return True

        # Very long queries might benefit from multi-agent approach
        return len(query.split()) > 25

    def _route_single_agent(
        self,
        query: str,
        classification: ClassificationResult,
        context: AgentContext,
        available_agents: set[AgentType],
    ) -> RoutingDecision:
        """Route to a single primary agent."""
        primary_agent = classification.agent_type

        # Consider context for agent selection - if we have previous successful agent outputs,
        # prefer continuing with the same agent type for consistency
        if context.previous_agent_outputs:
            for agent_type in context.previous_agent_outputs:
                if agent_type in available_agents and agent_type == primary_agent:
                    # Boost confidence when continuing with same agent
                    confidence_boost = 0.1
                    break
            else:
                confidence_boost = 0.0
        else:
            confidence_boost = 0.0

        # Check if primary agent is available
        if primary_agent not in available_agents:
            # Find fallback
            fallback_agents = self._get_fallback_agents(primary_agent, available_agents)
            if fallback_agents:
                primary_agent = fallback_agents[0]
            else:
                raise AgentRoutingError(
                    f"Primary agent {primary_agent.value} not available and no fallbacks found",
                    query_type=classification.intent.value,
                    available_agents=list(available_agents),
                )

        # Include query characteristics in reasoning
        query_length = len(query.split())
        reasoning_parts = [f"Single agent routing: {classification.reasoning}"]
        if query_length > 15:
            reasoning_parts.append("Complex query handled by single agent")
        if confidence_boost > 0:
            reasoning_parts.append("Continuing with previous agent for consistency")

        return RoutingDecision(
            primary_agent=primary_agent,
            secondary_agents=[],
            routing_strategy="single_agent",
            confidence=min(classification.confidence + confidence_boost, 1.0),
            reasoning=" | ".join(reasoning_parts),
            fallback_agents=self._get_fallback_agents(primary_agent, available_agents),
        )

    def _route_multi_agent(
        self,
        query: str,
        classification: ClassificationResult,
        context: AgentContext,
        available_agents: set[AgentType],
    ) -> RoutingDecision:
        """Route to multiple agents for complex queries."""
        primary_agent = classification.agent_type
        secondary_agents = []

        # Consider previous agent outputs to avoid redundancy
        previous_agents = set(context.previous_agent_outputs.keys()) if context.previous_agent_outputs else set()

        # Determine secondary agents based on query content
        query_lower = query.lower()

        if "design" in query_lower and "implement" in query_lower:
            # Architecture + Planning
            if primary_agent == AgentType.TECHNICAL_ARCHITECT and AgentType.TASK_PLANNER not in previous_agents:
                secondary_agents.append(AgentType.TASK_PLANNER)
            elif primary_agent == AgentType.TASK_PLANNER and AgentType.TECHNICAL_ARCHITECT not in previous_agents:
                secondary_agents.append(AgentType.TECHNICAL_ARCHITECT)

        # If we have conversation context, consider adding complementary agents
        if context.conversation_context and len(context.conversation_context.conversation_history) > 2:
            # For ongoing conversations, add the other agent type for comprehensive coverage
            all_agent_types = {AgentType.TECHNICAL_ARCHITECT, AgentType.TASK_PLANNER}
            potential_secondary = all_agent_types - {primary_agent} - previous_agents
            secondary_agents.extend(list(potential_secondary)[:1])  # Add one complementary agent

        # Filter available agents
        secondary_agents = [agent for agent in secondary_agents if agent in available_agents]

        reasoning_parts = [f"Multi-agent routing: {classification.reasoning}"]
        if previous_agents:
            reasoning_parts.append(f"Avoiding redundancy with previous agents: {[a.value for a in previous_agents]}")
        if secondary_agents:
            reasoning_parts.append(f"Secondary agents: {[a.value for a in secondary_agents]}")

        return RoutingDecision(
            primary_agent=primary_agent,
            secondary_agents=secondary_agents,
            routing_strategy="multi_agent",
            confidence=classification.confidence * 0.9,  # Slightly lower for complexity
            reasoning=" | ".join(reasoning_parts),
            fallback_agents=self._get_fallback_agents(primary_agent, available_agents),
        )

    def _route_fallback_chain(
        self,
        query: str,
        classification: ClassificationResult,
        context: AgentContext,
        available_agents: set[AgentType],
    ) -> RoutingDecision:
        """Route with fallback chain for uncertain classifications."""
        primary_agent = classification.agent_type
        fallback_agents = self._get_fallback_agents(primary_agent, available_agents)

        # Consider context for fallback strategy
        has_conversation_history = (
            context.conversation_context and
            len(context.conversation_context.conversation_history) > 0
        )

        # If we have previous successful agents, include them in fallback chain
        if context.previous_agent_outputs:
            successful_agents = list(context.previous_agent_outputs.keys())
            # Add successful agents to fallback chain if not already primary
            for agent in successful_agents:
                if agent != primary_agent and agent in available_agents and agent not in fallback_agents:
                    fallback_agents.insert(0, agent)

        # If confidence is very low or query is complex, start with RAG retrieval
        query_complexity = len(query.split()) > 20 or "?" in query
        should_use_rag = (classification.confidence < 0.4 or
                         (query_complexity and classification.confidence < 0.6))

        if should_use_rag and AgentType.RAG_RETRIEVAL in available_agents:
            primary_agent = AgentType.RAG_RETRIEVAL
            fallback_agents = [classification.agent_type, *fallback_agents]

        reasoning_parts = [f"Fallback routing: {classification.reasoning}"]
        if has_conversation_history:
            reasoning_parts.append("Using conversation context for fallback strategy")
        if query_complexity:
            reasoning_parts.append("Complex query detected")

        return RoutingDecision(
            primary_agent=primary_agent,
            secondary_agents=[],
            routing_strategy="fallback_chain",
            confidence=max(classification.confidence, 0.5),  # Boost confidence with fallback
            reasoning=" | ".join(reasoning_parts),
            fallback_agents=fallback_agents,
        )

    def _get_fallback_agents(self, primary_agent: AgentType, available_agents: set[AgentType]) -> list[AgentType]:
        """Get fallback agents for a primary agent."""
        if primary_agent not in self.agent_capabilities:
            return []

        fallback_candidates = self.agent_capabilities[primary_agent]["can_fallback_to"]
        return [agent for agent in fallback_candidates if agent in available_agents]

    def _validate_routing_decision(self, decision: RoutingDecision, available_agents: set[AgentType]) -> None:
        """Validate that routing decision is feasible."""
        # Check primary agent availability
        if decision.primary_agent not in available_agents:
            raise AgentRoutingError(
                f"Primary agent {decision.primary_agent.value} not available",
                available_agents=list(available_agents),
            )

        # Check secondary agents availability
        for agent in decision.secondary_agents:
            if agent not in available_agents:
                raise AgentRoutingError(
                    f"Secondary agent {agent.value} not available",
                    available_agents=list(available_agents),
                )

        # Check confidence bounds
        if not 0.0 <= decision.confidence <= 1.0:
            raise AgentRoutingError(
                f"Invalid confidence score: {decision.confidence}",
                routing_confidence=decision.confidence,
            )

    def get_routing_stats(self) -> dict[str, Any]:
        """Get routing statistics and capabilities."""
        return {
            "available_strategies": list(self.routing_strategies.keys()),
            "agent_capabilities": {
                agent.value: capabilities for agent, capabilities in self.agent_capabilities.items()
            },
            "classification_stats": self.classifier.get_classification_stats(),
        }
