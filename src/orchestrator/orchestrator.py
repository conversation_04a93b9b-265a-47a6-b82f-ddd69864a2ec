"""
Orchestrator Agent

This module provides the main orchestrator agent that coordinates workflow
execution and manages multi-agent interactions.
"""

import asyncio
import logging
import time
from typing import Any

from ..agents.base import (
    Agent,
    AgentContext,
    AgentResponse,
    AgentType,
    Message,
    MessageRole,
)
from ..agents.context import ConversationContextManager
from ..agents.exceptions import AgentTimeoutError
from ..agents.formatters import MarkdownFormatter
from ..agents.llm_client import LLMClient
from ..config import Settings
from .classifier import QueryClassifier
from .router import AgentRouter
from .synthesizer import ResponseSynthesizer

logger = logging.getLogger(__name__)


class OrchestratorAgent(Agent):
    """Main orchestrator agent that coordinates multi-agent workflows."""

    def __init__(
        self,
        settings: Settings,
        llm_client: LLMClient,
        formatter: MarkdownFormatter,
        context_manager: ConversationContextManager,
        agent_factory: Any,  # Avoid circular import
    ):
        super().__init__(AgentType.ORCHESTRATOR, settings)
        self.llm_client = llm_client
        self.formatter = formatter
        self.context_manager = context_manager
        self.agent_factory = agent_factory

        # Initialize orchestrator components
        self.classifier = QueryClassifier()
        self.router = AgentRouter(self.classifier)
        self.synthesizer = ResponseSynthesizer(formatter)

        # Configuration
        self.max_agent_timeout = 30.0
        self.max_fallback_attempts = 3
        self.enable_multi_agent = True
        self.enable_parallel_execution = True
        self.parallel_timeout_buffer = 5.0  # Extra time for parallel coordination

        # Agent registry
        self._agent_cache: dict[AgentType, Agent] = {}

        logger.info("Initialized Orchestrator Agent")

    def can_handle_query(self, query: str, context: AgentContext) -> float:
        """Orchestrator can handle any query (it routes to appropriate agents)."""
        # Orchestrator is the entry point and can handle any query by routing
        # Parameters are required by interface but not used in logic
        _ = query, context  # Mark as intentionally unused
        return 1.0  # Always confident - orchestrator is the entry point

    async def process_query(self, query: str, context: AgentContext) -> AgentResponse:
        """Process a query by routing to appropriate agents and synthesizing responses."""
        start_time = time.time()

        try:
            logger.info(f"Orchestrator processing query: {query[:100]}...")

            # Update conversation context
            await self._update_conversation_context(query, context)

            # Determine available agents
            available_agents = self._get_available_agents()

            # Route the query
            routing_decision = self.router.route_query(query, context, available_agents)

            # Execute the routing decision
            responses = await self._execute_routing_decision(query, context, routing_decision)

            # Synthesize responses
            synthesis_result = self.synthesizer.synthesize_responses(
                responses, routing_decision.routing_strategy, query
            )

            processing_time = time.time() - start_time

            # Create orchestrator response
            orchestrator_response = AgentResponse(
                agent_type=self.agent_type,
                content=synthesis_result.content,
                confidence=synthesis_result.confidence,
                sources=synthesis_result.sources,
                processing_time=processing_time,
                metadata={
                    "routing_strategy": routing_decision.routing_strategy,
                    "primary_agent": routing_decision.primary_agent.value,
                    "secondary_agents": [a.value for a in routing_decision.secondary_agents],
                    "synthesis_strategy": synthesis_result.synthesis_strategy,
                    "agent_count": len(responses),
                    **synthesis_result.metadata,
                },
            )

            # Update conversation context with response
            await self._update_conversation_context_with_response(orchestrator_response, context)

            self._update_stats(processing_time, success=True)
            logger.info(f"Orchestrator completed in {processing_time:.2f}s")

            return orchestrator_response

        except Exception as e:
            processing_time = time.time() - start_time
            self._update_stats(processing_time, success=False)

            logger.error(f"Orchestrator failed: {e}")

            # Try to provide a fallback response
            return await self._generate_fallback_response(query, context, e)

    async def _execute_routing_decision(
        self, query: str, context: AgentContext, routing_decision: Any
    ) -> list[AgentResponse]:
        """Execute the routing decision and collect responses with parallel processing."""
        responses = []

        try:
            if self.enable_parallel_execution and routing_decision.secondary_agents:
                # Parallel execution for multi-agent scenarios
                responses = await self._execute_agents_parallel(query, context, routing_decision)
            else:
                # Sequential execution for single agent or when parallel is disabled
                responses = await self._execute_agents_sequential(query, context, routing_decision)

            # If no successful responses, try fallback agents
            if not responses or all(r.confidence < 0.3 for r in responses):
                fallback_response = await self._try_fallback_agents(query, context, routing_decision.fallback_agents)
                if fallback_response:
                    responses.append(fallback_response)

            return responses

        except Exception as e:
            logger.error(f"Failed to execute routing decision: {e}")
            # Try fallback agents as last resort
            fallback_response = await self._try_fallback_agents(query, context, routing_decision.fallback_agents)
            return [fallback_response] if fallback_response else []

    async def _execute_agents_parallel(
        self, query: str, context: AgentContext, routing_decision: Any
    ) -> list[AgentResponse]:
        """Execute all agents in parallel for maximum performance."""
        all_agents = [routing_decision.primary_agent, *routing_decision.secondary_agents]
        tasks = []

        logger.info(f"Executing {len(all_agents)} agents in parallel: {[a.value for a in all_agents]}")

        # Create tasks for all agents
        for agent_type in all_agents:
            agent = self._get_agent(agent_type)
            task = asyncio.create_task(
                self._execute_agent_with_timeout(agent, query, context),
                name=f"agent_{agent_type.value}"
            )
            tasks.append((agent_type, task))

        # Wait for all tasks with timeout
        try:
            # Use a slightly longer timeout for parallel coordination
            timeout = self.max_agent_timeout + self.parallel_timeout_buffer

            # Wait for all tasks to complete
            done, pending = await asyncio.wait(
                [task for _, task in tasks],
                timeout=timeout,
                return_when=asyncio.ALL_COMPLETED
            )

            # Cancel any pending tasks
            for task in pending:
                task.cancel()
                logger.warning(f"Cancelled pending task: {task.get_name()}")

            # Collect successful responses
            responses = []
            for agent_type, task in tasks:
                if task in done:
                    try:
                        response = await task
                        responses.append(response)
                        logger.debug(f"Agent {agent_type.value} completed successfully")
                    except Exception as e:
                        logger.warning(f"Agent {agent_type.value} failed: {e}")
                else:
                    logger.warning(f"Agent {agent_type.value} timed out")

            # Sort responses by confidence (primary agent first if available)
            responses.sort(key=lambda r: (
                0 if r.agent_type == routing_decision.primary_agent else 1,
                -r.confidence
            ))

            return responses

        except Exception as e:
            logger.error(f"Parallel execution failed: {e}")
            # Cancel all tasks
            for _, task in tasks:
                if not task.done():
                    task.cancel()
            return []

    async def _execute_agents_sequential(
        self, query: str, context: AgentContext, routing_decision: Any
    ) -> list[AgentResponse]:
        """Execute agents sequentially (fallback method)."""
        responses = []

        try:
            # Execute primary agent first
            primary_agent = self._get_agent(routing_decision.primary_agent)
            primary_response = await self._execute_agent_with_timeout(primary_agent, query, context)
            responses.append(primary_response)
            logger.debug(f"Primary agent {routing_decision.primary_agent.value} completed")

            # Execute secondary agents if any
            if routing_decision.secondary_agents and self.enable_multi_agent:
                for agent_type in routing_decision.secondary_agents:
                    try:
                        agent = self._get_agent(agent_type)
                        response = await self._execute_agent_with_timeout(agent, query, context)
                        responses.append(response)
                        logger.debug(f"Secondary agent {agent_type.value} completed")
                    except Exception as e:
                        logger.warning(f"Secondary agent {agent_type.value} failed: {e}")

            return responses

        except Exception as e:
            logger.error(f"Sequential execution failed: {e}")
            return responses  # Return any partial results

    async def _execute_agent_with_timeout(self, agent: Agent, query: str, context: AgentContext) -> AgentResponse:
        """Execute an agent with timeout protection."""
        try:
            return await agent._execute_with_timeout(agent.process_query(query, context), self.max_agent_timeout)
        except AgentTimeoutError:
            logger.warning(f"Agent {agent.agent_type.value} timed out")
            raise
        except Exception as e:
            logger.error(f"Agent {agent.agent_type.value} failed: {e}")
            raise

    async def _try_fallback_agents(
        self, query: str, context: AgentContext, fallback_agents: list[AgentType]
    ) -> AgentResponse | None:
        """Try fallback agents in order."""
        for agent_type in fallback_agents[: self.max_fallback_attempts]:
            try:
                agent = self._get_agent(agent_type)
                response = await self._execute_agent_with_timeout(agent, query, context)
                logger.info(f"Fallback agent {agent_type.value} succeeded")
                return response
            except Exception as e:
                logger.warning(f"Fallback agent {agent_type.value} failed: {e}")
                continue

        return None

    def _get_agent(self, agent_type: AgentType) -> Agent:
        """Get or create an agent instance."""
        if agent_type not in self._agent_cache:
            self._agent_cache[agent_type] = self.agent_factory.create_agent(agent_type)
        return self._agent_cache[agent_type]

    def _get_available_agents(self) -> set[AgentType]:
        """Get set of available agent types."""
        # For now, all agents are available
        # In the future, this could check agent health, load, etc.
        return {
            AgentType.TECHNICAL_ARCHITECT,
            AgentType.TASK_PLANNER,
            AgentType.RAG_RETRIEVAL,
        }

    async def _update_conversation_context(self, query: str, context: AgentContext) -> None:
        """Update conversation context with the new query."""
        try:
            session_id = context.conversation_context.session_id
            message = Message(role=MessageRole.USER, content=query)
            await self.context_manager.add_message(session_id, message)
        except Exception as e:
            logger.warning(f"Failed to update conversation context: {e}")

    async def _update_conversation_context_with_response(self, response: AgentResponse, context: AgentContext) -> None:
        """Update conversation context with the response."""
        try:
            session_id = context.conversation_context.session_id
            message = Message(
                role=MessageRole.ASSISTANT,
                content=response.content,
                metadata={
                    "agent_type": response.agent_type.value,
                    "confidence": response.confidence,
                    "sources": response.sources,
                },
            )
            await self.context_manager.add_message(session_id, message)
        except Exception as e:
            logger.warning(f"Failed to update conversation context with response: {e}")

    async def _generate_fallback_response(self, query: str, context: AgentContext, error: Exception) -> AgentResponse:
        """Generate a fallback response when all else fails."""
        # Include context information in fallback response
        context_info = ""
        if context.conversation_context and context.conversation_context.conversation_history:
            context_info = f"\n\nContext: This appears to be related to our previous discussion about {context.conversation_context.current_repository or 'your project'}."

        error_message = (
            f"I apologize, but I encountered an error while processing your query: '{query}'\n\n"
            f"Error details: {error!s}\n\n"
            f"Please try rephrasing your question or contact support if the issue persists.{context_info}"
        )

        return AgentResponse(
            agent_type=self.agent_type,
            content=error_message,
            confidence=0.1,
            sources=[],
            metadata={
                "error_type": type(error).__name__,
                "fallback_response": True,
                "has_context": bool(context.conversation_context)
            },
        )

    def get_orchestrator_stats(self) -> dict[str, Any]:
        """Get orchestrator-specific statistics."""
        base_stats = self.get_stats()

        orchestrator_stats = {
            "classifier_stats": self.classifier.get_classification_stats(),
            "router_stats": self.router.get_routing_stats(),
            "synthesizer_stats": self.synthesizer.get_synthesis_stats(),
            "cached_agents": list(self._agent_cache.keys()),
            "configuration": {
                "max_agent_timeout": self.max_agent_timeout,
                "max_fallback_attempts": self.max_fallback_attempts,
                "enable_multi_agent": self.enable_multi_agent,
            },
        }

        return {**base_stats, **orchestrator_stats}
