"""
Ingestion Module

This module handles repository ingestion, file processing, and metadata extraction
for the LLM RAG Codebase Query System.
"""

from .base import (
    # Chunking components
    Chunk,
    ChunkContext,
    ChunkingPipeline,
    ChunkingStrategy,
    ChunkType,
    EmbeddedChunk,
    EmbeddingClient,
    # Embedding components
    EmbeddingMetadata,
    EmbeddingPipeline,
    FileFilter,
    FileLoader,
    FileMetadata,
    IngestionPipeline,
    MetadataExtractor,
    RepositoryInfo,
    RepositorySource,
    SearchResult,
    VectorStore,
)
from .embedding import (
    EmbeddingClientFactory,
    LocalEmbeddingClient,
    OpenAIEmbeddingClient,
)
from .embedding_pipeline import DefaultEmbeddingPipeline
from .exceptions import (
    AuthenticationError,
    ConfigurationError,
    FileProcessingError,
    IngestionError,
    MetadataExtractionError,
    RateLimitError,
    RepositoryError,
)
from .github import (
    GitHubClient,
    GitHubConnector,
    GitHubFileFilter,
    GitHubMetadataExtractor,
    GitHubRepositoryManager,
)
from .loader import UniversalFileLoader
from .pipeline import DefaultChunkingPipeline
from .strategies import (
    BaseChunkingStrategy,
    ChunkingStrategyFactory,
    CodeChunkingStrategy,
    ConfigChunkingStrategy,
    MarkdownChunkingStrategy,
    TextChunkingStrategy,
)
from .vector import (
    ChromaVectorStore,
    VectorStoreFactory,
)

__all__ = [
    "AuthenticationError",
    "BaseChunkingStrategy",
    "ChromaVectorStore",
    # Chunking components
    "Chunk",
    "ChunkContext",
    "ChunkType",
    "ChunkingPipeline",
    "ChunkingStrategy",
    "ChunkingStrategyFactory",
    "CodeChunkingStrategy",
    "ConfigChunkingStrategy",
    "ConfigurationError",
    "DefaultChunkingPipeline",
    # Embedding implementations
    "DefaultEmbeddingPipeline",
    "EmbeddedChunk",
    "EmbeddingClient",
    "EmbeddingClientFactory",
    # Embedding components
    "EmbeddingMetadata",
    "EmbeddingPipeline",
    "FileFilter",
    "FileLoader",
    "FileMetadata",
    "FileProcessingError",
    # GitHub implementations
    "GitHubClient",
    "GitHubConnector",
    "GitHubFileFilter",
    "GitHubMetadataExtractor",
    "GitHubRepositoryManager",
    # Exceptions
    "IngestionError",
    "IngestionPipeline",
    "LocalEmbeddingClient",
    "MarkdownChunkingStrategy",
    "MetadataExtractionError",
    "MetadataExtractor",
    "OpenAIEmbeddingClient",
    "RateLimitError",
    "RepositoryError",
    "RepositoryInfo",
    # Base classes
    "RepositorySource",
    "SearchResult",
    "TextChunkingStrategy",
    # Chunking implementations
    "UniversalFileLoader",
    "VectorStore",
    "VectorStoreFactory",
]
