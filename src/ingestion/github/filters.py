"""
GitHub File Filter

This module provides file filtering capabilities based on file type, size,
and exclusion rules as defined in the project configuration and docs/rules.md.
"""

import logging
from pathlib import Path
import re
from typing import Any

from ...config import Settings
from ..base import FileFilter

logger = logging.getLogger(__name__)


class GitHubFileFilter(FileFilter):
    """File filter for GitHub repositories based on project rules."""

    # Default exclusion patterns from docs/rules.md
    DEFAULT_EXCLUSIONS = {
        # Build artifacts and dependencies
        "node_modules/",
        "vendor/",
        "__pycache__/",
        ".pytest_cache/",
        "dist/",
        "build/",
        "target/",
        ".gradle/",
        ".mvn/",
        # IDE and editor files
        ".vscode/",
        ".idea/",
        "*.swp",
        "*.swo",
        "*~",
        ".DS_Store",
        "Thumbs.db",
        # Version control
        ".git/",
        ".svn/",
        ".hg/",
        # Logs and temporary files
        "*.log",
        "*.tmp",
        "*.temp",
        ".env.local",
        ".env.*.local",
        # Binary and media files
        "*.exe",
        "*.dll",
        "*.so",
        "*.dylib",
        "*.bin",
        "*.img",
        "*.iso",
        "*.dmg",
        "*.zip",
        "*.tar",
        "*.gz",
        "*.rar",
        "*.7z",
        "*.pdf",
        "*.doc",
        "*.docx",
        "*.xls",
        "*.xlsx",
        "*.ppt",
        "*.pptx",
        "*.jpg",
        "*.jpeg",
        "*.png",
        "*.gif",
        "*.bmp",
        "*.svg",
        "*.ico",
        "*.mp3",
        "*.mp4",
        "*.avi",
        "*.mov",
        "*.wmv",
        "*.flv",
        "*.webm",
        # Package files
        "*.deb",
        "*.rpm",
        "*.msi",
        "*.pkg",
        # Database files
        "*.db",
        "*.sqlite",
        "*.sqlite3",
        # Certificate and key files
        "*.pem",
        "*.key",
        "*.crt",
        "*.cer",
        "*.p12",
        "*.pfx",
    }

    # Priority boost for important directories
    PRIORITY_BOOSTS = {
        "docs/": 0.2,
        "documentation/": 0.2,
        "README": 0.15,
        "CHANGELOG": 0.1,
        "LICENSE": 0.1,
        "CONTRIBUTING": 0.1,
        "src/": 0.05,
        "lib/": 0.05,
        "app/": 0.05,
    }

    def __init__(self, settings: Settings, additional_exclusions: set[str] | None = None):
        """Initialize file filter with configuration."""
        self.settings = settings
        self.max_file_size = settings.max_file_size
        self.allowed_file_types = set(settings.allowed_file_types)

        # Combine default and additional exclusions
        self.exclusions = self.DEFAULT_EXCLUSIONS.copy()
        if additional_exclusions:
            self.exclusions.update(additional_exclusions)

        # Compile regex patterns for efficiency
        self._exclusion_patterns = self._compile_exclusion_patterns()

        logger.info(f"Initialized file filter with {len(self.allowed_file_types)} allowed types")
        logger.debug(f"Allowed file types: {self.allowed_file_types}")

    def _compile_exclusion_patterns(self) -> list[re.Pattern]:
        """Compile exclusion patterns into regex objects."""
        patterns = []
        for exclusion in self.exclusions:
            try:
                # Convert glob-like patterns to regex
                pattern = exclusion.replace("*", ".*").replace("?", ".")
                if exclusion.endswith("/"):
                    # Directory pattern - match if path starts with this
                    pattern = f"^{pattern}.*"
                else:
                    # File pattern - match if path ends with this or contains it
                    pattern = f".*{pattern}$"

                patterns.append(re.compile(pattern, re.IGNORECASE))
            except re.error as e:
                logger.warning(f"Invalid exclusion pattern '{exclusion}': {e}")

        return patterns

    def should_include_file(
        self, file_path: str, file_size: int, file_metadata: dict[str, Any] | None = None
    ) -> bool:
        """Determine if a file should be included in processing."""

        # Check file size limit
        if file_size > self.max_file_size:
            logger.debug(f"Excluding {file_path}: size {file_size} exceeds limit {self.max_file_size}")
            return False

        # Check exclusion patterns
        if self._is_excluded(file_path):
            logger.debug(f"Excluding {file_path}: matches exclusion pattern")
            return False

        # Check file extension
        file_extension = Path(file_path).suffix.lstrip(".")
        if file_extension and file_extension not in self.allowed_file_types:
            logger.debug(f"Excluding {file_path}: extension '{file_extension}' not in allowed types")
            return False

        # Special case: include files without extensions if they're common config/doc files
        if not file_extension:
            filename = Path(file_path).name.upper()
            common_files = {
                "README",
                "LICENSE",
                "CHANGELOG",
                "CONTRIBUTING",
                "DOCKERFILE",
                "MAKEFILE",
                "RAKEFILE",
                "GEMFILE",
            }
            if filename not in common_files:
                logger.debug(f"Excluding {file_path}: no extension and not a common file")
                return False

        # Check if file is empty (if metadata available)
        if file_metadata and file_metadata.get("size", 0) == 0:
            logger.debug(f"Excluding {file_path}: empty file")
            return False

        logger.debug(f"Including {file_path}: passed all filters")
        return True

    def _is_excluded(self, file_path: str) -> bool:
        """Check if file path matches any exclusion pattern."""
        normalized_path = file_path.replace("\\", "/")  # Normalize path separators

        return any(pattern.match(normalized_path) for pattern in self._exclusion_patterns)

    def filter_files(self, file_paths: list[str]) -> list[str]:
        """Filter a list of file paths."""
        included_files = []
        excluded_count = 0

        for file_path in file_paths:
            # We don't have file size here, so use a reasonable default
            # The actual size check will happen during processing
            if self.should_include_file(file_path, 0):  # Size will be checked later
                included_files.append(file_path)
            else:
                excluded_count += 1

        logger.info(f"Filtered {len(file_paths)} files: {len(included_files)} included, " f"{excluded_count} excluded")

        return included_files

    def get_file_priority(self, file_path: str) -> float:
        """Get priority score for a file (higher = more important)."""
        priority = 0.0
        normalized_path = file_path.replace("\\", "/").lower()

        # Check for priority boosts based on path
        for path_prefix, boost in self.PRIORITY_BOOSTS.items():
            if normalized_path.startswith(path_prefix.lower()):
                priority += boost
                break

        # Check for specific filename patterns
        filename = Path(file_path).name.upper()

        # Documentation files get high priority
        if any(doc_pattern in filename for doc_pattern in ["README", "DOC", "GUIDE"]):
            priority += 0.15

        # Configuration files get medium priority
        if any(config_pattern in filename for config_pattern in ["CONFIG", "SETTINGS", "ENV"]):
            priority += 0.1

        # Main entry points get medium priority
        if filename in ["MAIN.PY", "INDEX.JS", "APP.PY", "SERVER.PY", "__INIT__.PY"]:
            priority += 0.1

        # Test files get lower priority
        if any(test_pattern in normalized_path for test_pattern in ["test", "spec", "__test__"]):
            priority -= 0.05

        return max(0.0, priority)  # Ensure non-negative priority

    def get_filter_stats(self) -> dict[str, Any]:
        """Get statistics about the filter configuration."""
        return {
            "max_file_size": self.max_file_size,
            "allowed_file_types": list(self.allowed_file_types),
            "exclusion_count": len(self.exclusions),
            "priority_boost_count": len(self.PRIORITY_BOOSTS),
        }

    def add_exclusion(self, pattern: str) -> None:
        """Add a new exclusion pattern."""
        self.exclusions.add(pattern)
        self._exclusion_patterns = self._compile_exclusion_patterns()
        logger.info(f"Added exclusion pattern: {pattern}")

    def remove_exclusion(self, pattern: str) -> None:
        """Remove an exclusion pattern."""
        if pattern in self.exclusions:
            self.exclusions.remove(pattern)
            self._exclusion_patterns = self._compile_exclusion_patterns()
            logger.info(f"Removed exclusion pattern: {pattern}")
        else:
            logger.warning(f"Exclusion pattern not found: {pattern}")
