# GitHub Connector

The GitHub Connector is a comprehensive system for ingesting GitHub repositories, extracting metadata, and preparing content for the RAG pipeline. It follows SOLID principles and provides a robust, extensible architecture for repository processing.

## Architecture

The GitHub Connector consists of five main components:

```text
GitHubConnector (Main Orchestrator)
├── GitHubClient (GitHub API interactions)
├── GitHubRepositoryManager (Local repository operations)
├── GitHubFileFilter (Content filtering and prioritization)
├── GitHubMetadataExtractor (Metadata extraction)
└── Base classes and exceptions (Abstract interfaces)
```

## Components

### GitHubClient

Handles all GitHub API interactions with comprehensive error handling and rate limiting.

**Features:**
- OAuth authentication with public access fallback
- Automatic rate limit detection and throttling
- Repository information retrieval
- File listing and content fetching
- Robust error handling with detailed context

**Usage:**
```python
from src.ingestion.github import GitHubClient
from src.config import get_settings

client = GitHubClient(get_settings())
await client.authenticate()

repo_info = await client.get_repository_info("https://github.com/owner/repo")
files = await client.list_files("https://github.com/owner/repo", "main")
```

### GitHubRepositoryManager

Manages local repository operations using GitPython for efficient file access.

**Features:**
- Repository cloning with authentication
- Incremental updates and branch switching
- Local file management and cleanup
- Git metadata extraction (commits, authors, etc.)

**Usage:**
```python
from src.ingestion.github import GitHubRepositoryManager

manager = GitHubRepositoryManager(get_settings())
local_path = await manager.clone_repository("https://github.com/owner/repo")
files = await manager.get_file_list(local_path)
```

### GitHubFileFilter

Intelligent file filtering system with configurable exclusion patterns and priority scoring.

**Features:**
- 50+ default exclusion patterns (build artifacts, dependencies, etc.)
- File type and size filtering
- Priority scoring system for important files
- Dynamic pattern management

**Default Exclusions:**
- Build artifacts: `node_modules/`, `__pycache__/`, `dist/`, `build/`
- IDE files: `.vscode/`, `.idea/`, `*.swp`
- Binary files: `*.exe`, `*.dll`, `*.so`, `*.png`, `*.jpg`
- Logs and temporary files: `*.log`, `*.tmp`, `.env.local`

**Priority Scoring:**
- `docs/` directory: +0.2
- README files: +0.15
- Configuration files: +0.1
- Main entry points: +0.1
- Source directories: +0.05
- Test files: -0.05 (clamped to 0.0)

**Usage:**
```python
from src.ingestion.github import GitHubFileFilter

filter = GitHubFileFilter(get_settings())
filtered_files = filter.filter_files(all_files)
priority = filter.get_file_priority("docs/api.md")  # Returns 0.2
```

### GitHubMetadataExtractor

Extracts comprehensive metadata from files and repositories.

**Features:**
- File metadata: size, type, language, timestamps
- Git lineage: commit SHA, author, branch information
- Language detection for 30+ programming languages
- Repository-level aggregation

**Supported Languages:**
Python, JavaScript, TypeScript, Java, C++, C#, Go, Rust, PHP, Ruby, Swift, Kotlin, and many more.

**Usage:**
```python
from src.ingestion.github import GitHubMetadataExtractor

extractor = GitHubMetadataExtractor(get_settings())
file_metadata = await extractor.extract_file_metadata("src/main.py", repo_path)
repo_metadata = await extractor.extract_repository_metadata(repo_path)
```

### GitHubConnector

Main orchestrator that coordinates all components for complete repository ingestion.

**Features:**
- End-to-end repository processing
- Batch file processing with error resilience
- Ingestion status tracking
- Comprehensive cleanup and resource management
- Detailed progress reporting

**Usage:**
```python
from src.ingestion.github import GitHubConnector

connector = GitHubConnector(get_settings())

# Ingest complete repository
result = await connector.ingest_repository(
    "https://github.com/owner/repo",
    branch="main",
    force_refresh=False
)

# Check ingestion status
status = await connector.get_ingestion_status("https://github.com/owner/repo")

# Get connector statistics
stats = connector.get_connector_stats()
```

## Configuration

The GitHub Connector uses the application's configuration system. Key settings:

```python
# GitHub API settings
GITHUB_TOKEN=your_github_token_here
GITHUB_API_URL=https://api.github.com

# File processing settings
MAX_FILE_SIZE=10485760  # 10MB
ALLOWED_FILE_TYPES=["py", "js", "ts", "md", "txt", "json", "yaml", "yml"]
TEMP_DIR=/tmp/rag_processing
```

## Error Handling

The connector uses a comprehensive exception hierarchy:

- `IngestionError` - Base exception for all ingestion errors
- `AuthenticationError` - GitHub authentication failures
- `RepositoryError` - Repository access or processing errors
- `FileProcessingError` - File-level processing errors
- `MetadataExtractionError` - Metadata extraction failures
- `RateLimitError` - GitHub API rate limit exceeded
- `ConfigurationError` - Invalid or missing configuration

All exceptions include detailed context and support for error chaining.

## Testing

The GitHub Connector includes comprehensive test coverage:

- **Unit Tests**: 42 tests covering individual components
- **Integration Tests**: 11 tests covering end-to-end workflows
- **Total Coverage**: 53 tests with >90% code coverage

Run tests with:
```bash
uv run python -m pytest tests/unit/test_github_*.py -v
uv run python -m pytest tests/integration/test_github_*.py -v
```

## Performance Considerations

- **Batch Processing**: Files are processed in configurable batches (default: 50)
- **Async Operations**: All I/O operations use async/await for efficiency
- **Rate Limiting**: Automatic GitHub API rate limit handling
- **Shallow Cloning**: Uses Git shallow clones for faster repository access
- **Memory Management**: Streaming file processing for large repositories

## Examples

See `examples/github_connector_usage.py` for comprehensive usage examples including:

- Basic repository ingestion
- Custom file filtering
- Error handling patterns
- Advanced configuration options

## Future Enhancements

- Support for GitLab and Bitbucket
- Incremental ingestion with change detection
- Parallel repository processing
- Enhanced language detection
- Custom metadata extractors
