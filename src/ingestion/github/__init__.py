"""
GitHub Integration Module

This module provides GitHub-specific implementations for repository ingestion,
including API client, repository management, and metadata extraction.
"""

from .client import GitHubClient
from .connector import GitHubConnector
from .filters import GitHubFileFilter
from .metadata import GitHubMetadataExtractor
from .repository import GitHubRepositoryManager

__all__ = [
    "GitHubClient",
    "GitHubConnector",
    "GitHubFileFilter",
    "GitHubMetadataExtractor",
    "GitHubRepositoryManager",
]
