"""
GitHub Connector

This module provides the main orchestrator for GitHub repository ingestion,
coordinating all components to provide a unified interface for repository processing.
"""

import asyncio
from datetime import datetime
import logging
from pathlib import Path
from typing import Any

from ...config import Settings
from ..base import FileMetadata, IngestionPipeline
from ..exceptions import (
    IngestionError,
)
from .client import GitHubClient
from .filters import GitHubFileFilter
from .metadata import GitHubMetadataExtractor
from .repository import GitHubRepositoryManager

logger = logging.getLogger(__name__)


class GitHubConnector(IngestionPipeline):
    """Main orchestrator for GitHub repository ingestion."""

    def __init__(
        self,
        settings: Settings,
        github_client: GitHubClient | None = None,
        repository_manager: GitHubRepositoryManager | None = None,
        file_filter: GitHubFileFilter | None = None,
        metadata_extractor: GitHubMetadataExtractor | None = None,
    ):
        """Initialize GitHub connector with dependencies."""
        self.settings = settings

        # Initialize components with dependency injection
        self.github_client = github_client or GitHubClient(settings)
        self.repository_manager = repository_manager or GitHubRepositoryManager(settings)
        self.file_filter = file_filter or GitHubFileFilter(settings)
        self.metadata_extractor = metadata_extractor or GitHubMetadataExtractor(settings)

        # Track ingestion status
        self._ingestion_status: dict[str, dict[str, Any]] = {}

        logger.info("Initialized GitHub connector")

    async def ingest_repository(
        self, repository_url: str, branch: str = "main", force_refresh: bool = False
    ) -> dict[str, Any]:
        """Ingest a complete GitHub repository."""
        ingestion_id = f"{repository_url}#{branch}"
        start_time = datetime.now()

        try:
            logger.info(f"Starting ingestion of repository: {repository_url} (branch: {branch})")

            # Update status
            self._update_ingestion_status(
                ingestion_id,
                "starting",
                {
                    "repository_url": repository_url,
                    "branch": branch,
                    "start_time": start_time.isoformat(),
                },
            )

            # Step 1: Get repository information
            logger.info("Step 1: Getting repository information...")
            repo_info = await self.github_client.get_repository_info(repository_url)

            # Use repository's default branch if main doesn't exist
            if branch == "main" and repo_info.branch != "main":
                branch = repo_info.branch
                logger.info(f"Using repository default branch: {branch}")

            # Step 2: Clone repository locally
            logger.info("Step 2: Cloning repository...")
            local_path = await self.repository_manager.clone_repository(repository_url, branch, force_refresh)

            self._update_ingestion_status(
                ingestion_id,
                "cloned",
                {
                    "local_path": str(local_path),
                    "repository_info": repo_info.__dict__,
                },
            )

            # Step 3: Get file list and filter
            logger.info("Step 3: Discovering and filtering files...")
            all_files = await self.repository_manager.get_file_list(local_path)
            filtered_files = self.file_filter.filter_files(all_files)

            logger.info(f"Found {len(all_files)} total files, {len(filtered_files)} after filtering")

            # Step 4: Extract metadata for filtered files
            logger.info("Step 4: Extracting file metadata...")
            file_metadata_list = await self.ingest_files(filtered_files, local_path)

            # Step 5: Extract repository metadata
            logger.info("Step 5: Extracting repository metadata...")
            repo_metadata = await self.metadata_extractor.extract_repository_metadata(local_path)

            # Step 6: Apply priority scoring
            logger.info("Step 6: Applying priority scoring...")
            for file_metadata in file_metadata_list:
                priority = self.file_filter.get_file_priority(file_metadata.file_path)
                file_metadata.priority = priority

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # Prepare final result
            result = {
                "repository_url": repository_url,
                "branch": branch,
                "repository_info": repo_info.__dict__,
                "repository_metadata": repo_metadata,
                "total_files": len(all_files),
                "processed_files": len(file_metadata_list),
                "file_metadata": [fm.to_dict() for fm in file_metadata_list],
                "ingestion_duration": duration,
                "ingestion_time": end_time.isoformat(),
                "local_path": str(local_path),
            }

            self._update_ingestion_status(
                ingestion_id,
                "completed",
                {
                    "end_time": end_time.isoformat(),
                    "duration": duration,
                    "total_files": len(all_files),
                    "processed_files": len(file_metadata_list),
                },
            )

            logger.info(f"Successfully completed repository ingestion in {duration:.2f} seconds")
            return result

        except Exception as e:
            error_time = datetime.now()
            duration = (error_time - start_time).total_seconds()

            logger.error(f"Repository ingestion failed after {duration:.2f} seconds: {e}")

            self._update_ingestion_status(
                ingestion_id,
                "failed",
                {
                    "error": str(e),
                    "error_time": error_time.isoformat(),
                    "duration": duration,
                },
            )

            raise IngestionError(
                f"Repository ingestion failed: {e}",
                details={
                    "repository_url": repository_url,
                    "branch": branch,
                    "duration": duration,
                },
                cause=e,
            ) from e

    async def ingest_files(self, file_paths: list[str], repository_path: Path) -> list[FileMetadata]:
        """Ingest specific files from a repository."""
        try:
            logger.info(f"Processing {len(file_paths)} files...")

            file_metadata_list = []
            batch_size = 50  # Process files in batches to avoid memory issues

            for i in range(0, len(file_paths), batch_size):
                batch = file_paths[i : i + batch_size]
                logger.debug(f"Processing batch {i//batch_size + 1}: {len(batch)} files")

                # Process batch concurrently
                batch_tasks = [self._process_single_file(file_path, repository_path) for file_path in batch]

                batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)

                # Collect successful results and log errors
                for file_path, result in zip(batch, batch_results, strict=False):
                    if isinstance(result, Exception):
                        logger.warning(f"Failed to process file {file_path}: {result}")
                    elif result:
                        file_metadata_list.append(result)

            logger.info(f"Successfully processed {len(file_metadata_list)} files")
            return file_metadata_list

        except Exception as e:
            logger.error(f"Failed to ingest files: {e}")
            raise IngestionError(f"File ingestion failed: {e}", details={"file_count": len(file_paths)}, cause=e) from e

    async def _process_single_file(self, file_path: str, repository_path: Path) -> FileMetadata | None:
        """Process a single file and extract its metadata."""
        try:
            full_path = repository_path / file_path

            # Check file size before processing
            if not full_path.exists():
                logger.warning(f"File not found: {file_path}")
                return None

            file_size = full_path.stat().st_size

            # Double-check with file filter (now with actual size)
            if not self.file_filter.should_include_file(file_path, file_size):
                logger.debug(f"File excluded by filter: {file_path}")
                return None

            # Extract metadata
            return await self.metadata_extractor.extract_file_metadata(file_path, repository_path)


        except Exception as e:
            logger.debug(f"Error processing file {file_path}: {e}")
            return None

    async def get_ingestion_status(self, repository_url: str) -> dict[str, Any]:
        """Get the status of repository ingestion."""
        # Find status for any branch of this repository
        for _ingestion_id, status in self._ingestion_status.items():
            if status.get("repository_url") == repository_url:
                return status

        return {
            "repository_url": repository_url,
            "status": "not_found",
            "message": "No ingestion found for this repository",
        }

    def _update_ingestion_status(self, ingestion_id: str, status: str, details: dict[str, Any]) -> None:
        """Update ingestion status."""
        if ingestion_id not in self._ingestion_status:
            self._ingestion_status[ingestion_id] = {}

        self._ingestion_status[ingestion_id].update(
            {"status": status, "last_updated": datetime.now().isoformat(), **details}
        )

    async def cleanup(self, repository_url: str | None = None) -> None:
        """Clean up temporary files and resources."""
        try:
            if repository_url:
                # Clean up specific repository
                local_path = self.repository_manager._get_repository_local_path(repository_url)
                await self.repository_manager.cleanup(local_path)
            else:
                # Clean up all temporary files
                await self.repository_manager.cleanup()

            logger.info("Cleanup completed successfully")

        except Exception as e:
            logger.warning(f"Cleanup failed: {e}")

    def get_connector_stats(self) -> dict[str, Any]:
        """Get statistics about the connector."""
        return {
            "total_ingestions": len(self._ingestion_status),
            "filter_stats": self.file_filter.get_filter_stats(),
            "temp_directory": str(self.repository_manager.temp_dir),
            "settings": {
                "max_file_size": self.settings.max_file_size,
                "allowed_file_types": self.settings.allowed_file_types,
                "github_api_url": self.settings.github_api_url,
            },
        }
