"""
GitHub API Client

This module provides a comprehensive GitHub API client with authentication,
rate limiting, and error handling capabilities.
"""

from datetime import UTC, datetime
import logging
from pathlib import Path

from github import Github, GithubException
from github.Repository import Repository

from ...config import Settings
from ..base import RepositoryInfo, RepositorySource
from ..exceptions import (
    AuthenticationError,
    RateLimitError,
    RepositoryError,
)

logger = logging.getLogger(__name__)


class GitHubClient(RepositorySource):
    """GitHub API client with authentication and rate limiting."""

    def __init__(self, settings: Settings):
        """Initialize GitHub client with configuration."""
        self.settings = settings
        self._github: Github | None = None
        self._authenticated = False
        self._rate_limit_remaining = 5000
        self._rate_limit_reset_time: datetime | None = None

    async def authenticate(self) -> bool:
        """Authenticate with GitHub API."""
        try:
            if self.settings.github_token:
                self._github = Github(
                    self.settings.github_token,
                    base_url=self.settings.github_api_url,
                    timeout=30,
                )
                # Test authentication
                user = self._github.get_user()
                logger.info(f"Authenticated as GitHub user: {user.login}")
                self._authenticated = True
            else:
                # Use public access (rate limited)
                self._github = Github(
                    base_url=self.settings.github_api_url,
                    timeout=30,
                )
                logger.info("Using GitHub public API (rate limited)")
                self._authenticated = True

            # Update rate limit info
            await self._update_rate_limit_info()
            return True

        except GithubException as e:
            logger.error(f"GitHub authentication failed: {e}")
            raise AuthenticationError(
                "Failed to authenticate with GitHub",
                service="github",
                details={"error": str(e), "status": e.status},
                cause=e,
            ) from e
        except Exception as e:
            logger.error(f"Unexpected error during GitHub authentication: {e}")
            raise AuthenticationError("Unexpected authentication error", service="github", cause=e) from e

    async def _update_rate_limit_info(self) -> None:
        """Update rate limit information."""
        if not self._github:
            return

        try:
            rate_limit = self._github.get_rate_limit()
            self._rate_limit_remaining = rate_limit.core.remaining
            self._rate_limit_reset_time = rate_limit.core.reset

            logger.debug(
                f"GitHub rate limit: {self._rate_limit_remaining} remaining, "
                f"resets at {self._rate_limit_reset_time}"
            )

        except GithubException as e:
            logger.warning(f"Failed to get rate limit info: {e}")

    async def _check_rate_limit(self) -> None:
        """Check and handle rate limiting."""
        if self._rate_limit_remaining <= 10:  # Conservative threshold
            if self._rate_limit_reset_time:
                wait_time = (self._rate_limit_reset_time - datetime.now(UTC)).total_seconds()
                if wait_time > 0:
                    logger.warning(f"Rate limit nearly exceeded, waiting {wait_time} seconds")
                    raise RateLimitError(
                        "GitHub API rate limit nearly exceeded",
                        service="github",
                        retry_after=int(wait_time),
                        details={
                            "remaining": self._rate_limit_remaining,
                            "reset_time": self._rate_limit_reset_time.isoformat(),
                        },
                    )

    def _parse_repository_url(self, repository_url: str) -> tuple[str, str]:
        """Parse repository URL to extract owner and repo name."""
        # Handle various GitHub URL formats
        if repository_url.startswith("https://github.com/"):
            path = repository_url.replace("https://github.com/", "").rstrip("/")
        elif repository_url.startswith("**************:"):
            path = repository_url.replace("**************:", "").replace(".git", "")
        else:
            # Assume it's already in owner/repo format
            path = repository_url

        parts = path.split("/")
        if len(parts) != 2:
            raise RepositoryError(
                f"Invalid GitHub repository URL format: {repository_url}",
                repository_url=repository_url,
                operation="parse_url",
            )

        return parts[0], parts[1]

    async def get_repository_info(self, repository_url: str) -> RepositoryInfo:
        """Get information about a GitHub repository."""
        if not self._authenticated:
            await self.authenticate()

        await self._check_rate_limit()

        try:
            owner, repo_name = self._parse_repository_url(repository_url)
            repo = self._github.get_repo(f"{owner}/{repo_name}")

            # Get default branch
            default_branch = repo.default_branch

            # Get latest commit on default branch
            commits = repo.get_commits(sha=default_branch)
            latest_commit = commits[0] if commits.totalCount > 0 else None

            return RepositoryInfo(
                url=repository_url,
                name=repo.name,
                owner=repo.owner.login,
                branch=default_branch,
                commit_sha=latest_commit.sha if latest_commit else "",
                is_private=repo.private,
                description=repo.description,
                language=repo.language,
                size=repo.size,
                created_at=repo.created_at,
                updated_at=repo.updated_at,
            )

        except GithubException as e:
            logger.error(f"Failed to get repository info for {repository_url}: {e}")
            raise RepositoryError(
                f"Failed to get repository information: {e}",
                repository_url=repository_url,
                operation="get_info",
                details={"error": str(e), "status": e.status},
                cause=e,
            ) from e
        finally:
            await self._update_rate_limit_info()

    async def list_files(self, repository_url: str, branch: str = "main") -> list[str]:
        """List all files in the repository."""
        if not self._authenticated:
            await self.authenticate()

        try:
            owner, repo_name = self._parse_repository_url(repository_url)
            repo = self._github.get_repo(f"{owner}/{repo_name}")

            # Use the repository's default branch if "main" doesn't exist
            if branch == "main":
                try:
                    repo.get_branch("main")
                except GithubException:
                    branch = repo.default_branch
                    logger.info(f"Branch 'main' not found, using default branch: {branch}")

            files = []
            await self._list_files_recursive(repo, "", branch, files)

            logger.info(f"Found {len(files)} files in {repository_url}")
            return files

        except GithubException as e:
            logger.error(f"Failed to list files for {repository_url}: {e}")
            raise RepositoryError(
                f"Failed to list repository files: {e}",
                repository_url=repository_url,
                operation="list_files",
                details={"error": str(e), "status": e.status, "branch": branch},
                cause=e,
            ) from e

    async def _list_files_recursive(self, repo: Repository, path: str, branch: str, files: list[str]) -> None:
        """Recursively list files in a directory."""
        await self._check_rate_limit()

        try:
            contents = repo.get_contents(path, ref=branch)
            if not isinstance(contents, list):
                contents = [contents]

            for content in contents:
                if content.type == "file":
                    files.append(content.path)
                elif content.type == "dir":
                    await self._list_files_recursive(repo, content.path, branch, files)

        except GithubException as e:
            if e.status == 404:
                logger.warning(f"Path not found: {path}")
            else:
                raise
        finally:
            await self._update_rate_limit_info()

    async def get_file_content(self, repository_url: str, file_path: str, branch: str = "main") -> str:
        """Get the content of a specific file."""
        if not self._authenticated:
            await self.authenticate()

        await self._check_rate_limit()

        try:
            owner, repo_name = self._parse_repository_url(repository_url)
            repo = self._github.get_repo(f"{owner}/{repo_name}")

            # Use the repository's default branch if "main" doesn't exist
            if branch == "main":
                try:
                    repo.get_branch("main")
                except GithubException:
                    branch = repo.default_branch

            file_content = repo.get_contents(file_path, ref=branch)

            if isinstance(file_content, list):
                raise RepositoryError(
                    f"Path {file_path} is a directory, not a file",
                    repository_url=repository_url,
                    operation="get_file_content",
                )

            return file_content.decoded_content.decode("utf-8")

        except GithubException as e:
            logger.error(f"Failed to get file content for {file_path}: {e}")
            raise RepositoryError(
                f"Failed to get file content: {e}",
                repository_url=repository_url,
                operation="get_file_content",
                details={"error": str(e), "status": e.status, "file_path": file_path, "branch": branch},
                cause=e,
            ) from e
        finally:
            await self._update_rate_limit_info()

    async def clone_repository(self, repository_url: str, local_path: Path, branch: str = "main") -> Path:
        """Clone repository to local path (delegated to RepositoryManager)."""
        # This method is implemented in GitHubRepositoryManager
        # which handles the actual git operations
        raise NotImplementedError("Repository cloning is handled by GitHubRepositoryManager")
