"""
Markdown Chunking Strategy

This module provides chunking strategy for Markdown files with header-based
hierarchical chunking and structure preservation.
"""

import logging
import re

import markdown

from ..base import Chunk, ChunkContext, ChunkType, FileMetadata
from .base import BaseChunkingStrategy

logger = logging.getLogger(__name__)


class MarkdownChunkingStrategy(BaseChunkingStrategy):
    """Chunking strategy for Markdown files with header-based structure."""

    def __init__(self, settings):
        """Initialize markdown chunking strategy."""
        super().__init__(settings)

        # Initialize markdown parser
        self.md = markdown.Markdown(
            extensions=["toc", "fenced_code", "tables", "codehilite"],
            extension_configs={
                "toc": {
                    "permalink": False,
                    "baselevel": 1,
                }
            },
        )

    def get_strategy_name(self) -> str:
        """Get the name of this chunking strategy."""
        return "markdown"

    def is_applicable(self, file_metadata: FileMetadata) -> bool:
        """Check if this strategy is applicable to the file."""
        file_extension = file_metadata.file_path.split(".")[-1].lower() if "." in file_metadata.file_path else ""
        return file_extension in {"md", "markdown", "rst"}

    async def chunk_content(self, content: str, file_metadata: FileMetadata, **kwargs) -> list[Chunk]:
        """Chunk markdown content based on headers and structure."""
        try:
            if not content.strip():
                return []

            # Parse markdown structure
            sections = self._parse_markdown_structure(content)

            # Create chunks from sections
            chunks = []
            for i, section in enumerate(sections):
                chunk = self._create_section_chunk(section, file_metadata, i)
                chunks.append(chunk)

                # Handle code blocks within sections as separate chunks
                code_chunks = self._extract_code_blocks(section, file_metadata, i)
                chunks.extend(code_chunks)

            logger.debug(f"Created {len(chunks)} markdown chunks for {file_metadata.file_path}")
            return chunks

        except Exception as e:
            logger.error(f"Failed to chunk markdown content for {file_metadata.file_path}: {e}")
            raise

    def _parse_markdown_structure(self, content: str) -> list[dict]:
        """Parse markdown content into hierarchical sections."""
        lines = content.split("\n")
        sections = []
        current_section = None
        current_content = []

        for line_num, line in enumerate(lines, 1):
            # Check if line is a header
            header_match = re.match(r"^(#{1,6})\s+(.+)$", line.strip())

            if header_match:
                # Save previous section
                if current_section is not None:
                    current_section["content"] = "\n".join(current_content)
                    current_section["end_line"] = line_num - 1
                    sections.append(current_section)

                # Start new section
                header_level = len(header_match.group(1))
                header_text = header_match.group(2).strip()

                current_section = {
                    "level": header_level,
                    "title": header_text,
                    "start_line": line_num,
                    "end_line": line_num,
                    "content": "",
                    "parent_headers": self._get_parent_headers(sections, header_level),
                }
                current_content = [line]
            else:
                # Add line to current section
                if current_section is None:
                    # Content before first header
                    current_section = {
                        "level": 0,
                        "title": "Introduction",
                        "start_line": 1,
                        "end_line": 1,
                        "content": "",
                        "parent_headers": [],
                    }
                    current_content = []

                current_content.append(line)

        # Save final section
        if current_section is not None:
            current_section["content"] = "\n".join(current_content)
            current_section["end_line"] = len(lines)
            sections.append(current_section)

        return sections

    def _get_parent_headers(self, sections: list[dict], current_level: int) -> list[str]:
        """Get parent headers for hierarchical context."""
        parent_headers = []

        # Find parent headers (headers with lower level numbers)
        for section in reversed(sections):
            if section["level"] < current_level:
                parent_headers.insert(0, section["title"])
                current_level = section["level"]

                if current_level == 1:
                    break

        return parent_headers

    def _create_section_chunk(self, section: dict, file_metadata: FileMetadata, section_index: int) -> Chunk:
        """Create a chunk from a markdown section."""
        content = section["content"]

        # Remove code blocks from section content (they'll be separate chunks)
        content_without_code = self._remove_code_blocks(content)

        # Create context with hierarchical information
        context = ChunkContext(
            section_headers=section["parent_headers"] + [section["title"]], namespace=section["title"]
        )

        # Determine chunk type based on section level
        chunk_type = ChunkType.MARKDOWN_SECTION

        return self.create_chunk(
            content=content_without_code.strip(),
            file_metadata=file_metadata,
            start_line=section["start_line"],
            end_line=section["end_line"],
            chunk_type=chunk_type,
            chunk_index=section_index,
            context=context,
        )

    def _extract_code_blocks(self, section: dict, file_metadata: FileMetadata, section_index: int) -> list[Chunk]:
        """Extract code blocks from a section as separate chunks."""
        content = section["content"]
        code_chunks = []

        # Find fenced code blocks
        code_block_pattern = r"```(\w+)?\n(.*?)\n```"
        matches = re.finditer(code_block_pattern, content, re.DOTALL)

        for i, match in enumerate(matches):
            match.group(1) or "text"
            code_content = match.group(2)

            if code_content.strip():
                # Calculate approximate line numbers
                lines_before = content[: match.start()].count("\n")
                start_line = section["start_line"] + lines_before
                end_line = start_line + code_content.count("\n")

                context = ChunkContext(
                    section_headers=section["parent_headers"] + [section["title"]],
                    namespace=f"{section['title']} (code)",
                    parent_chunk_id=f"section_{section_index}",
                )

                chunk = self.create_chunk(
                    content=code_content.strip(),
                    file_metadata=file_metadata,
                    start_line=start_line,
                    end_line=end_line,
                    chunk_type=ChunkType.MARKDOWN_CODE_BLOCK,
                    chunk_index=f"{section_index}_code_{i}",
                    context=context,
                )

                code_chunks.append(chunk)

        return code_chunks

    def _remove_code_blocks(self, content: str) -> str:
        """Remove code blocks from content."""
        # Remove fenced code blocks
        content = re.sub(r"```\w*\n.*?\n```", "[Code Block]", content, flags=re.DOTALL)

        # Remove inline code
        return re.sub(r"`[^`]+`", "[Code]", content)


    def _split_large_section(self, section: dict) -> list[dict]:
        """Split large sections into smaller chunks."""
        content = section["content"]
        token_count = self.count_tokens(content)

        if token_count <= self.chunk_size:
            return [section]

        # Split by paragraphs first
        paragraphs = content.split("\n\n")

        if len(paragraphs) <= 1:
            # No paragraph breaks, split by sentences
            chunks = self.split_by_tokens(content, self.chunk_size)
        else:
            # Group paragraphs into chunks
            chunks = self._group_paragraphs_into_chunks(paragraphs)

        # Create section objects for each chunk
        result_sections = []
        lines_per_chunk = max(1, (section["end_line"] - section["start_line"] + 1) // len(chunks))

        for i, chunk_content in enumerate(chunks):
            start_line = section["start_line"] + (i * lines_per_chunk)
            end_line = min(section["end_line"], start_line + lines_per_chunk - 1)

            chunk_section = {
                "level": section["level"],
                "title": f"{section['title']} (part {i+1})",
                "start_line": start_line,
                "end_line": end_line,
                "content": chunk_content,
                "parent_headers": section["parent_headers"],
            }
            result_sections.append(chunk_section)

        return result_sections

    def _group_paragraphs_into_chunks(self, paragraphs: list[str]) -> list[str]:
        """Group paragraphs into chunks based on token limits."""
        chunks = []
        current_chunk = []
        current_tokens = 0

        for paragraph in paragraphs:
            paragraph_tokens = self.count_tokens(paragraph)

            if current_tokens + paragraph_tokens > self.chunk_size and current_chunk:
                chunks.append("\n\n".join(current_chunk))
                current_chunk = [paragraph]
                current_tokens = paragraph_tokens
            else:
                current_chunk.append(paragraph)
                current_tokens += paragraph_tokens

        if current_chunk:
            chunks.append("\n\n".join(current_chunk))

        return chunks

    def estimate_chunks(self, content: str) -> int:
        """Estimate number of chunks for markdown content."""
        sections = self._parse_markdown_structure(content)

        total_chunks = 0
        for section in sections:
            section_tokens = self.count_tokens(section["content"])
            section_chunks = max(1, (section_tokens + self.chunk_size - 1) // self.chunk_size)
            total_chunks += section_chunks

            # Add estimated code block chunks
            code_blocks = re.findall(r"```\w*\n.*?\n```", section["content"], re.DOTALL)
            total_chunks += len(code_blocks)

        return max(1, total_chunks)
