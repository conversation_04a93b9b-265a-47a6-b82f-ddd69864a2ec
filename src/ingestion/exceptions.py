"""
Custom exceptions for the ingestion module.

This module defines all custom exceptions used throughout the ingestion pipeline,
following the centralized error handling pattern defined in docs/rules.md.
"""

from typing import Any


class IngestionError(Exception):
    """Base exception for all ingestion-related errors."""

    def __init__(self, message: str, details: dict[str, Any] | None = None, cause: Exception | None = None, operation: str | None = None):
        super().__init__(message)
        self.message = message
        self.details = details or {}
        self.cause = cause
        self.operation = operation

    def __str__(self) -> str:
        if self.details:
            return f"{self.message} - Details: {self.details}"
        return self.message


class AuthenticationError(IngestionError):
    """Raised when authentication with external services fails."""

    def __init__(
        self,
        message: str = "Authentication failed",
        service: str | None = None,
        details: dict[str, Any] | None = None,
        cause: Exception | None = None,
    ):
        super().__init__(message, details, cause)
        self.service = service


class RepositoryError(IngestionError):
    """Raised when repository operations fail."""

    def __init__(
        self,
        message: str,
        repository_url: str | None = None,
        operation: str | None = None,
        details: dict[str, Any] | None = None,
        cause: Exception | None = None,
    ):
        super().__init__(message, details, cause)
        self.repository_url = repository_url
        self.operation = operation


class FileProcessingError(IngestionError):
    """Raised when file processing operations fail."""

    def __init__(
        self,
        message: str,
        file_path: str | None = None,
        operation: str | None = None,
        details: dict[str, Any] | None = None,
        cause: Exception | None = None,
    ):
        super().__init__(message, details, cause)
        self.file_path = file_path
        self.operation = operation


class MetadataExtractionError(IngestionError):
    """Raised when metadata extraction fails."""

    def __init__(
        self,
        message: str,
        source: str | None = None,
        metadata_type: str | None = None,
        details: dict[str, Any] | None = None,
        cause: Exception | None = None,
    ):
        super().__init__(message, details, cause)
        self.source = source
        self.metadata_type = metadata_type


class RateLimitError(IngestionError):
    """Raised when API rate limits are exceeded."""

    def __init__(
        self,
        message: str = "Rate limit exceeded",
        service: str | None = None,
        retry_after: int | None = None,
        details: dict[str, Any] | None = None,
        cause: Exception | None = None,
    ):
        super().__init__(message, details, cause)
        self.service = service
        self.retry_after = retry_after


class ConfigurationError(IngestionError):
    """Raised when configuration is invalid or missing."""

    def __init__(
        self,
        message: str,
        config_key: str | None = None,
        details: dict[str, Any] | None = None,
        cause: Exception | None = None,
    ):
        super().__init__(message, details, cause)
        self.config_key = config_key


class EmbeddingError(IngestionError):
    """Base class for embedding-related errors."""

    def __init__(
        self,
        message: str,
        embedding_provider: str | None = None,
        model_name: str | None = None,
        details: dict[str, Any] | None = None,
        cause: Exception | None = None,
    ):
        super().__init__(message, details, cause)
        self.embedding_provider = embedding_provider
        self.model_name = model_name


class EmbeddingGenerationError(EmbeddingError):
    """Raised when embedding generation fails."""

    def __init__(self, message: str, text_content: str | None = None, batch_size: int | None = None, **kwargs) -> None:
        super().__init__(message, **kwargs)
        self.text_content = text_content
        self.batch_size = batch_size


class EmbeddingQualityError(EmbeddingError):
    """Raised when embedding quality validation fails."""

    def __init__(
        self,
        message: str,
        embedding_dimension: int | None = None,
        expected_dimension: int | None = None,
        **kwargs,
    ) -> None:
        super().__init__(message, **kwargs)
        self.embedding_dimension = embedding_dimension
        self.expected_dimension = expected_dimension


class VectorStoreError(IngestionError):
    """Base class for vector store-related errors."""

    def __init__(
        self,
        message: str,
        store_provider: str | None = None,
        collection_name: str | None = None,
        details: dict[str, Any] | None = None,
        cause: Exception | None = None,
    ):
        super().__init__(message, details, cause)
        self.store_provider = store_provider
        self.collection_name = collection_name


class VectorStoreConnectionError(VectorStoreError):
    """Raised when vector store connection fails."""

    def __init__(self, message: str, connection_url: str | None = None, **kwargs) -> None:
        super().__init__(message, **kwargs)
        self.connection_url = connection_url


class VectorStoreOperationError(VectorStoreError):
    """Raised when vector store operations fail."""

    def __init__(
        self, message: str, operation_type: str | None = None, embedding_count: int | None = None, **kwargs
    ) -> None:
        super().__init__(message, **kwargs)
        self.operation_type = operation_type
        self.embedding_count = embedding_count


class EmbeddingPipelineError(IngestionError):
    """Raised when embedding pipeline operations fail."""

    def __init__(
        self,
        message: str,
        pipeline_stage: str | None = None,
        chunk_count: int | None = None,
        details: dict[str, Any] | None = None,
        cause: Exception | None = None,
    ):
        super().__init__(message, details, cause)
        self.pipeline_stage = pipeline_stage
        self.chunk_count = chunk_count
