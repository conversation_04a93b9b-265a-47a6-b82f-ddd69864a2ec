"""
Standards Integration Module

This module provides integration and prioritization of content from the docs/ folder,
ensuring authoritative guidance from rules.md, design.md, and other documentation.
"""

from dataclasses import dataclass, field
from enum import Enum
import logging
from pathlib import Path
import re
from typing import Any

logger = logging.getLogger(__name__)


class DocumentType(Enum):
    """Types of documentation files."""

    RULES = "rules"
    DESIGN = "design"
    REQUIREMENTS = "requirements"
    STRUCTURE = "structure"
    TECH = "tech"
    TASKS = "tasks"
    TESTING = "testing"
    WORKFLOWS = "workflows"
    OTHER = "other"


class StandardsPriority(Enum):
    """Priority levels for standards and guidelines."""

    MANDATORY = "mandatory"  # Must be followed (rules.md)
    RECOMMENDED = "recommended"  # Should be followed (design.md)
    GUIDANCE = "guidance"  # May be followed (other docs)


@dataclass
class StandardsRule:
    """A specific rule or standard from documentation."""

    title: str
    content: str
    source_file: str
    priority: StandardsPriority
    category: str
    section: str = ""
    line_number: int = 0
    tags: list[str] = field(default_factory=list)


@dataclass
class StandardsContext:
    """Context information about project standards."""

    rules: list[StandardsRule] = field(default_factory=list)
    design_principles: list[str] = field(default_factory=list)
    technology_stack: dict[str, str] = field(default_factory=dict)
    coding_standards: dict[str, Any] = field(default_factory=dict)
    testing_requirements: dict[str, Any] = field(default_factory=dict)
    architectural_constraints: list[str] = field(default_factory=list)


class ProjectStandardsManager:
    """Manages and provides access to project standards and guidelines."""

    def __init__(self, docs_path: str = "docs"):
        """Initialize the standards manager."""
        self.docs_path = Path(docs_path)
        self.document_priorities = {
            "rules.md": StandardsPriority.MANDATORY,
            "design.md": StandardsPriority.RECOMMENDED,
            "requirements.md": StandardsPriority.RECOMMENDED,
            "structure.md": StandardsPriority.GUIDANCE,
            "tech.md": StandardsPriority.GUIDANCE,
            "tasks.md": StandardsPriority.GUIDANCE,
            "testing.md": StandardsPriority.RECOMMENDED,
            "workflows.md": StandardsPriority.GUIDANCE,
        }

        self._standards_cache: StandardsContext | None = None
        logger.info(f"Initialized standards manager for {docs_path}")

    def get_standards_context(self, force_refresh: bool = False) -> StandardsContext:
        """Get comprehensive standards context."""
        if self._standards_cache is None or force_refresh:
            self._standards_cache = self._load_standards_context()

        return self._standards_cache

    def get_rules_for_category(self, category: str) -> list[StandardsRule]:
        """Get rules for a specific category."""
        context = self.get_standards_context()
        return [rule for rule in context.rules if rule.category.lower() == category.lower()]

    def get_mandatory_rules(self) -> list[StandardsRule]:
        """Get all mandatory rules."""
        context = self.get_standards_context()
        return [rule for rule in context.rules if rule.priority == StandardsPriority.MANDATORY]

    def validate_against_standards(self, design_content: str, category: str = "") -> dict[str, Any]:
        """Validate design content against project standards."""
        context = self.get_standards_context()
        validation_results = {
            "compliant": True,
            "violations": [],
            "recommendations": [],
            "score": 1.0,
        }

        # Get relevant rules
        relevant_rules = context.rules
        if category:
            relevant_rules = self.get_rules_for_category(category)

        # Check compliance with each rule
        violations = []
        for rule in relevant_rules:
            if not self._check_rule_compliance(design_content, rule):
                violations.append({
                    "rule": rule.title,
                    "priority": rule.priority.value,
                    "source": rule.source_file,
                    "content": rule.content[:200] + "..." if len(rule.content) > 200 else rule.content,
                })

        validation_results["violations"] = violations
        validation_results["compliant"] = len(violations) == 0

        # Calculate compliance score
        if relevant_rules:
            violation_weight = sum(
                3 if v["priority"] == "mandatory" else 2 if v["priority"] == "recommended" else 1
                for v in violations
            )
            total_weight = sum(
                3 if rule.priority == StandardsPriority.MANDATORY
                else 2 if rule.priority == StandardsPriority.RECOMMENDED
                else 1
                for rule in relevant_rules
            )
            validation_results["score"] = max(0, 1 - (violation_weight / total_weight))

        return validation_results

    def _load_standards_context(self) -> StandardsContext:
        """Load standards context from documentation files."""
        logger.info("Loading standards context from documentation")

        context = StandardsContext()

        # Load rules from each documentation file
        for doc_file in self.docs_path.glob("*.md"):
            if doc_file.name in self.document_priorities:
                rules = self._parse_document(doc_file)
                context.rules.extend(rules)

        # Extract specific information
        context.design_principles = self._extract_design_principles(context.rules)
        context.technology_stack = self._extract_technology_stack(context.rules)
        context.coding_standards = self._extract_coding_standards(context.rules)
        context.testing_requirements = self._extract_testing_requirements(context.rules)
        context.architectural_constraints = self._extract_architectural_constraints(context.rules)

        logger.info(f"Loaded {len(context.rules)} standards rules")
        return context

    def _parse_document(self, doc_path: Path) -> list[StandardsRule]:
        """Parse a documentation file and extract rules."""
        try:
            with open(doc_path, encoding="utf-8") as f:
                content = f.read()

            rules = []
            priority = self.document_priorities.get(doc_path.name, StandardsPriority.GUIDANCE)

            # Split content into sections
            sections = self._split_into_sections(content)

            for section_title, section_content in sections.items():
                # Extract rules from this section
                section_rules = self._extract_rules_from_section(
                    section_title, section_content, doc_path.name, priority
                )
                rules.extend(section_rules)

            return rules

        except Exception as e:
            logger.warning(f"Failed to parse document {doc_path}: {e}")
            return []

    def _split_into_sections(self, content: str) -> dict[str, str]:
        """Split document content into sections based on headers."""
        sections = {}
        current_section = "Introduction"
        current_content = []

        lines = content.split("\n")
        for line in lines:
            # Check for markdown headers
            if line.startswith("#"):
                # Save previous section
                if current_content:
                    sections[current_section] = "\n".join(current_content)

                # Start new section
                current_section = line.strip("# ").strip()
                current_content = []
            else:
                current_content.append(line)

        # Save last section
        if current_content:
            sections[current_section] = "\n".join(current_content)

        return sections

    def _extract_rules_from_section(
        self, section_title: str, section_content: str, source_file: str, priority: StandardsPriority
    ) -> list[StandardsRule]:
        """Extract rules from a documentation section."""
        rules = []

        # Look for explicit rules (bullet points, numbered lists)
        rule_patterns = [
            r"^[-*+]\s+(.+)$",  # Bullet points
            r"^\d+\.\s+(.+)$",  # Numbered lists
            r"^>\s+(.+)$",  # Blockquotes
        ]

        lines = section_content.split("\n")
        for i, original_line in enumerate(lines):
            line = original_line.strip()
            if not line:
                continue

            for pattern in rule_patterns:
                match = re.match(pattern, line)
                if match:
                    rule_text = match.group(1).strip()
                    if len(rule_text) > 10:  # Filter out very short rules
                        rule = StandardsRule(
                            title=rule_text[:100] + "..." if len(rule_text) > 100 else rule_text,
                            content=rule_text,
                            source_file=source_file,
                            priority=priority,
                            category=self._categorize_rule(section_title, rule_text),
                            section=section_title,
                            line_number=i + 1,
                            tags=self._extract_tags(rule_text),
                        )
                        rules.append(rule)

        # Look for imperative statements (must, should, shall)
        imperative_pattern = r"(must|should|shall|require[sd]?|mandate[sd]?)\s+(.+?)(?:\.|$)"
        matches = re.finditer(imperative_pattern, section_content, re.IGNORECASE | re.MULTILINE)

        for match in matches:
            imperative = match.group(1).lower()
            rule_text = match.group(2).strip()

            if len(rule_text) > 10:
                # Adjust priority based on imperative
                rule_priority = priority
                if imperative in ["must", "shall", "required", "mandate"]:
                    rule_priority = StandardsPriority.MANDATORY
                elif imperative in ["should"]:
                    rule_priority = StandardsPriority.RECOMMENDED

                rule = StandardsRule(
                    title=f"{imperative.title()} {rule_text[:80]}...",
                    content=f"{imperative} {rule_text}",
                    source_file=source_file,
                    priority=rule_priority,
                    category=self._categorize_rule(section_title, rule_text),
                    section=section_title,
                    tags=self._extract_tags(rule_text),
                )
                rules.append(rule)

        return rules

    def _categorize_rule(self, section_title: str, rule_text: str) -> str:
        """Categorize a rule based on its content and section."""
        section_lower = section_title.lower()
        text_lower = rule_text.lower()

        # Category mapping based on keywords
        categories = {
            "code_quality": ["code", "quality", "style", "format", "lint", "pep", "naming"],
            "testing": ["test", "coverage", "unit", "integration", "pytest"],
            "architecture": ["architecture", "design", "pattern", "solid", "dependency"],
            "documentation": ["document", "docstring", "comment", "readme"],
            "workflow": ["workflow", "process", "review", "commit", "branch"],
            "performance": ["performance", "speed", "optimization", "memory"],
            "security": ["security", "auth", "permission", "encrypt"],
            "deployment": ["deploy", "container", "docker", "ci", "cd"],
        }

        for category, keywords in categories.items():
            if any(keyword in section_lower or keyword in text_lower for keyword in keywords):
                return category

        return "general"

    def _extract_tags(self, rule_text: str) -> list[str]:
        """Extract tags from rule text."""
        tags = []
        text_lower = rule_text.lower()

        # Common tags
        tag_keywords = {
            "mandatory": ["must", "shall", "required", "mandatory"],
            "testing": ["test", "coverage", "pytest"],
            "solid": ["solid", "single responsibility", "open closed", "liskov", "interface segregation", "dependency inversion"],
            "patterns": ["pattern", "factory", "singleton", "strategy"],
            "quality": ["quality", "maintainability", "readability"],
        }

        for tag, keywords in tag_keywords.items():
            if any(keyword in text_lower for keyword in keywords):
                tags.append(tag)

        return tags

    def _extract_design_principles(self, rules: list[StandardsRule]) -> list[str]:
        """Extract design principles from rules."""
        principles = []

        for rule in rules:
            if "principle" in rule.content.lower() or "solid" in rule.tags:
                principles.append(rule.content)

        # Add common principles if not found
        common_principles = [
            "Follow SOLID principles for maintainable code",
            "Use dependency injection for testability",
            "Implement proper error handling and logging",
            "Write comprehensive unit and integration tests",
            "Document all public APIs and architectural decisions",
        ]

        for principle in common_principles:
            if not any(principle.lower() in p.lower() for p in principles):
                principles.append(principle)

        return principles

    def _extract_technology_stack(self, rules: list[StandardsRule]) -> dict[str, str]:
        """Extract technology stack information from rules."""
        tech_stack = {}

        for rule in rules:
            if rule.source_file == "tech.md":
                # Extract technology mentions
                content = rule.content.lower()

                # Common technology patterns
                if "python" in content:
                    tech_stack["language"] = "Python"
                if "fastapi" in content or "flask" in content:
                    tech_stack["web_framework"] = "FastAPI" if "fastapi" in content else "Flask"
                if "postgresql" in content or "postgres" in content:
                    tech_stack["database"] = "PostgreSQL"
                if "redis" in content:
                    tech_stack["cache"] = "Redis"
                if "docker" in content:
                    tech_stack["containerization"] = "Docker"
                if "openai" in content:
                    tech_stack["llm_provider"] = "OpenAI"
                if "chromadb" in content:
                    tech_stack["vector_store"] = "ChromaDB"

        return tech_stack

    def _extract_coding_standards(self, rules: list[StandardsRule]) -> dict[str, Any]:
        """Extract coding standards from rules."""
        standards = {
            "style_guide": "PEP 8",
            "line_length": 88,
            "formatter": "Black",
            "linter": "Ruff",
            "docstring_style": "Google",
            "naming_conventions": {
                "variables": "snake_case",
                "functions": "snake_case",
                "classes": "PascalCase",
                "constants": "UPPER_CASE",
            },
        }

        for rule in rules:
            content = rule.content.lower()

            # Extract specific standards
            if "pep 8" in content:
                standards["style_guide"] = "PEP 8"
            if "black" in content:
                standards["formatter"] = "Black"
            if "ruff" in content:
                standards["linter"] = "Ruff"
            if "google" in content and "docstring" in content:
                standards["docstring_style"] = "Google"

            # Extract line length
            line_length_match = re.search(r"(\d+)\s*characters?", content)
            if line_length_match:
                standards["line_length"] = int(line_length_match.group(1))

        return standards

    def _extract_testing_requirements(self, rules: list[StandardsRule]) -> dict[str, Any]:
        """Extract testing requirements from rules."""
        requirements = {
            "framework": "pytest",
            "min_coverage": 80,
            "test_types": ["unit", "integration"],
            "test_isolation": True,
            "mocking_required": True,
        }

        for rule in rules:
            content = rule.content.lower()

            if "pytest" in content:
                requirements["framework"] = "pytest"
            if "coverage" in content:
                # Extract coverage percentage
                coverage_match = re.search(r"(\d+)%", content)
                if coverage_match:
                    requirements["min_coverage"] = int(coverage_match.group(1))
            if "unit test" in content and "unit" not in requirements["test_types"]:
                requirements["test_types"].append("unit")
            if "integration test" in content and "integration" not in requirements["test_types"]:
                requirements["test_types"].append("integration")

        return requirements

    def _extract_architectural_constraints(self, rules: list[StandardsRule]) -> list[str]:
        """Extract architectural constraints from rules."""
        constraints = []

        for rule in rules:
            if rule.category in ["architecture", "design"]:
                constraints.append(rule.content)

        # Add common constraints
        common_constraints = [
            "Maintain separation between client and server components",
            "Use containerized environments for consistency",
            "Configure via environment variables and .env files",
            "Implement proper error handling with custom exceptions",
            "Follow the 5-phase development methodology",
        ]

        for constraint in common_constraints:
            if not any(constraint.lower() in c.lower() for c in constraints):
                constraints.append(constraint)

        return constraints

    def _check_rule_compliance(self, design_content: str, rule: StandardsRule) -> bool:
        """Check if design content complies with a specific rule."""
        content_lower = design_content.lower()
        rule_lower = rule.content.lower()

        # Simple keyword-based compliance checking
        # This is a basic implementation - could be enhanced with more sophisticated analysis

        # Check for positive indicators
        positive_keywords = []
        negative_keywords = []

        if "test" in rule_lower:
            positive_keywords.extend(["test", "testing", "pytest", "coverage"])
            negative_keywords.extend(["no test", "skip test", "without test"])

        if "solid" in rule_lower or any(principle in rule_lower for principle in ["single responsibility", "open closed", "liskov", "interface segregation", "dependency inversion"]):
            positive_keywords.extend(["solid", "principle", "dependency injection", "interface", "abstract"])

        if "document" in rule_lower:
            positive_keywords.extend(["document", "docstring", "comment", "readme"])
            negative_keywords.extend(["no document", "undocumented"])

        if "error" in rule_lower and "handling" in rule_lower:
            positive_keywords.extend(["try", "except", "error", "exception", "handling"])
            negative_keywords.extend(["no error handling", "ignore error"])

        # Check compliance
        if positive_keywords:
            has_positive = any(keyword in content_lower for keyword in positive_keywords)
            has_negative = any(keyword in content_lower for keyword in negative_keywords)

            # If we have negative indicators, it's non-compliant
            if has_negative:
                return False

            # Return true if we have positive indicators, false otherwise
            return has_positive

        # Default to compliant if we can't determine
        return True

    def get_standards_summary(self) -> dict[str, Any]:
        """Get a summary of all project standards."""
        context = self.get_standards_context()

        return {
            "total_rules": len(context.rules),
            "mandatory_rules": len([r for r in context.rules if r.priority == StandardsPriority.MANDATORY]),
            "recommended_rules": len([r for r in context.rules if r.priority == StandardsPriority.RECOMMENDED]),
            "guidance_rules": len([r for r in context.rules if r.priority == StandardsPriority.GUIDANCE]),
            "categories": list({rule.category for rule in context.rules}),
            "design_principles": context.design_principles,
            "technology_stack": context.technology_stack,
            "coding_standards": context.coding_standards,
            "testing_requirements": context.testing_requirements,
            "architectural_constraints": context.architectural_constraints,
        }
