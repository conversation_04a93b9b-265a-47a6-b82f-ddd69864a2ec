"""
Design Generation Module

This module provides capabilities for generating technical designs aligned with project standards,
incorporating docs/ content and ensuring consistency with existing patterns.
"""

from dataclasses import dataclass, field
from enum import Enum
import logging
from typing import Any

from .analyzer import ArchitectureAnalysis, ArchitecturePattern

logger = logging.getLogger(__name__)


class DesignType(Enum):
    """Types of design documents that can be generated."""

    ARCHITECTURE_OVERVIEW = "architecture_overview"
    COMPONENT_DESIGN = "component_design"
    API_DESIGN = "api_design"
    DATABASE_DESIGN = "database_design"
    INTEGRATION_DESIGN = "integration_design"
    REFACTORING_PLAN = "refactoring_plan"


class DesignPriority(Enum):
    """Priority levels for design recommendations."""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"


@dataclass
class DesignRecommendation:
    """A specific design recommendation."""

    title: str
    description: str
    priority: DesignPriority
    rationale: str
    implementation_steps: list[str] = field(default_factory=list)
    affected_components: list[str] = field(default_factory=list)
    patterns_to_apply: list[ArchitecturePattern] = field(default_factory=list)
    estimated_effort: str = ""
    risks: list[str] = field(default_factory=list)
    benefits: list[str] = field(default_factory=list)


@dataclass
class DesignDocument:
    """Generated design document."""

    design_type: DesignType
    title: str
    overview: str
    recommendations: list[DesignRecommendation] = field(default_factory=list)
    architecture_diagrams: list[str] = field(default_factory=list)
    implementation_timeline: dict[str, Any] = field(default_factory=dict)
    compliance_notes: list[str] = field(default_factory=list)
    references: list[str] = field(default_factory=list)
    confidence: float = 0.0


class TechnicalDesigner:
    """Generates technical designs aligned with project standards."""

    def __init__(self):
        """Initialize the technical designer."""
        self.design_templates = {
            DesignType.ARCHITECTURE_OVERVIEW: self._generate_architecture_overview,
            DesignType.COMPONENT_DESIGN: self._generate_component_design,
            DesignType.API_DESIGN: self._generate_api_design,
            DesignType.REFACTORING_PLAN: self._generate_refactoring_plan,
        }

        # Project-specific patterns and standards
        self.preferred_patterns = [
            ArchitecturePattern.DEPENDENCY_INJECTION,
            ArchitecturePattern.FACTORY,
            ArchitecturePattern.STRATEGY,
            ArchitecturePattern.RAG_PIPELINE,
        ]

        logger.info("Initialized technical designer")

    def generate_design(
        self,
        design_type: DesignType,
        requirements: list[str],
        current_analysis: ArchitectureAnalysis,
        project_standards: dict[str, Any],
    ) -> DesignDocument:
        """Generate a design document based on requirements and current state."""
        logger.info(f"Generating {design_type.value} design")

        if design_type not in self.design_templates:
            raise ValueError(f"Unsupported design type: {design_type}")

        # Generate the design using the appropriate template
        design = self.design_templates[design_type](
            requirements, current_analysis, project_standards
        )

        # Add compliance notes
        design.compliance_notes = self._generate_compliance_notes(design, project_standards)

        # Calculate confidence
        design.confidence = self._calculate_design_confidence(design, current_analysis)

        logger.info(f"Generated design with {len(design.recommendations)} recommendations")
        return design

    def _generate_architecture_overview(
        self,
        requirements: list[str],
        current_analysis: ArchitectureAnalysis,
        project_standards: dict[str, Any],
    ) -> DesignDocument:
        """Generate an architecture overview design."""
        design = DesignDocument(
            design_type=DesignType.ARCHITECTURE_OVERVIEW,
            title="System Architecture Overview",
            overview="Comprehensive architecture design for the LLM RAG Codebase Query System",
        )

        # Analyze current state and generate recommendations
        recommendations = []

        # Check for missing critical patterns
        missing_patterns = [p for p in self.preferred_patterns if p not in current_analysis.patterns_detected]
        for pattern in missing_patterns:
            rec = self._create_pattern_recommendation(pattern, requirements)
            if rec:
                recommendations.append(rec)

        # Address SOLID compliance issues
        for principle, score in current_analysis.solid_compliance.items():
            if score < 0.7:
                rec = self._create_solid_improvement_recommendation(principle, score)
                recommendations.append(rec)

        # Add layered architecture recommendation
        if "layered" not in [r.title.lower() for r in recommendations]:
            recommendations.append(self._create_layered_architecture_recommendation())

        design.recommendations = recommendations

        # Generate architecture diagrams
        design.architecture_diagrams = self._generate_architecture_diagrams(current_analysis)

        return design

    def _generate_component_design(
        self,
        requirements: list[str],
        current_analysis: ArchitectureAnalysis,
        project_standards: dict[str, Any],
    ) -> DesignDocument:
        """Generate a component-specific design."""
        design = DesignDocument(
            design_type=DesignType.COMPONENT_DESIGN,
            title="Component Design Specification",
            overview="Detailed design for specific system components",
        )

        recommendations = []

        # Analyze requirements for component needs
        for requirement in requirements:
            if "agent" in requirement.lower():
                rec = self._create_agent_component_recommendation(requirement)
                recommendations.append(rec)
            elif "pipeline" in requirement.lower():
                rec = self._create_pipeline_component_recommendation(requirement)
                recommendations.append(rec)
            elif "api" in requirement.lower():
                rec = self._create_api_component_recommendation(requirement)
                recommendations.append(rec)

        design.recommendations = recommendations
        return design

    def _generate_api_design(
        self,
        requirements: list[str],
        current_analysis: ArchitectureAnalysis,
        project_standards: dict[str, Any],
    ) -> DesignDocument:
        """Generate an API design."""
        design = DesignDocument(
            design_type=DesignType.API_DESIGN,
            title="API Design Specification",
            overview="RESTful API design for the codebase query system",
        )

        # Standard API recommendations
        recommendations = [
            DesignRecommendation(
                title="RESTful Endpoint Design",
                description="Design REST endpoints following OpenAPI 3.0 specification",
                priority=DesignPriority.HIGH,
                rationale="Ensures consistent, discoverable API interface",
                implementation_steps=[
                    "Define OpenAPI 3.0 specification",
                    "Implement endpoint handlers with proper HTTP methods",
                    "Add request/response validation",
                    "Include comprehensive error handling",
                ],
                patterns_to_apply=[ArchitecturePattern.FACTORY],
                estimated_effort="2-3 days",
            ),
            DesignRecommendation(
                title="Query Processing Pipeline",
                description="Implement async query processing with proper error handling",
                priority=DesignPriority.CRITICAL,
                rationale="Core functionality for the system",
                implementation_steps=[
                    "Create async query handler",
                    "Implement agent routing logic",
                    "Add response formatting",
                    "Include timeout and error handling",
                ],
                patterns_to_apply=[ArchitecturePattern.STRATEGY, ArchitecturePattern.RAG_PIPELINE],
                estimated_effort="3-5 days",
            ),
        ]

        design.recommendations = recommendations
        return design

    def _generate_refactoring_plan(
        self,
        requirements: list[str],
        current_analysis: ArchitectureAnalysis,
        project_standards: dict[str, Any],
    ) -> DesignDocument:
        """Generate a refactoring plan based on current analysis."""
        design = DesignDocument(
            design_type=DesignType.REFACTORING_PLAN,
            title="Code Refactoring Plan",
            overview="Systematic plan to improve code quality and architecture",
        )

        recommendations = []

        # Address high complexity components
        if current_analysis.code_quality_metrics.get("max_complexity", 0) > 7:
            recommendations.append(
                DesignRecommendation(
                    title="Reduce Component Complexity",
                    description="Break down high-complexity components into smaller, focused units",
                    priority=DesignPriority.HIGH,
                    rationale="High complexity reduces maintainability and increases bug risk",
                    implementation_steps=[
                        "Identify components with complexity > 7",
                        "Apply Single Responsibility Principle",
                        "Extract methods and classes",
                        "Add comprehensive unit tests",
                    ],
                    estimated_effort="1-2 weeks",
                )
            )

        # Address SOLID violations
        violation_count = current_analysis.code_quality_metrics.get("components_with_violations", 0)
        if violation_count > 0:
            recommendations.append(
                DesignRecommendation(
                    title="Fix SOLID Principle Violations",
                    description="Address identified SOLID principle violations",
                    priority=DesignPriority.MEDIUM,
                    rationale="SOLID compliance improves code maintainability and testability",
                    implementation_steps=[
                        "Review violation reports",
                        "Refactor violating components",
                        "Add dependency injection where needed",
                        "Validate with automated checks",
                    ],
                    estimated_effort="1 week",
                )
            )

        design.recommendations = recommendations
        return design

    def _create_pattern_recommendation(
        self, pattern: ArchitecturePattern, requirements: list[str]
    ) -> DesignRecommendation | None:
        """Create a recommendation for implementing a specific pattern."""
        pattern_configs = {
            ArchitecturePattern.DEPENDENCY_INJECTION: {
                "title": "Implement Dependency Injection",
                "description": "Add dependency injection to improve testability and modularity",
                "priority": DesignPriority.HIGH,
                "rationale": "DI enables better unit testing and loose coupling",
                "steps": [
                    "Create dependency injection container",
                    "Refactor constructors to accept dependencies",
                    "Add factory classes for complex object creation",
                    "Update tests to use dependency injection",
                ],
                "effort": "3-5 days",
            },
            ArchitecturePattern.FACTORY: {
                "title": "Implement Factory Pattern",
                "description": "Add factory classes for consistent object creation",
                "priority": DesignPriority.MEDIUM,
                "rationale": "Factories centralize object creation logic and improve maintainability",
                "steps": [
                    "Identify object creation points",
                    "Create factory classes",
                    "Replace direct instantiation with factory calls",
                    "Add configuration support to factories",
                ],
                "effort": "2-3 days",
            },
            ArchitecturePattern.STRATEGY: {
                "title": "Implement Strategy Pattern",
                "description": "Use strategy pattern for algorithm selection and extensibility",
                "priority": DesignPriority.MEDIUM,
                "rationale": "Strategy pattern enables runtime algorithm selection and easy extension",
                "steps": [
                    "Define strategy interfaces",
                    "Implement concrete strategies",
                    "Create strategy context classes",
                    "Add strategy selection logic",
                ],
                "effort": "2-4 days",
            },
        }

        config = pattern_configs.get(pattern)
        if not config:
            return None

        return DesignRecommendation(
            title=config["title"],
            description=config["description"],
            priority=config["priority"],
            rationale=config["rationale"],
            implementation_steps=config["steps"],
            patterns_to_apply=[pattern],
            estimated_effort=config["effort"],
        )

    def _create_solid_improvement_recommendation(
        self, principle: Any, score: float
    ) -> DesignRecommendation:
        """Create a recommendation for improving SOLID compliance."""
        principle_name = principle.value.replace("_", " ").title()

        return DesignRecommendation(
            title=f"Improve {principle_name} Compliance",
            description=f"Address {principle_name} violations to improve code quality",
            priority=DesignPriority.MEDIUM if score > 0.5 else DesignPriority.HIGH,
            rationale=f"Current compliance score: {score:.1%}. Improving this will enhance maintainability",
            implementation_steps=[
                f"Review components violating {principle_name}",
                "Refactor to align with principle guidelines",
                "Add unit tests to verify improvements",
                "Document architectural decisions",
            ],
            estimated_effort="1-2 weeks",
        )

    def _create_layered_architecture_recommendation(self) -> DesignRecommendation:
        """Create a recommendation for layered architecture."""
        return DesignRecommendation(
            title="Implement Layered Architecture",
            description="Organize code into distinct layers (presentation, business, data)",
            priority=DesignPriority.HIGH,
            rationale="Layered architecture improves separation of concerns and maintainability",
            implementation_steps=[
                "Define layer boundaries and responsibilities",
                "Organize modules into appropriate layers",
                "Implement layer-specific interfaces",
                "Add layer dependency validation",
            ],
            patterns_to_apply=[ArchitecturePattern.LAYERED],
            estimated_effort="1-2 weeks",
            benefits=[
                "Clear separation of concerns",
                "Improved testability",
                "Better code organization",
                "Easier maintenance and updates",
            ],
        )

    def _create_agent_component_recommendation(self, requirement: str) -> DesignRecommendation:
        """Create a recommendation for agent component design."""
        return DesignRecommendation(
            title="Agent Component Design",
            description="Design agent components following established patterns",
            priority=DesignPriority.HIGH,
            rationale="Consistent agent design ensures maintainability and extensibility",
            implementation_steps=[
                "Inherit from base Agent class",
                "Implement required abstract methods",
                "Add agent-specific configuration",
                "Include comprehensive error handling",
                "Add unit and integration tests",
            ],
            patterns_to_apply=[
                ArchitecturePattern.STRATEGY,
                ArchitecturePattern.DEPENDENCY_INJECTION,
            ],
            estimated_effort="3-5 days",
        )

    def _create_pipeline_component_recommendation(self, requirement: str) -> DesignRecommendation:
        """Create a recommendation for pipeline component design."""
        return DesignRecommendation(
            title="Pipeline Component Design",
            description="Design pipeline components for data processing workflows",
            priority=DesignPriority.HIGH,
            rationale="Consistent pipeline design enables efficient data processing",
            implementation_steps=[
                "Define pipeline stage interfaces",
                "Implement async processing capabilities",
                "Add error handling and retry logic",
                "Include monitoring and logging",
                "Add comprehensive testing",
            ],
            patterns_to_apply=[
                ArchitecturePattern.RAG_PIPELINE,
                ArchitecturePattern.STRATEGY,
            ],
            estimated_effort="4-6 days",
        )

    def _create_api_component_recommendation(self, requirement: str) -> DesignRecommendation:
        """Create a recommendation for API component design."""
        return DesignRecommendation(
            title="API Component Design",
            description="Design API components following REST principles",
            priority=DesignPriority.HIGH,
            rationale="Well-designed APIs provide clear interfaces for system interaction",
            implementation_steps=[
                "Define OpenAPI specification",
                "Implement request/response models",
                "Add input validation and sanitization",
                "Include comprehensive error handling",
                "Add API documentation and examples",
            ],
            patterns_to_apply=[ArchitecturePattern.FACTORY],
            estimated_effort="3-4 days",
        )

    def _generate_architecture_diagrams(self, current_analysis: ArchitectureAnalysis) -> list[str]:
        """Generate ASCII architecture diagrams."""
        diagrams = []

        # System overview diagram
        system_diagram = """
# System Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Client API    │    │   Orchestrator  │    │  Agent Factory  │
│                 │◄──►│     Agent       │◄──►│                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                    ┌─────────────────────────┐
                    │    Specialized Agents   │
                    │                         │
                    │ ┌─────────────────────┐ │
                    │ │ Technical Architect │ │
                    │ └─────────────────────┘ │
                    │ ┌─────────────────────┐ │
                    │ │   Task Planner      │ │
                    │ └─────────────────────┘ │
                    │ ┌─────────────────────┐ │
                    │ │   RAG Retrieval     │ │
                    │ └─────────────────────┘ │
                    └─────────────────────────┘
                                │
                                ▼
                    ┌─────────────────────────┐
                    │   RAG Pipeline          │
                    │                         │
                    │ ┌─────┐ ┌─────┐ ┌─────┐ │
                    │ │Ingest│►│Embed│►│Store│ │
                    │ └─────┘ └─────┘ └─────┘ │
                    └─────────────────────────┘
```
"""
        diagrams.append(system_diagram)

        # Agent interaction diagram
        agent_diagram = """
# Agent Interaction Flow

```
User Query
    │
    ▼
┌─────────────────┐
│  Orchestrator   │
│   Classifier    │
└─────────────────┘
    │
    ▼
┌─────────────────┐    ┌─────────────────┐
│  Agent Router   │───►│ Selected Agent  │
└─────────────────┘    └─────────────────┘
    │                           │
    ▼                           ▼
┌─────────────────┐    ┌─────────────────┐
│ Response        │◄───│ Agent Response  │
│ Synthesizer     │    │                 │
└─────────────────┘    └─────────────────┘
    │
    ▼
Formatted Response
```
"""
        diagrams.append(agent_diagram)

        return diagrams

    def _generate_compliance_notes(
        self, design: DesignDocument, project_standards: dict[str, Any]
    ) -> list[str]:
        """Generate compliance notes for the design."""
        notes = []

        # Check for SOLID compliance
        notes.append("Design follows SOLID principles for maintainability")

        # Check for pattern usage
        patterns_used = set()
        for rec in design.recommendations:
            patterns_used.update(rec.patterns_to_apply)

        if ArchitecturePattern.DEPENDENCY_INJECTION in patterns_used:
            notes.append("Dependency injection improves testability")

        if ArchitecturePattern.FACTORY in patterns_used:
            notes.append("Factory pattern centralizes object creation")

        # Check for testing considerations
        notes.append("All components must include comprehensive unit tests")
        notes.append("Integration tests required for multi-component interactions")

        # Check for documentation requirements
        notes.append("All public APIs must include comprehensive documentation")
        notes.append("Architecture decisions must be documented as ADRs")

        return notes

    def _calculate_design_confidence(
        self, design: DesignDocument, current_analysis: ArchitectureAnalysis
    ) -> float:
        """Calculate confidence in the design recommendations."""
        # Base confidence on analysis quality
        analysis_confidence = current_analysis.confidence

        # Adjust based on number of recommendations
        rec_confidence = min(len(design.recommendations) / 5, 1.0)

        # Adjust based on recommendation priorities
        high_priority_count = len([r for r in design.recommendations if r.priority == DesignPriority.HIGH])
        priority_confidence = 1.0 - (high_priority_count / max(len(design.recommendations), 1)) * 0.2

        return (analysis_confidence + rec_confidence + priority_confidence) / 3
