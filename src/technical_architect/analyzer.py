"""
Architecture Analysis Module

This module provides capabilities for analyzing existing codebase architecture,
identifying patterns, and assessing compliance with SOLID principles and project standards.
"""

from dataclasses import dataclass, field
from enum import Enum
import logging
import re
from typing import Any

from ..ingestion.base import SearchResult

logger = logging.getLogger(__name__)


class ArchitecturePattern(Enum):
    """Enumeration of recognized architecture patterns."""

    FACTORY = "factory"
    SINGLETON = "singleton"
    OBSERVER = "observer"
    STRATEGY = "strategy"
    DEPENDENCY_INJECTION = "dependency_injection"
    REPOSITORY = "repository"
    MVC = "mvc"
    LAYERED = "layered"
    MICROSERVICES = "microservices"
    RAG_PIPELINE = "rag_pipeline"


class SOLIDPrinciple(Enum):
    """SOLID principles enumeration."""

    SINGLE_RESPONSIBILITY = "single_responsibility"
    OPEN_CLOSED = "open_closed"
    LISKOV_SUBSTITUTION = "liskov_substitution"
    INTERFACE_SEGREGATION = "interface_segregation"
    DEPENDENCY_INVERSION = "dependency_inversion"


@dataclass
class ArchitectureAnalysis:
    """Results of architecture analysis."""

    patterns_detected: list[ArchitecturePattern] = field(default_factory=list)
    solid_compliance: dict[SOLIDPrinciple, float] = field(default_factory=dict)
    code_quality_metrics: dict[str, Any] = field(default_factory=dict)
    recommendations: list[str] = field(default_factory=list)
    concerns: list[str] = field(default_factory=list)
    strengths: list[str] = field(default_factory=list)
    confidence: float = 0.0


@dataclass
class ComponentAnalysis:
    """Analysis of a specific component or module."""

    component_name: str
    file_path: str
    component_type: str  # class, function, module
    responsibilities: list[str] = field(default_factory=list)
    dependencies: list[str] = field(default_factory=list)
    patterns_used: list[ArchitecturePattern] = field(default_factory=list)
    solid_violations: list[str] = field(default_factory=list)
    complexity_score: float = 0.0


class ArchitectureAnalyzer:
    """Analyzes codebase architecture and patterns."""

    def __init__(self):
        """Initialize the architecture analyzer."""
        self.pattern_detectors = {
            ArchitecturePattern.FACTORY: self._detect_factory_pattern,
            ArchitecturePattern.SINGLETON: self._detect_singleton_pattern,
            ArchitecturePattern.STRATEGY: self._detect_strategy_pattern,
            ArchitecturePattern.DEPENDENCY_INJECTION: self._detect_dependency_injection,
            ArchitecturePattern.REPOSITORY: self._detect_repository_pattern,
            ArchitecturePattern.RAG_PIPELINE: self._detect_rag_pipeline_pattern,
        }

        self.solid_analyzers = {
            SOLIDPrinciple.SINGLE_RESPONSIBILITY: self._analyze_single_responsibility,
            SOLIDPrinciple.OPEN_CLOSED: self._analyze_open_closed,
            SOLIDPrinciple.LISKOV_SUBSTITUTION: self._analyze_liskov_substitution,
            SOLIDPrinciple.INTERFACE_SEGREGATION: self._analyze_interface_segregation,
            SOLIDPrinciple.DEPENDENCY_INVERSION: self._analyze_dependency_inversion,
        }

        logger.info("Initialized architecture analyzer")

    def analyze_codebase(self, search_results: list[SearchResult]) -> ArchitectureAnalysis:
        """Analyze the overall codebase architecture."""
        logger.info(f"Analyzing codebase architecture from {len(search_results)} components")

        analysis = ArchitectureAnalysis()

        # Analyze individual components
        components = []
        for result in search_results:
            component = self._analyze_component(result)
            if component:
                components.append(component)

        # Detect patterns across components
        analysis.patterns_detected = self._detect_patterns(components)

        # Analyze SOLID compliance
        analysis.solid_compliance = self._analyze_solid_compliance(components)

        # Calculate quality metrics
        analysis.code_quality_metrics = self._calculate_quality_metrics(components)

        # Generate recommendations
        analysis.recommendations = self._generate_recommendations(analysis, components)

        # Identify concerns and strengths
        analysis.concerns = self._identify_concerns(analysis, components)
        analysis.strengths = self._identify_strengths(analysis, components)

        # Calculate overall confidence
        analysis.confidence = self._calculate_confidence(analysis, components)

        logger.info(f"Architecture analysis complete: {len(analysis.patterns_detected)} patterns detected")
        return analysis

    def _analyze_component(self, search_result: SearchResult) -> ComponentAnalysis | None:
        """Analyze a single component."""
        try:
            content = search_result.content
            file_path = search_result.chunk.file_metadata.file_path

            # Determine component type
            component_type = self._determine_component_type(content, file_path)
            if not component_type:
                return None

            component = ComponentAnalysis(
                component_name=self._extract_component_name(content, file_path),
                file_path=file_path,
                component_type=component_type,
            )

            # Analyze responsibilities
            component.responsibilities = self._extract_responsibilities(content)

            # Analyze dependencies
            component.dependencies = self._extract_dependencies(content)

            # Detect patterns in this component
            component.patterns_used = self._detect_component_patterns(content)

            # Check for SOLID violations
            component.solid_violations = self._check_solid_violations(content)

            # Calculate complexity
            component.complexity_score = self._calculate_complexity(content)

            return component

        except Exception as e:
            try:
                file_path = search_result.chunk.file_metadata.file_path
            except (AttributeError, TypeError):
                file_path = "unknown"
            logger.warning(f"Failed to analyze component {file_path}: {e}")
            return None

    def _determine_component_type(self, content: str, file_path: str) -> str | None:
        """Determine the type of component (class, function, module)."""
        if file_path.endswith(".py"):
            if "class " in content:
                return "class"
            if "def " in content and "class " not in content:
                return "function"
            return "module"
        if file_path.endswith((".md", ".rst")):
            return "documentation"
        if file_path.endswith((".json", ".yaml", ".yml", ".toml")):
            return "configuration"
        return None

    def _extract_component_name(self, content: str, file_path: str) -> str:
        """Extract the main component name."""
        # Try to extract class name
        class_match = re.search(r"class\s+(\w+)", content)
        if class_match:
            return class_match.group(1)

        # Try to extract main function name
        func_match = re.search(r"def\s+(\w+)", content)
        if func_match:
            return func_match.group(1)

        # Fall back to file name
        return file_path.split("/")[-1].split(".")[0]

    def _extract_responsibilities(self, content: str) -> list[str]:
        """Extract the responsibilities of a component."""
        responsibilities = []

        # Look for docstrings that describe responsibilities
        docstring_pattern = r'"""(.*?)"""'
        docstrings = re.findall(docstring_pattern, content, re.DOTALL)

        for docstring in docstrings:
            # Extract sentences that describe what the component does
            sentences = [s.strip() for s in docstring.split(".") if s.strip()]
            for sentence in sentences[:3]:  # Limit to first 3 sentences
                if any(word in sentence.lower() for word in ["provide", "handle", "manage", "implement", "create"]):
                    responsibilities.append(sentence)

        # If no docstring responsibilities found, infer from method names
        if not responsibilities:
            method_names = re.findall(r"def\s+(\w+)", content)
            for method in method_names[:5]:  # Limit to first 5 methods
                if not method.startswith("_"):  # Skip private methods
                    responsibilities.append(f"Implements {method} functionality")

        return responsibilities

    def _extract_dependencies(self, content: str) -> list[str]:
        """Extract dependencies from imports and usage."""
        dependencies = []

        # Extract imports
        import_patterns = [
            r"from\s+([\w.]+)\s+import",
            r"import\s+([\w.]+)",
        ]

        for pattern in import_patterns:
            matches = re.findall(pattern, content)
            dependencies.extend(matches)

        # Remove standard library and relative imports
        filtered_deps = []
        for dep in dependencies:
            if not dep.startswith(".") and dep not in ["os", "sys", "json", "re", "time", "logging"]:
                filtered_deps.append(dep)

        return list(set(filtered_deps))  # Remove duplicates

    def _detect_component_patterns(self, content: str) -> list[ArchitecturePattern]:
        """Detect patterns used in a single component."""
        patterns = []

        for pattern, detector in self.pattern_detectors.items():
            if detector(content):
                patterns.append(pattern)

        return patterns

    def _detect_factory_pattern(self, content: str) -> bool:
        """Detect factory pattern usage."""
        factory_indicators = [
            r"class\s+\w*Factory",
            r"def\s+create_\w+",
            r"def\s+make_\w+",
            r"@staticmethod.*create",
        ]
        return any(re.search(pattern, content) for pattern in factory_indicators)

    def _detect_singleton_pattern(self, content: str) -> bool:
        """Detect singleton pattern usage."""
        singleton_indicators = [
            r"_instance\s*=\s*None",
            r"__new__.*cls\._instance",
            r"@singleton",
        ]
        return any(re.search(pattern, content) for pattern in singleton_indicators)

    def _detect_strategy_pattern(self, content: str) -> bool:
        """Detect strategy pattern usage."""
        strategy_indicators = [
            r"class\s+\w*Strategy",
            r"def\s+execute\s*\(",
            r"strategy\s*=",
        ]
        return any(re.search(pattern, content) for pattern in strategy_indicators)

    def _detect_dependency_injection(self, content: str) -> bool:
        """Detect dependency injection pattern."""
        # Check if constructor takes dependencies (more than just self)
        init_match = re.search(r"def\s+__init__\(self(?:,\s*([^)]+))?\)", content)
        if init_match:
            params = init_match.group(1)
            if params and params.strip():
                # Has parameters other than self
                param_count = len([p.strip() for p in params.split(",") if p.strip()])
                if param_count > 0:
                    return True

        # Check for @inject decorator
        return bool(re.search(r"@inject", content))

    def _detect_repository_pattern(self, content: str) -> bool:
        """Detect repository pattern usage."""
        repo_indicators = [
            r"class\s+\w*Repository",
            r"def\s+find_\w+",
            r"def\s+save\s*\(",
            r"def\s+delete\s*\(",
        ]
        return any(re.search(pattern, content) for pattern in repo_indicators)

    def _detect_rag_pipeline_pattern(self, content: str) -> bool:
        """Detect RAG pipeline pattern specific to this project."""
        rag_indicators = [
            r"class\s+\w*Pipeline",
            r"embedding",
            r"vector.*store",
            r"search.*results",
            r"chunk",
        ]
        return sum(1 for pattern in rag_indicators if re.search(pattern, content, re.IGNORECASE)) >= 2

    def _check_solid_violations(self, content: str) -> list[str]:
        """Check for SOLID principle violations."""
        violations = []

        # Single Responsibility Principle violations
        if self._has_srp_violation(content):
            violations.append("Single Responsibility Principle: Component has multiple responsibilities")

        # Open/Closed Principle violations
        if self._has_ocp_violation(content):
            violations.append("Open/Closed Principle: Component is not easily extensible")

        # Dependency Inversion violations
        if self._has_dip_violation(content):
            violations.append("Dependency Inversion Principle: Component depends on concretions")

        return violations

    def _has_srp_violation(self, content: str) -> bool:
        """Check for Single Responsibility Principle violations."""
        # Count distinct responsibilities (rough heuristic)
        method_count = len(re.findall(r"def\s+\w+", content))
        class_count = len(re.findall(r"class\s+\w+", content))

        # If a class has too many methods, it might violate SRP
        if class_count > 0 and method_count > 15:
            return True

        # Check for mixed concerns (e.g., data access + business logic)
        concerns = {
            "data_access": ["save", "load", "fetch", "query", "database"],
            "business_logic": ["calculate", "process", "validate", "transform"],
            "ui_logic": ["render", "display", "show", "format"],
            "network": ["request", "response", "http", "api"],
        }

        detected_concerns = []
        for concern, keywords in concerns.items():
            if any(keyword in content.lower() for keyword in keywords):
                detected_concerns.append(concern)

        return len(detected_concerns) > 2

    def _has_ocp_violation(self, content: str) -> bool:
        """Check for Open/Closed Principle violations."""
        # Look for large if/elif chains that should be polymorphic
        if_elif_pattern = r"if\s+.*:\s*.*\s*elif\s+.*:\s*.*\s*elif"
        return bool(re.search(if_elif_pattern, content, re.DOTALL))

    def _has_dip_violation(self, content: str) -> bool:
        """Check for Dependency Inversion Principle violations."""
        # Look for direct instantiation of concrete classes
        concrete_instantiation = r"\w+\s*=\s*\w+\("
        instantiations = re.findall(concrete_instantiation, content)

        # Filter out obvious non-violations (primitives, built-ins)
        violations = [inst for inst in instantiations
                     if not any(builtin in inst.lower()
                               for builtin in ["str(", "int(", "float(", "list(", "dict(", "set("])]

        return len(violations) > 3

    def _calculate_complexity(self, content: str) -> float:
        """Calculate complexity score for a component."""
        # Simple complexity metrics
        lines = content.split("\n")
        non_empty_lines = [line for line in lines if line.strip()]

        # Count various complexity indicators
        method_count = len(re.findall(r"def\s+\w+", content))
        class_count = len(re.findall(r"class\s+\w+", content))
        if_count = len(re.findall(r"\bif\b", content))
        loop_count = len(re.findall(r"\b(for|while)\b", content))
        try_count = len(re.findall(r"\btry\b", content))

        # Calculate weighted complexity
        complexity = (
            len(non_empty_lines) * 0.1 +
            method_count * 2 +
            class_count * 3 +
            if_count * 1.5 +
            loop_count * 2 +
            try_count * 1
        )

        # Normalize to 0-10 scale
        return min(complexity / 50, 10.0)

    def _detect_patterns(self, components: list[ComponentAnalysis]) -> list[ArchitecturePattern]:
        """Detect patterns across multiple components."""
        pattern_counts = {}

        for component in components:
            for pattern in component.patterns_used:
                pattern_counts[pattern] = pattern_counts.get(pattern, 0) + 1

        # Return patterns that appear in multiple components
        return [pattern for pattern, count in pattern_counts.items() if count >= 2]

    def _analyze_solid_compliance(self, components: list[ComponentAnalysis]) -> dict[SOLIDPrinciple, float]:
        """Analyze SOLID compliance across components."""
        compliance = {}

        for principle, analyzer in self.solid_analyzers.items():
            scores = []
            for component in components:
                score = analyzer(component)
                if score is not None:
                    scores.append(score)

            if scores:
                compliance[principle] = sum(scores) / len(scores)
            else:
                compliance[principle] = 0.5  # Default neutral score

        return compliance

    def _analyze_single_responsibility(self, component: ComponentAnalysis) -> float:
        """Analyze Single Responsibility Principle compliance."""
        # Score based on number of responsibilities and violations
        responsibility_count = len(component.responsibilities)
        has_violation = any("Single Responsibility" in violation for violation in component.solid_violations)

        if has_violation:
            return 0.3
        if responsibility_count <= 2:
            return 0.9
        if responsibility_count <= 4:
            return 0.7
        return 0.5

    def _analyze_open_closed(self, component: ComponentAnalysis) -> float:
        """Analyze Open/Closed Principle compliance."""
        # Check for extensibility patterns
        has_strategy = ArchitecturePattern.STRATEGY in component.patterns_used
        has_factory = ArchitecturePattern.FACTORY in component.patterns_used
        has_violation = any("Open/Closed" in violation for violation in component.solid_violations)

        if has_violation:
            return 0.3
        if has_strategy or has_factory:
            return 0.9
        return 0.6

    def _analyze_liskov_substitution(self, component: ComponentAnalysis) -> float:
        """Analyze Liskov Substitution Principle compliance."""
        # This is harder to detect statically, use heuristics
        if component.component_type == "class":
            return 0.7  # Assume reasonable compliance for classes
        return 0.8  # Non-classes don't typically violate LSP

    def _analyze_interface_segregation(self, component: ComponentAnalysis) -> float:
        """Analyze Interface Segregation Principle compliance."""
        # Check for large interfaces (many dependencies)
        dependency_count = len(component.dependencies)

        if dependency_count <= 3:
            return 0.9
        if dependency_count <= 6:
            return 0.7
        return 0.5

    def _analyze_dependency_inversion(self, component: ComponentAnalysis) -> float:
        """Analyze Dependency Inversion Principle compliance."""
        has_di = ArchitecturePattern.DEPENDENCY_INJECTION in component.patterns_used
        has_violation = any("Dependency Inversion" in violation for violation in component.solid_violations)

        if has_violation:
            return 0.3
        if has_di:
            return 0.9
        return 0.6

    def _calculate_quality_metrics(self, components: list[ComponentAnalysis]) -> dict[str, Any]:
        """Calculate overall code quality metrics."""
        if not components:
            return {}

        complexity_scores = [c.complexity_score for c in components]
        pattern_usage = {}

        for component in components:
            for pattern in component.patterns_used:
                pattern_usage[pattern.value] = pattern_usage.get(pattern.value, 0) + 1

        return {
            "total_components": len(components),
            "average_complexity": sum(complexity_scores) / len(complexity_scores),
            "max_complexity": max(complexity_scores),
            "pattern_usage": pattern_usage,
            "components_with_violations": len([c for c in components if c.solid_violations]),
        }

    def _generate_recommendations(self, analysis: ArchitectureAnalysis, components: list[ComponentAnalysis]) -> list[str]:
        """Generate architecture recommendations."""
        recommendations = []

        # SOLID compliance recommendations
        for principle, score in analysis.solid_compliance.items():
            if score < 0.6:
                recommendations.append(f"Improve {principle.value.replace('_', ' ').title()} compliance (current: {score:.1%})")

        # Pattern recommendations
        if ArchitecturePattern.FACTORY not in analysis.patterns_detected:
            recommendations.append("Consider implementing Factory pattern for object creation")

        if ArchitecturePattern.DEPENDENCY_INJECTION not in analysis.patterns_detected:
            recommendations.append("Consider implementing Dependency Injection for better testability")

        # Complexity recommendations
        high_complexity_components = [c for c in components if c.complexity_score > 7]
        if high_complexity_components:
            recommendations.append(f"Refactor {len(high_complexity_components)} high-complexity components")

        return recommendations

    def _identify_concerns(self, analysis: ArchitectureAnalysis, components: list[ComponentAnalysis]) -> list[str]:
        """Identify architectural concerns."""
        concerns = []

        # Check for widespread SOLID violations
        violation_count = sum(len(c.solid_violations) for c in components)
        if violation_count > len(components) * 0.5:
            concerns.append("High number of SOLID principle violations detected")

        # Check for missing critical patterns
        if ArchitecturePattern.DEPENDENCY_INJECTION not in analysis.patterns_detected:
            concerns.append("Lack of dependency injection may impact testability")

        # Check for high complexity
        avg_complexity = analysis.code_quality_metrics.get("average_complexity", 0)
        if avg_complexity > 5:
            concerns.append("High average component complexity may impact maintainability")

        return concerns

    def _identify_strengths(self, analysis: ArchitectureAnalysis, components: list[ComponentAnalysis]) -> list[str]:
        """Identify architectural strengths."""
        strengths = []

        # Check for good pattern usage
        if len(analysis.patterns_detected) >= 3:
            strengths.append("Good use of design patterns throughout the codebase")

        # Check for SOLID compliance
        good_solid_scores = [score for score in analysis.solid_compliance.values() if score > 0.7]
        if len(good_solid_scores) >= 3:
            strengths.append("Strong SOLID principles compliance")

        # Check for reasonable complexity
        avg_complexity = analysis.code_quality_metrics.get("average_complexity", 0)
        if avg_complexity < 3:
            strengths.append("Low complexity components promote maintainability")

        return strengths

    def _calculate_confidence(self, analysis: ArchitectureAnalysis, components: list[ComponentAnalysis]) -> float:
        """Calculate confidence in the analysis."""
        if not components:
            return 0.0

        # Base confidence on number of components analyzed
        component_confidence = min(len(components) / 20, 1.0)  # Max confidence at 20+ components

        # Adjust based on analysis completeness
        pattern_confidence = min(len(analysis.patterns_detected) / 5, 1.0)
        solid_confidence = len(analysis.solid_compliance) / 5  # 5 SOLID principles

        return (component_confidence + pattern_confidence + solid_confidence) / 3
