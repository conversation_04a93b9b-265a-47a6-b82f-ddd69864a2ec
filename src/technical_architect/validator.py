"""
SOLID Principles Validator

This module provides automated validation of SOLID principles compliance,
design pattern recognition, and architectural quality assessment.
"""

from dataclasses import dataclass, field
from enum import Enum
import logging
from typing import Any

from .analyzer import ArchitectureAnalysis, ComponentAnalysis, SOLIDPrinciple

logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """Severity levels for validation issues."""

    CRITICAL = "critical"
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    INFO = "info"


@dataclass
class ValidationIssue:
    """A specific validation issue found in the code."""

    principle: SOLIDPrinciple
    severity: ValidationSeverity
    title: str
    description: str
    component: str
    file_path: str
    line_number: int = 0
    suggestion: str = ""
    code_snippet: str = ""


@dataclass
class ValidationReport:
    """Comprehensive validation report."""

    overall_score: float
    principle_scores: dict[SOLIDPrinciple, float] = field(default_factory=dict)
    issues: list[ValidationIssue] = field(default_factory=list)
    recommendations: list[str] = field(default_factory=list)
    compliant_components: list[str] = field(default_factory=list)
    non_compliant_components: list[str] = field(default_factory=list)
    summary: dict[str, Any] = field(default_factory=dict)


class SOLIDValidator:
    """Validates SOLID principles compliance in code."""

    def __init__(self):
        """Initialize the SOLID validator."""
        self.validators = {
            SOLIDPrinciple.SINGLE_RESPONSIBILITY: self._validate_srp,
            SOLIDPrinciple.OPEN_CLOSED: self._validate_ocp,
            SOLIDPrinciple.LISKOV_SUBSTITUTION: self._validate_lsp,
            SOLIDPrinciple.INTERFACE_SEGREGATION: self._validate_isp,
            SOLIDPrinciple.DEPENDENCY_INVERSION: self._validate_dip,
        }

        logger.info("Initialized SOLID validator")

    def validate_architecture(self, analysis: ArchitectureAnalysis, components: list[ComponentAnalysis]) -> ValidationReport:
        """Validate SOLID principles compliance across the architecture."""
        logger.info(f"Validating SOLID compliance for {len(components)} components")

        report = ValidationReport(overall_score=0.0)

        # Validate each principle
        all_issues = []
        principle_scores = {}

        for principle, validator in self.validators.items():
            issues, score = validator(components)
            all_issues.extend(issues)
            principle_scores[principle] = score

        report.issues = all_issues
        report.principle_scores = principle_scores

        # Calculate overall score
        if principle_scores:
            report.overall_score = sum(principle_scores.values()) / len(principle_scores)

        # Generate recommendations
        report.recommendations = self._generate_recommendations(report)

        # Categorize components
        report.compliant_components, report.non_compliant_components = self._categorize_components(components, all_issues)

        # Generate summary
        report.summary = self._generate_summary(report, components)

        logger.info(f"Validation complete: {len(all_issues)} issues found, overall score: {report.overall_score:.2f}")
        return report

    def _validate_srp(self, components: list[ComponentAnalysis]) -> tuple[list[ValidationIssue], float]:
        """Validate Single Responsibility Principle."""
        issues = []
        scores = []

        for component in components:
            score = 1.0
            component_issues = []

            # Check for multiple responsibilities
            if len(component.responsibilities) > 3:
                issue = ValidationIssue(
                    principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                    severity=ValidationSeverity.MEDIUM,
                    title="Too Many Responsibilities",
                    description=f"Component has {len(component.responsibilities)} responsibilities, should have 1-2",
                    component=component.component_name,
                    file_path=component.file_path,
                    suggestion="Split component into smaller, focused units with single responsibilities",
                )
                component_issues.append(issue)
                score -= 0.3

            # Check for mixed concerns in method names
            method_concerns = self._analyze_method_concerns(component)
            if len(method_concerns) > 2:
                issue = ValidationIssue(
                    principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                    severity=ValidationSeverity.HIGH,
                    title="Mixed Concerns Detected",
                    description=f"Component mixes {len(method_concerns)} different concerns: {', '.join(method_concerns)}",
                    component=component.component_name,
                    file_path=component.file_path,
                    suggestion="Separate concerns into different classes or modules",
                )
                component_issues.append(issue)
                score -= 0.5

            # Check complexity as indicator of multiple responsibilities
            if component.complexity_score > 8:
                issue = ValidationIssue(
                    principle=SOLIDPrinciple.SINGLE_RESPONSIBILITY,
                    severity=ValidationSeverity.MEDIUM,
                    title="High Complexity Indicates Multiple Responsibilities",
                    description=f"Complexity score {component.complexity_score:.1f} suggests multiple responsibilities",
                    component=component.component_name,
                    file_path=component.file_path,
                    suggestion="Refactor to reduce complexity and focus on single responsibility",
                )
                component_issues.append(issue)
                score -= 0.2

            issues.extend(component_issues)
            scores.append(max(0, score))

        avg_score = sum(scores) / len(scores) if scores else 0.0
        return issues, avg_score

    def _validate_ocp(self, components: list[ComponentAnalysis]) -> tuple[list[ValidationIssue], float]:
        """Validate Open/Closed Principle."""
        issues = []
        scores = []

        for component in components:
            score = 1.0
            component_issues = []

            # Check for extensibility patterns
            has_extensibility = any(
                pattern.value in ["strategy", "factory", "template_method"]
                for pattern in component.patterns_used
            )

            # Check for large if/elif chains (should use polymorphism)
            if "Open/Closed" in component.solid_violations:
                issue = ValidationIssue(
                    principle=SOLIDPrinciple.OPEN_CLOSED,
                    severity=ValidationSeverity.HIGH,
                    title="Violation of Open/Closed Principle",
                    description="Component uses large conditional statements instead of polymorphism",
                    component=component.component_name,
                    file_path=component.file_path,
                    suggestion="Replace conditional logic with strategy pattern or polymorphism",
                )
                component_issues.append(issue)
                score -= 0.6

            # Check for hardcoded behavior
            if not has_extensibility and component.component_type == "class":
                issue = ValidationIssue(
                    principle=SOLIDPrinciple.OPEN_CLOSED,
                    severity=ValidationSeverity.MEDIUM,
                    title="Limited Extensibility",
                    description="Component lacks extensibility patterns for future modifications",
                    component=component.component_name,
                    file_path=component.file_path,
                    suggestion="Consider adding strategy pattern or factory methods for extensibility",
                )
                component_issues.append(issue)
                score -= 0.3

            issues.extend(component_issues)
            scores.append(max(0, score))

        avg_score = sum(scores) / len(scores) if scores else 0.0
        return issues, avg_score

    def _validate_lsp(self, components: list[ComponentAnalysis]) -> tuple[list[ValidationIssue], float]:
        """Validate Liskov Substitution Principle."""
        issues = []
        scores = []

        for component in components:
            score = 1.0

            # LSP is harder to detect statically, but we can check for common violations
            if component.component_type == "class":
                # Check for inheritance-related issues
                # This is a simplified check - real LSP validation requires semantic analysis

                # For now, assume reasonable compliance for classes that follow other principles
                score = 0.8 if len(component.solid_violations) == 0 else 0.6

            scores.append(score)

        avg_score = sum(scores) / len(scores) if scores else 0.8  # Default to good score
        return issues, avg_score

    def _validate_isp(self, components: list[ComponentAnalysis]) -> tuple[list[ValidationIssue], float]:
        """Validate Interface Segregation Principle."""
        issues = []
        scores = []

        for component in components:
            score = 1.0
            component_issues = []

            # Check for large interfaces (many dependencies)
            if len(component.dependencies) > 8:
                issue = ValidationIssue(
                    principle=SOLIDPrinciple.INTERFACE_SEGREGATION,
                    severity=ValidationSeverity.MEDIUM,
                    title="Large Interface Dependencies",
                    description=f"Component depends on {len(component.dependencies)} interfaces/modules",
                    component=component.component_name,
                    file_path=component.file_path,
                    suggestion="Split large interfaces into smaller, focused ones",
                )
                component_issues.append(issue)
                score -= 0.4

            # Check for unused dependencies (potential interface bloat)
            # This would require more sophisticated analysis in practice

            issues.extend(component_issues)
            scores.append(max(0, score))

        avg_score = sum(scores) / len(scores) if scores else 0.0
        return issues, avg_score

    def _validate_dip(self, components: list[ComponentAnalysis]) -> tuple[list[ValidationIssue], float]:
        """Validate Dependency Inversion Principle."""
        issues = []
        scores = []

        for component in components:
            score = 1.0
            component_issues = []

            # Check for dependency injection usage
            has_di = any(pattern.value == "dependency_injection" for pattern in component.patterns_used)

            if not has_di and component.component_type == "class":
                issue = ValidationIssue(
                    principle=SOLIDPrinciple.DEPENDENCY_INVERSION,
                    severity=ValidationSeverity.MEDIUM,
                    title="Missing Dependency Injection",
                    description="Component doesn't use dependency injection pattern",
                    component=component.component_name,
                    file_path=component.file_path,
                    suggestion="Implement dependency injection for better testability and flexibility",
                )
                component_issues.append(issue)
                score -= 0.4

            # Check for concrete dependencies
            if "Dependency Inversion" in component.solid_violations:
                issue = ValidationIssue(
                    principle=SOLIDPrinciple.DEPENDENCY_INVERSION,
                    severity=ValidationSeverity.HIGH,
                    title="Depends on Concrete Classes",
                    description="Component depends on concrete implementations instead of abstractions",
                    component=component.component_name,
                    file_path=component.file_path,
                    suggestion="Depend on abstractions (interfaces) rather than concrete implementations",
                )
                component_issues.append(issue)
                score -= 0.6

            issues.extend(component_issues)
            scores.append(max(0, score))

        avg_score = sum(scores) / len(scores) if scores else 0.0
        return issues, avg_score

    def _analyze_method_concerns(self, component: ComponentAnalysis) -> list[str]:
        """Analyze method names to identify different concerns."""
        concerns = set()

        # Define concern patterns
        concern_patterns = {
            "data_access": ["save", "load", "fetch", "query", "find", "get", "set", "store"],
            "business_logic": ["calculate", "process", "validate", "transform", "compute"],
            "ui_presentation": ["render", "display", "show", "format", "present"],
            "communication": ["send", "receive", "request", "response", "notify"],
            "configuration": ["configure", "setup", "initialize", "config"],
            "logging": ["log", "debug", "trace", "monitor"],
        }

        # Extract method names from responsibilities
        for responsibility in component.responsibilities:
            responsibility_lower = responsibility.lower()
            for concern, patterns in concern_patterns.items():
                if any(pattern in responsibility_lower for pattern in patterns):
                    concerns.add(concern)

        return list(concerns)

    def _generate_recommendations(self, report: ValidationReport) -> list[str]:
        """Generate recommendations based on validation results."""
        recommendations = []

        # Recommendations based on principle scores
        for principle, score in report.principle_scores.items():
            if score < 0.6:
                principle_name = principle.value.replace("_", " ").title()
                recommendations.append(f"Focus on improving {principle_name} compliance (current score: {score:.1%})")

        # Recommendations based on issue severity
        critical_issues = [i for i in report.issues if i.severity == ValidationSeverity.CRITICAL]
        high_issues = [i for i in report.issues if i.severity == ValidationSeverity.HIGH]

        if critical_issues:
            recommendations.append(f"Address {len(critical_issues)} critical SOLID violations immediately")

        if high_issues:
            recommendations.append(f"Plan to fix {len(high_issues)} high-priority SOLID issues")

        # General recommendations
        if report.overall_score < 0.7:
            recommendations.append("Consider refactoring to improve overall SOLID compliance")

        if len(report.non_compliant_components) > len(report.compliant_components):
            recommendations.append("Focus on component-level refactoring to improve architecture quality")

        return recommendations

    def _categorize_components(
        self, components: list[ComponentAnalysis], issues: list[ValidationIssue]
    ) -> tuple[list[str], list[str]]:
        """Categorize components as compliant or non-compliant."""
        components_with_issues = {issue.component for issue in issues}

        compliant = []
        non_compliant = []

        for component in components:
            if component.component_name in components_with_issues:
                non_compliant.append(component.component_name)
            else:
                compliant.append(component.component_name)

        return compliant, non_compliant

    def _generate_summary(self, report: ValidationReport, components: list[ComponentAnalysis]) -> dict[str, Any]:
        """Generate validation summary."""
        return {
            "total_components": len(components),
            "compliant_components": len(report.compliant_components),
            "non_compliant_components": len(report.non_compliant_components),
            "total_issues": len(report.issues),
            "critical_issues": len([i for i in report.issues if i.severity == ValidationSeverity.CRITICAL]),
            "high_issues": len([i for i in report.issues if i.severity == ValidationSeverity.HIGH]),
            "medium_issues": len([i for i in report.issues if i.severity == ValidationSeverity.MEDIUM]),
            "low_issues": len([i for i in report.issues if i.severity == ValidationSeverity.LOW]),
            "overall_score": report.overall_score,
            "principle_scores": {p.value: s for p, s in report.principle_scores.items()},
        }
