# Development Environment Configuration
# This file contains development-specific settings

application:
  name: "LLM RAG Codebase Query System"
  version: "0.1.0"
  environment: "development"
  debug: true

logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  handlers:
    - console

api:
  host: "0.0.0.0"
  port: 8000
  workers: 1
  reload: true
  cors:
    enabled: true
    origins:
      - "http://localhost:3000"
      - "http://127.0.0.1:3000"

llm:
  provider: "openai"
  model: "gpt-5"
  temperature: 0.1
  max_tokens: 4000
  timeout: 30

embedding:
  model: "text-embedding-ada-002"
  dimension: 1536
  batch_size: 100

vector_db:
  provider: "chroma"
  host: "localhost"
  port: 8001
  collection: "codebase_embeddings_dev"

cache:
  provider: "redis"
  host: "localhost"
  port: 6379
  db: 0
  ttl: 3600

github:
  api_url: "https://api.github.com"
  timeout: 30
  rate_limit:
    requests: 5000
    window: 3600

processing:
  chunk_size: 1000
  chunk_overlap: 200
  max_file_size: 10485760 # 10MB
  allowed_extensions:
    - ".py"
    - ".js"
    - ".ts"
    - ".md"
    - ".txt"
    - ".json"
    - ".yaml"
    - ".yml"
  temp_directory: "/tmp/rag_processing"

security:
  jwt_expiration: 3600
  rate_limiting:
    enabled: true
    requests: 100
    window: 60

monitoring:
  metrics:
    enabled: true
    port: 9090
  health_check:
    enabled: true
    endpoint: "/health"
