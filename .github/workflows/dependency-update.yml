name: Dependency Updates

on:
  schedule:
    # Run weekly on Mondays at 9 AM UTC
    - cron: '0 9 * * 1'
  workflow_dispatch: # Allow manual triggering

jobs:
  update-python-deps:
    name: Update Python Dependencies
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"
        
    - name: Set up Python
      run: uv python install 3.13
      
    - name: Update dependencies
      run: |
        uv sync --upgrade
        uv export --dev > requirements-dev.txt
        
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v6
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: "chore: update Python dependencies"
        title: "chore: update Python dependencies"
        body: |
          Automated dependency update for Python packages.
          
          Please review the changes and ensure all tests pass before merging.
        branch: update-python-deps
        delete-branch: true

  update-node-deps:
    name: Update Node.js Dependencies
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 20
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 9
        
    - name: Update dependencies
      run: |
        cd frontend
        pnpm update --latest
        
    - name: Create Pull Request
      uses: peter-evans/create-pull-request@v6
      with:
        token: ${{ secrets.GITHUB_TOKEN }}
        commit-message: "chore: update Node.js dependencies"
        title: "chore: update Node.js dependencies"
        body: |
          Automated dependency update for Node.js packages.
          
          Please review the changes and ensure all tests pass before merging.
        branch: update-node-deps
        delete-branch: true
