name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

env:
  PYTHON_VERSION: "3.13"
  NODE_VERSION: "20"

jobs:
  # Python Backend Tests and Quality Checks
  backend-quality:
    name: Backend Quality Checks
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"
        
    - name: Set up Python
      run: uv python install ${{ env.PYTHON_VERSION }}
      
    - name: Install dependencies
      run: uv sync --dev
      
    - name: Run Ruff linting
      run: uv run ruff check .
      
    - name: Run Ruff formatting check
      run: uv run ruff format --check .
      
    - name: Run Black formatting check
      run: uv run black --check .
      
    - name: Run MyPy type checking
      run: uv run mypy src/
      continue-on-error: true  # Allow to continue if type checking fails
      
    - name: Run Bandit security checks
      run: uv run bandit -r src/ -f json -o bandit-report.json
      continue-on-error: true
      
    - name: Upload Bandit report
      uses: actions/upload-artifact@v4
      if: always()
      with:
        name: bandit-report
        path: bandit-report.json

  # Python Backend Tests
  backend-tests:
    name: Backend Tests
    runs-on: ubuntu-latest
    needs: backend-quality
    
    services:
      redis:
        image: redis:7-alpine
        ports:
          - 6379:6379
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
          
      chroma:
        image: chromadb/chroma:latest
        ports:
          - 8001:8000
        env:
          CHROMA_SERVER_HOST: 0.0.0.0
          CHROMA_SERVER_HTTP_PORT: 8000
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Install uv
      uses: astral-sh/setup-uv@v4
      with:
        version: "latest"
        
    - name: Set up Python
      run: uv python install ${{ env.PYTHON_VERSION }}
      
    - name: Install dependencies
      run: uv sync --dev
      
    - name: Run tests with coverage
      run: uv run pytest --cov=src --cov-report=xml --cov-report=html
      env:
        REDIS_HOST: localhost
        REDIS_PORT: 6379
        CHROMA_HOST: localhost
        CHROMA_PORT: 8001
        
    - name: Upload coverage to Codecov
      uses: codecov/codecov-action@v4
      with:
        file: ./coverage.xml
        flags: backend
        name: backend-coverage

  # Frontend Quality Checks
  frontend-quality:
    name: Frontend Quality Checks
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 9
        
    - name: Install dependencies
      run: cd frontend && pnpm install --frozen-lockfile
      
    - name: Run ESLint
      run: cd frontend && pnpm run lint
      
    - name: Run Prettier check
      run: cd frontend && pnpm run format:check
      
    - name: Run TypeScript check
      run: cd frontend && pnpm run type-check

  # Frontend Tests
  frontend-tests:
    name: Frontend Tests
    runs-on: ubuntu-latest
    needs: frontend-quality
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        
    - name: Setup pnpm
      uses: pnpm/action-setup@v4
      with:
        version: 9
        
    - name: Install dependencies
      run: cd frontend && pnpm install --frozen-lockfile
      
    - name: Run tests
      run: cd frontend && pnpm run test
      
    - name: Build application
      run: cd frontend && pnpm run build

  # Docker Build Tests
  docker-build:
    name: Docker Build Tests
    runs-on: ubuntu-latest
    needs: [backend-tests, frontend-tests]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3
      
    - name: Build backend Docker image
      uses: docker/build-push-action@v5
      with:
        context: .
        file: backend/Dockerfile
        push: false
        tags: llm-rag-backend:test
        cache-from: type=gha
        cache-to: type=gha,mode=max
        
    - name: Build frontend Docker image
      uses: docker/build-push-action@v5
      with:
        context: frontend
        file: frontend/Dockerfile
        push: false
        tags: llm-rag-frontend:test
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # Security Scans
  security:
    name: Security Scans
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Run Trivy vulnerability scanner
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'
        
    - name: Upload Trivy scan results to GitHub Security tab
      uses: github/codeql-action/upload-sarif@v3
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'
