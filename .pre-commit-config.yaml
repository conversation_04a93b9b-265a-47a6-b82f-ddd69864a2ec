# Pre-commit hooks configuration for LLM RAG Codebase Query System
# See https://pre-commit.com for more information

repos:
  # General hooks for all files
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        exclude: \.md$
      - id: end-of-file-fixer
      - id: check-yaml
        args: [--unsafe]
      - id: check-json
      - id: check-toml
      - id: check-xml
      - id: check-merge-conflict
      - id: check-case-conflict
      - id: check-symlinks
      - id: check-executables-have-shebangs
      - id: check-shebang-scripts-are-executable
      - id: detect-private-key
      - id: mixed-line-ending
        args: [--fix=lf]

  # Python code formatting with Black
  - repo: https://github.com/psf/black
    rev: 24.10.0
    hooks:
      - id: black
        language_version: python3.13
        args: [--line-length=88]

  # Python linting with Ruff
  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.4
    hooks:
      - id: ruff
        args: [--fix, --exit-non-zero-on-fix]
      - id: ruff-format

  # Python type checking with mypy
  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.13.0
    hooks:
      - id: mypy
        additional_dependencies: [types-all]
        args: [--ignore-missing-imports, --show-error-codes]

  # Python security checks with bandit
  - repo: https://github.com/PyCQA/bandit
    rev: 1.8.0
    hooks:
      - id: bandit
        args: [-r, src/, -f, json, -o, bandit-report.json]
        pass_filenames: false

  # Python dependency security checks
  - repo: https://github.com/PyCQA/safety
    rev: 3.2.11
    hooks:
      - id: safety
        args: [--json, --output, safety-report.json]

  # JavaScript/TypeScript formatting with Prettier
  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v4.0.0-alpha.8
    hooks:
      - id: prettier
        files: \.(js|jsx|ts|tsx|json|css|scss|md|yaml|yml)$
        exclude: |
          (?x)^(
            frontend/node_modules/.*|
            \.next/.*|
            dist/.*|
            build/.*|
            coverage/.*
          )$

  # JavaScript/TypeScript linting with ESLint
  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v9.15.0
    hooks:
      - id: eslint
        files: \.(js|jsx|ts|tsx)$
        types: [file]
        additional_dependencies:
          - eslint@9.33.0
          - "@typescript-eslint/eslint-plugin@8.39.0"
          - "@typescript-eslint/parser@8.39.0"
          - "@eslint/eslintrc@3.3.1"
          - eslint-config-next@15.4.6
        args: [--fix, --max-warnings=0]

  # Dockerfile linting
  - repo: https://github.com/hadolint/hadolint
    rev: v2.12.0
    hooks:
      - id: hadolint-docker
        args: [--ignore, DL3008, --ignore, DL3009]

  # YAML linting
  - repo: https://github.com/adrienverge/yamllint
    rev: v1.35.1
    hooks:
      - id: yamllint
        args: [-d, relaxed]

  # Markdown linting
  - repo: https://github.com/igorshubovych/markdownlint-cli
    rev: v0.42.0
    hooks:
      - id: markdownlint
        args: [--fix]

  # Git commit message linting (Conventional Commits)
  - repo: https://github.com/compilerla/conventional-pre-commit
    rev: v3.6.0
    hooks:
      - id: conventional-pre-commit
        stages: [commit-msg]

  # Secrets detection
  - repo: https://github.com/Yelp/detect-secrets
    rev: v1.5.0
    hooks:
      - id: detect-secrets
        args: [--baseline, .secrets.baseline]

# Configuration for specific tools
ci:
  autofix_commit_msg: |
    [pre-commit.ci] auto fixes from pre-commit.com hooks

    for more information, see https://pre-commit.ci
  autofix_prs: true
  autoupdate_branch: ''
  autoupdate_commit_msg: '[pre-commit.ci] pre-commit autoupdate'
  autoupdate_schedule: weekly
  skip: [safety, bandit]  # Skip these in CI as they require network access
  submodules: false
