version: '3.8'

services:
  # Backend service
  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    ports:
      - "8000:8000"
    environment:
      - ENVIRONMENT=development
      - PYTHONPATH=/app
    env_file:
      - .env
    volumes:
      - ./src:/app/src
      - ./data:/app/data
      - ./config:/app/config
      - ./prompts:/app/prompts
    depends_on:
      - chroma
      - redis
    networks:
      - rag-network
    restart: unless-stopped

  # Frontend service
  frontend:
    build:
      context: frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    volumes:
      - ./frontend/src:/app/src
      - ./frontend/public:/app/public
    depends_on:
      - backend
    networks:
      - rag-network
    restart: unless-stopped

  # ChromaDB for vector storage
  chroma:
    image: chromadb/chroma:latest
    ports:
      - "8001:8000"
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    volumes:
      - chroma_data:/chroma/chroma
    networks:
      - rag-network
    restart: unless-stopped

  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - rag-network
    restart: unless-stopped
    command: redis-server --appendonly yes

volumes:
  chroma_data:
  redis_data:

networks:
  rag-network:
    driver: bridge
