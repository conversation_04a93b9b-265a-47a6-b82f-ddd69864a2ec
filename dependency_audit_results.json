{"dependencies": [{"name": "aiohappyeyeballs", "version": "2.6.1", "vulns": []}, {"name": "aiohttp", "version": "3.12.15", "vulns": []}, {"name": "aiosignal", "version": "1.4.0", "vulns": []}, {"name": "aiosqlite", "version": "0.21.0", "vulns": []}, {"name": "annotated-types", "version": "0.7.0", "vulns": []}, {"name": "anyio", "version": "4.10.0", "vulns": []}, {"name": "attrs", "version": "25.3.0", "vulns": []}, {"name": "authlib", "version": "1.6.1", "vulns": []}, {"name": "backoff", "version": "2.2.1", "vulns": []}, {"name": "bandit", "version": "1.8.6", "vulns": []}, {"name": "banks", "version": "2.2.0", "vulns": []}, {"name": "bcrypt", "version": "4.3.0", "vulns": []}, {"name": "beautifulsoup4", "version": "4.13.4", "vulns": []}, {"name": "black", "version": "25.1.0", "vulns": []}, {"name": "boolean-py", "version": "5.0", "vulns": []}, {"name": "build", "version": "1.3.0", "vulns": []}, {"name": "cachecontrol", "version": "0.14.3", "vulns": []}, {"name": "cachetools", "version": "5.5.2", "vulns": []}, {"name": "certifi", "version": "2025.8.3", "vulns": []}, {"name": "cffi", "version": "1.17.1", "vulns": []}, {"name": "cfgv", "version": "3.4.0", "vulns": []}, {"name": "chardet", "version": "5.2.0", "vulns": []}, {"name": "charset-normalizer", "version": "3.4.3", "vulns": []}, {"name": "chromadb", "version": "1.0.16", "vulns": []}, {"name": "click", "version": "8.2.1", "vulns": []}, {"name": "colorama", "version": "0.4.6", "vulns": []}, {"name": "coloredlogs", "version": "15.0.1", "vulns": []}, {"name": "coverage", "version": "7.10.2", "vulns": []}, {"name": "cryptography", "version": "45.0.6", "vulns": []}, {"name": "cyclonedx-python-lib", "version": "9.1.0", "vulns": []}, {"name": "dataclasses-json", "version": "0.6.7", "vulns": []}, {"name": "defusedxml", "version": "0.7.1", "vulns": []}, {"name": "deprecated", "version": "1.2.18", "vulns": []}, {"name": "deprecation", "version": "2.1.0", "vulns": []}, {"name": "detect-secrets", "version": "1.5.0", "vulns": []}, {"name": "<PERSON><PERSON><PERSON>", "version": "1.0.8", "vulns": []}, {"name": "diskcache", "version": "5.6.3", "vulns": []}, {"name": "distlib", "version": "0.4.0", "vulns": []}, {"name": "distro", "version": "1.9.0", "vulns": []}, {"name": "docstring-parser", "version": "0.17.0", "vulns": []}, {"name": "dparse", "version": "0.6.4", "vulns": []}, {"name": "durationpy", "version": "0.10", "vulns": []}, {"name": "<PERSON><PERSON><PERSON>", "version": "0.116.1", "vulns": []}, {"name": "filelock", "version": "3.12.4", "vulns": []}, {"name": "filetype", "version": "1.2.0", "vulns": []}, {"name": "flatbuffers", "version": "25.2.10", "vulns": []}, {"name": "frozenlist", "version": "1.7.0", "vulns": []}, {"name": "fsspec", "version": "2025.7.0", "vulns": []}, {"name": "gitdb", "version": "4.0.12", "vulns": []}, {"name": "git<PERSON><PERSON>on", "version": "3.1.45", "vulns": []}, {"name": "google-auth", "version": "2.40.3", "vulns": []}, {"name": "googleapis-common-protos", "version": "1.70.0", "vulns": []}, {"name": "greenlet", "version": "3.2.4", "vulns": []}, {"name": "griffe", "version": "1.11.0", "vulns": []}, {"name": "grpcio", "version": "1.74.0", "vulns": []}, {"name": "grpcio-health-checking", "version": "1.74.0", "vulns": []}, {"name": "h11", "version": "0.16.0", "vulns": []}, {"name": "hf-xet", "version": "1.1.7", "vulns": []}, {"name": "httpcore", "version": "1.0.9", "vulns": []}, {"name": "httptools", "version": "0.6.4", "vulns": []}, {"name": "httpx", "version": "0.28.1", "vulns": []}, {"name": "huggingface-hub", "version": "0.34.4", "vulns": []}, {"name": "humanfriendly", "version": "10.0", "vulns": []}, {"name": "identify", "version": "2.6.13", "vulns": []}, {"name": "idna", "version": "3.10", "vulns": []}, {"name": "importlib-metadata", "version": "8.7.0", "vulns": []}, {"name": "importlib-resources", "version": "6.5.2", "vulns": []}, {"name": "iniconfig", "version": "2.1.0", "vulns": []}, {"name": "instructor", "version": "1.10.0", "vulns": []}, {"name": "jinja2", "version": "3.1.6", "vulns": []}, {"name": "jiter", "version": "0.10.0", "vulns": []}, {"name": "joblib", "version": "1.5.1", "vulns": []}, {"name": "jsonpatch", "version": "1.33", "vulns": []}, {"name": "<PERSON><PERSON><PERSON>er", "version": "3.0.0", "vulns": []}, {"name": "jsonschema", "version": "4.25.0", "vulns": []}, {"name": "jsonschema-specifications", "version": "2025.4.1", "vulns": []}, {"name": "kubernetes", "version": "33.1.0", "vulns": []}, {"name": "langchain", "version": "0.3.27", "vulns": []}, {"name": "langchain-core", "version": "0.3.74", "vulns": []}, {"name": "langchain-text-splitters", "version": "0.3.9", "vulns": []}, {"name": "langsmith", "version": "0.4.13", "vulns": []}, {"name": "license-expression", "version": "30.4.4", "vulns": []}, {"name": "llama-cloud", "version": "0.1.35", "vulns": []}, {"name": "llama-cloud-services", "version": "0.6.54", "vulns": []}, {"name": "llama-index", "version": "0.13.1", "vulns": []}, {"name": "llama-index-cli", "version": "0.5.0", "vulns": []}, {"name": "llama-index-core", "version": "0.13.1", "vulns": []}, {"name": "llama-index-embeddings-openai", "version": "0.5.0", "vulns": []}, {"name": "llama-index-indices-managed-llama-cloud", "version": "0.9.1", "vulns": []}, {"name": "llama-index-instrumentation", "version": "0.4.0", "vulns": []}, {"name": "llama-index-llms-openai", "version": "0.5.2", "vulns": []}, {"name": "llama-index-readers-file", "version": "0.5.0", "vulns": []}, {"name": "llama-index-readers-llama-parse", "version": "0.5.0", "vulns": []}, {"name": "llama-index-workflows", "version": "1.3.0", "vulns": []}, {"name": "llama-parse", "version": "0.6.54", "vulns": []}, {"name": "llm-rag-codebase-query", "skip_reason": "Dependency not found on PyPI and could not be audited: llm-rag-codebase-query (0.1.0)"}, {"name": "loguru", "version": "0.7.3", "vulns": []}, {"name": "markdown", "version": "3.8.2", "vulns": []}, {"name": "markdown-it-py", "version": "3.0.0", "vulns": []}, {"name": "markupsafe", "version": "3.0.2", "vulns": []}, {"name": "marshmallow", "version": "3.26.1", "vulns": []}, {"name": "mdurl", "version": "0.1.2", "vulns": []}, {"name": "mmh3", "version": "5.2.0", "vulns": []}, {"name": "mpmath", "version": "1.3.0", "vulns": []}, {"name": "msgpack", "version": "1.1.1", "vulns": []}, {"name": "multidict", "version": "6.6.3", "vulns": []}, {"name": "mypy-extensions", "version": "1.1.0", "vulns": []}, {"name": "nest-asyncio", "version": "1.6.0", "vulns": []}, {"name": "networkx", "version": "3.5", "vulns": []}, {"name": "nltk", "version": "3.9.1", "vulns": []}, {"name": "nodeenv", "version": "1.9.1", "vulns": []}, {"name": "numpy", "version": "2.3.2", "vulns": []}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "3.3.1", "vulns": []}, {"name": "onnxruntime", "version": "1.22.1", "vulns": []}, {"name": "openai", "version": "1.99.6", "vulns": []}, {"name": "opentelemetry-api", "version": "1.36.0", "vulns": []}, {"name": "opentelemetry-exporter-otlp-proto-common", "version": "1.36.0", "vulns": []}, {"name": "opentelemetry-exporter-otlp-proto-grpc", "version": "1.36.0", "vulns": []}, {"name": "opentelemetry-proto", "version": "1.36.0", "vulns": []}, {"name": "opentelemetry-sdk", "version": "1.36.0", "vulns": []}, {"name": "opentelemetry-semantic-conventions", "version": "0.57b0", "vulns": []}, {"name": "<PERSON><PERSON><PERSON>", "version": "3.11.1", "vulns": []}, {"name": "overrides", "version": "7.7.0", "vulns": []}, {"name": "packageurl-python", "version": "0.17.5", "vulns": []}, {"name": "packaging", "version": "25.0", "vulns": []}, {"name": "pandas", "version": "2.2.3", "vulns": []}, {"name": "pathspec", "version": "0.12.1", "vulns": []}, {"name": "pbr", "version": "6.1.1", "vulns": []}, {"name": "pdfminer-six", "version": "20250506", "vulns": []}, {"name": "pdfplumber", "version": "0.11.7", "vulns": []}, {"name": "pillow", "version": "11.3.0", "vulns": []}, {"name": "pip", "version": "25.2", "vulns": []}, {"name": "pip-api", "version": "0.0.34", "vulns": []}, {"name": "pip-audit", "version": "2.9.0", "vulns": []}, {"name": "pip-requirements-parser", "version": "32.0.1", "vulns": []}, {"name": "platformdirs", "version": "4.3.8", "vulns": []}, {"name": "pluggy", "version": "1.6.0", "vulns": []}, {"name": "posthog", "version": "5.4.0", "vulns": []}, {"name": "pre-commit", "version": "4.3.0", "vulns": []}, {"name": "propcache", "version": "0.3.2", "vulns": []}, {"name": "protobuf", "version": "6.31.1", "vulns": []}, {"name": "psutil", "version": "6.0.0", "vulns": []}, {"name": "py-serializable", "version": "2.1.0", "vulns": []}, {"name": "pyasn1", "version": "0.6.1", "vulns": []}, {"name": "pyasn1-modules", "version": "0.4.2", "vulns": []}, {"name": "pybase64", "version": "1.4.2", "vulns": []}, {"name": "pyc<PERSON><PERSON>", "version": "2.22", "vulns": []}, {"name": "pydantic", "version": "2.11.7", "vulns": []}, {"name": "pydantic-core", "version": "2.33.2", "vulns": []}, {"name": "pydantic-settings", "version": "2.10.1", "vulns": []}, {"name": "p<PERSON><PERSON><PERSON><PERSON>", "version": "2.7.0", "vulns": []}, {"name": "pygments", "version": "2.19.2", "vulns": []}, {"name": "pyjwt", "version": "2.10.1", "vulns": []}, {"name": "pymupdf", "version": "1.26.3", "vulns": []}, {"name": "pynacl", "version": "1.5.0", "vulns": []}, {"name": "pyparsing", "version": "3.2.3", "vulns": []}, {"name": "pypdf", "version": "5.9.0", "vulns": []}, {"name": "pypdfium2", "version": "4.30.0", "vulns": []}, {"name": "pypika", "version": "0.48.9", "vulns": []}, {"name": "pyproject-hooks", "version": "1.2.0", "vulns": []}, {"name": "pytest", "version": "8.4.1", "vulns": []}, {"name": "pytest-asyncio", "version": "1.1.0", "vulns": []}, {"name": "pytest-cov", "version": "6.2.1", "vulns": []}, {"name": "pytest-mock", "version": "3.14.1", "vulns": []}, {"name": "python-dateutil", "version": "2.9.0.post0", "vulns": []}, {"name": "python-dotenv", "version": "1.1.1", "vulns": []}, {"name": "pytz", "version": "2025.2", "vulns": []}, {"name": "pyyaml", "version": "6.0.2", "vulns": []}, {"name": "referencing", "version": "0.36.2", "vulns": []}, {"name": "regex", "version": "2025.7.34", "vulns": []}, {"name": "requests", "version": "2.32.4", "vulns": []}, {"name": "requests-o<PERSON><PERSON><PERSON>", "version": "2.0.0", "vulns": []}, {"name": "requests-toolbelt", "version": "1.0.0", "vulns": []}, {"name": "rich", "version": "14.1.0", "vulns": []}, {"name": "rpds-py", "version": "0.27.0", "vulns": []}, {"name": "rsa", "version": "4.9.1", "vulns": []}, {"name": "ruamel-yaml", "version": "0.18.14", "vulns": []}, {"name": "ruamel-yaml-clib", "version": "0.2.12", "vulns": []}, {"name": "safetensors", "version": "0.6.2", "vulns": []}, {"name": "safety", "version": "3.2.9", "vulns": []}, {"name": "safety-schemas", "version": "0.0.5", "vulns": []}, {"name": "setuptools", "version": "80.9.0", "vulns": []}, {"name": "shellingham", "version": "1.5.4", "vulns": []}, {"name": "six", "version": "1.17.0", "vulns": []}, {"name": "smmap", "version": "5.0.2", "vulns": []}, {"name": "sniffio", "version": "1.3.1", "vulns": []}, {"name": "sortedcontainers", "version": "2.4.0", "vulns": []}, {"name": "soupsieve", "version": "2.7", "vulns": []}, {"name": "sqlalchemy", "version": "2.0.42", "vulns": []}, {"name": "starlette", "version": "0.47.2", "vulns": []}, {"name": "s<PERSON><PERSON><PERSON>", "version": "5.4.1", "vulns": []}, {"name": "striprtf", "version": "0.0.26", "vulns": []}, {"name": "sympy", "version": "1.14.0", "vulns": []}, {"name": "tenacity", "version": "9.1.2", "vulns": []}, {"name": "tiktoken", "version": "0.11.0", "vulns": []}, {"name": "tokenizers", "version": "0.21.4", "vulns": []}, {"name": "toml", "version": "0.10.2", "vulns": []}, {"name": "tqdm", "version": "4.67.1", "vulns": []}, {"name": "transformers", "version": "4.55.0", "vulns": []}, {"name": "typer", "version": "0.16.0", "vulns": []}, {"name": "typing-extensions", "version": "4.14.1", "vulns": []}, {"name": "typing-inspect", "version": "0.9.0", "vulns": []}, {"name": "typing-inspection", "version": "0.4.1", "vulns": []}, {"name": "tzdata", "version": "2025.2", "vulns": []}, {"name": "urllib3", "version": "2.5.0", "vulns": []}, {"name": "u<PERSON><PERSON>", "version": "0.35.0", "vulns": []}, {"name": "uvloop", "version": "0.21.0", "vulns": []}, {"name": "validators", "version": "0.35.0", "vulns": []}, {"name": "virtualenv", "version": "20.33.1", "vulns": []}, {"name": "watchfiles", "version": "1.1.0", "vulns": []}, {"name": "weaviate-client", "version": "4.16.6", "vulns": []}, {"name": "websocket-client", "version": "1.8.0", "vulns": []}, {"name": "websockets", "version": "15.0.1", "vulns": []}, {"name": "wrapt", "version": "1.17.2", "vulns": []}, {"name": "yarl", "version": "1.20.1", "vulns": []}, {"name": "zipp", "version": "3.23.0", "vulns": []}, {"name": "zstandard", "version": "0.23.0", "vulns": []}], "fixes": []}