# LLM RAG Codebase Query System - Environment Configuration Template
# Copy this file to .env and fill in your actual values

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# Frontend Configuration
FRONTEND_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:8000

# =============================================================================
# LLM PROVIDERS
# =============================================================================
# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-5
OPENAI_TEMPERATURE=0.1
OPENAI_MAX_TOKENS=4000

# Alternative LLM Providers (uncomment as needed)
# ANTHROPIC_API_KEY=your_anthropic_api_key_here
# COHERE_API_KEY=your_cohere_api_key_here
# HUGGINGFACE_API_KEY=your_huggingface_api_key_here

# =============================================================================
# VECTOR DATABASE CONFIGURATION
# =============================================================================
# ChromaDB Configuration
CHROMA_HOST=localhost
CHROMA_PORT=8001
CHROMA_COLLECTION_NAME=codebase_embeddings

# Alternative Vector DB (uncomment as needed)
# WEAVIATE_URL=http://localhost:8080
# WEAVIATE_API_KEY=your_weaviate_api_key_here

# =============================================================================
# EMBEDDING CONFIGURATION
# =============================================================================
EMBEDDING_MODEL=text-embedding-ada-002
EMBEDDING_DIMENSION=1536
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# =============================================================================
# GITHUB INTEGRATION
# =============================================================================
GITHUB_TOKEN=your_github_token_here
GITHUB_API_URL=https://api.github.com

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0
REDIS_PASSWORD=
REDIS_TTL=3600

# =============================================================================
# DATABASE CONFIGURATION (if using SQL database)
# =============================================================================
# DATABASE_URL=postgresql://user:password@localhost:5432/ragdb
# DATABASE_POOL_SIZE=10
# DATABASE_MAX_OVERFLOW=20

# =============================================================================
# SECURITY SETTINGS
# =============================================================================
SECRET_KEY=your_secret_key_here_change_in_production
JWT_SECRET=your_jwt_secret_here_change_in_production
JWT_EXPIRATION=3600
CORS_ORIGINS=["http://localhost:3000"]

# =============================================================================
# FILE PROCESSING
# =============================================================================
MAX_FILE_SIZE=10485760  # 10MB in bytes
ALLOWED_FILE_TYPES=["py", "js", "ts", "md", "txt", "json", "yaml", "yml"]
TEMP_DIR=/tmp/rag_processing

# =============================================================================
# MONITORING & OBSERVABILITY
# =============================================================================
ENABLE_METRICS=true
METRICS_PORT=9090
SENTRY_DSN=your_sentry_dsn_here

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60  # seconds

# =============================================================================
# DEVELOPMENT SETTINGS
# =============================================================================
# Set to true to enable development features
DEV_MODE=true
RELOAD_ON_CHANGE=true

# =============================================================================
# PRODUCTION SETTINGS (override in .env.production)
# =============================================================================
# ENVIRONMENT=production
# DEBUG=false
# API_WORKERS=4
# LOG_LEVEL=WARNING
