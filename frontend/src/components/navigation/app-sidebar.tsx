'use client'

import * as React from 'react'

import type { NavItems } from './types'

import { Search, Zap } from 'lucide-react'

import { Input } from '@/components/ui/input'
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarRail,
  useSidebar,
} from '@/components/ui/sidebar'

import { NavListItem } from './nav-list-item'
import { NavUser } from './nav-user'

// TODO: This should not be hardcoded
const user = {
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: '/avatars/shadcn.jpg',
}

interface AppSidebarProps extends React.ComponentProps<typeof Sidebar> {
  navItems: NavItems[]
}

export function AppSidebar({ navItems, ...props }: AppSidebarProps) {
  const { state } = useSidebar()
  const isCollapsed = state === 'collapsed'

  // Group navigation items by their group property
  const groupedItems = React.useMemo(() => {
    const groups = new Map<string, NavItems[]>()
    navItems.forEach((item) => {
      const group = item.group || 'General'
      if (!groups.has(group)) {
        groups.set(group, [])
      }
      groups.get(group)!.push(item)
    })
    return groups
  }, [navItems])

  return (
    <Sidebar collapsible='icon' {...props}>
      <SidebarHeader>
        <div className='flex items-center gap-2 px-2 py-2'>
          <div className='flex min-w-0 items-center gap-2'>
            <div className='bg-primary flex h-8 w-8 shrink-0 items-center justify-center rounded-lg'>
              <Zap className='text-primary-foreground h-4 w-4' />
            </div>
            {!isCollapsed && (
              <div className='flex min-w-0 flex-col'>
                <span className='text-sidebar-foreground truncate text-sm font-semibold'>
                  Ultimate Electrical Designer
                </span>
                <span className='text-sidebar-foreground/60 truncate text-xs'>
                  v1.0.0
                </span>
              </div>
            )}
          </div>
        </div>
        {!isCollapsed && (
          <div className='px-2 pb-2'>
            <div className='relative'>
              <Search className='text-sidebar-foreground/50 absolute top-1/2 left-2 h-4 w-4 -translate-y-1/2' />
              <Input
                placeholder='Search...'
                className='bg-sidebar-accent/50 border-sidebar-border focus:bg-background h-8 pl-8'
              />
            </div>
          </div>
        )}
      </SidebarHeader>
      <SidebarContent>
        {Array.from(groupedItems.entries()).map(([groupName, items]) => (
          <SidebarGroup key={groupName}>
            <SidebarGroupLabel>{groupName}</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                {items.map((item) => (
                  <NavListItem key={item.path} item={item} isCollapsed={isCollapsed} />
                ))}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  )
}
