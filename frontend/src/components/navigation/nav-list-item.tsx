'use client'

import * as React from 'react'

import type { LucideIcon } from 'lucide-react'
import type { NavItems } from './types'

import {
  Bug,
  LayoutDashboard,
  Logs,
  MessageSquareWarning,
  Network,
  Settings,
} from 'lucide-react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'

import { Badge } from '@/components/ui/badge'
import { SidebarMenuButton, SidebarMenuItem } from '@/components/ui/sidebar'

const iconMap: Record<string, LucideIcon> = {
  LayoutDashboard,
  Logs,
  Bug,
  Network,
  MessageSquareWarning,
  Settings,
}

interface NavListItemProps {
  item: NavItems
  isCollapsed: boolean
}

export function NavListItem({ item, isCollapsed }: NavListItemProps) {
  const pathname = usePathname()
  const isActive = pathname === item.path
  const IconComponent = iconMap[item.icon]

  return (
    <SidebarMenuItem>
      <SidebarMenuButton
        asChild
        isActive={isActive}
        tooltip={isCollapsed ? item.name : undefined}
      >
        <Link href={item.path} className='flex items-center gap-3'>
          {IconComponent && <IconComponent className='h-4 w-4' />}
          {!isCollapsed && (
            <>
              <span className='flex-1'>{item.name}</span>
              {item.badge && (
                <Badge
                  type='generic'
                  intent='info'
                  size='sm'
                  className='h-5 px-1.5 text-xs'
                >
                  {item.badge}
                </Badge>
              )}
            </>
          )}
        </Link>
      </SidebarMenuButton>
    </SidebarMenuItem>
  )
}
