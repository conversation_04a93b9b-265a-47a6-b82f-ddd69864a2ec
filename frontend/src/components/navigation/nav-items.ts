import type { NavItems } from "./types"

export const NavItemss: NavItems[] = [
  {
    name: "Dashboard",
    path: "/app/dashboard",
    icon: "LayoutDashboard",
    group: "Overview",
    badge: "Home",
  },
  {
    name: "System Logs",
    path: "/app/logs",
    icon: "Logs",
    group: "Diagnostics",
    badge: "12",
  },
  {
    name: "Component Diagnostics",
    path: "/app/diagnostics",
    icon: "Bug",
    group: "Diagnostics",
    badge: "3",
  },
  {
    name: "Network Status",
    path: "/app/network",
    icon: "Network",
    group: "Monitoring",
    badge: "OK",
  },
  {
    name: "Error Reporting",
    path: "/app/errors",
    icon: "MessageSquareWarning",
    group: "Monitoring",
    badge: "2",
  },
  {
    name: "Settings",
    path: "/app/settings",
    icon: "Settings",
    group: "Configuration",
  },
]
