/**
 * @file Navigation types test
 * @description Tests for navigation type definitions
 */

import { describe, expect, it } from "vitest"
import type { NavItems } from "../types"

describe("Navigation Types", () => {
  describe("NavItems interface", () => {
    it("should define required navigation properties", () => {
      const navItem: NavItems = {
        name: "Dashboard",
        path: "/app/dashboard",
        icon: "LayoutDashboard",
      }

      expect(navItem.name).toBe("Dashboard")
      expect(navItem.path).toBe("/app/dashboard")
      expect(navItem.icon).toBe("LayoutDashboard")
    })

    it("should allow optional properties", () => {
      const navItem: NavItems = {
        name: "System Logs",
        path: "/app/logs",
        icon: "Logs",
        group: "Diagnostics",
        badge: "12",
      }

      expect(navItem.group).toBe("Diagnostics")
      expect(navItem.badge).toBe("12")
    })

    it("should work without optional properties", () => {
      const navItem: NavItems = {
        name: "Settings",
        path: "/app/settings",
        icon: "Settings",
      }

      expect(navItem.group).toBeUndefined()
      expect(navItem.badge).toBeUndefined()
    })
  })
})
