'use client'

import * as React from 'react'

import { ThemeProvider } from '@/providers/theme-provider'
import { ActiveThemeProvider } from '@/utils/active-theme'

import { LayoutProvider } from '@/hooks/useLayout'

import { SidebarProvider } from '@/components/ui/sidebar'

export function Providers({ children }: { children: React.ReactNode }) {

  return (
    <ThemeProvider>
      <LayoutProvider>
        <ActiveThemeProvider>
          <SidebarProvider>{children}</SidebarProvider>
        </ActiveThemeProvider>
      </LayoutProvider>
    </ThemeProvider>
  )
}
