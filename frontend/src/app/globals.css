@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  /* --color-white */
  --foreground: oklch(0.141 0.005 285.823);
  /* --color-zinc-950 */
  --card: oklch(1 0 0);
  /* --color-white */
  --card-foreground: oklch(0.141 0.005 285.823);
  /* --color-zinc-950 */
  --popover: oklch(1 0 0);
  /* --color-white */
  --popover-foreground: oklch(0.141 0.005 285.823);
  /* --color-zinc-950 */
  --primary: oklch(0.21 0.006 285.885);
  /* --color-zinc-900 */
  --primary-foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --secondary: oklch(0.967 0.001 286.375);
  /* --color-zinc-100 */
  --secondary-foreground: oklch(0.21 0.006 285.885);
  /* --color-zinc-900 */
  --muted: oklch(0.967 0.001 286.375);
  /* --color-zinc-100 */
  --muted-foreground: oklch(0.552 0.016 285.938);
  /* --color-zinc-500 */
  --accent: oklch(0.967 0.001 286.375);
  /* --color-zinc-100 */
  --accent-foreground: oklch(0.21 0.006 285.885);
  /* --color-zinc-900 */
  --destructive: oklch(0.637 0.237 25.331);
  /* --color-red-500 */
  --destructive-foreground: oklch(0.637 0.237 25.331);
  /* --color-red-500 */
  --border: oklch(0.92 0.004 286.32);
  /* --color-zinc-200 */
  --input: oklch(0.871 0.006 286.286);
  /* --color-zinc-300 */
  --ring: oklch(0.871 0.006 286.286);
  /* --color-zinc-300 */
  --chart-1: oklch(0.646 0.222 41.116);
  /* --color-orange-600 */
  --chart-2: oklch(0.6 0.118 184.704);
  /* --color-teal-600 */
  --chart-3: oklch(0.398 0.07 227.392);
  /* --color-cyan-900 */
  --chart-4: oklch(0.828 0.189 84.429);
  /* --color-amber-400 */
  --chart-5: oklch(0.769 0.188 70.08);
  /* --color-amber-500 */
  --sidebar: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --sidebar-foreground: oklch(0.141 0.005 285.823);
  /* --color-zinc-950 */
  --sidebar-primary: oklch(0.21 0.006 285.885);
  /* --color-zinc-900 */
  --sidebar-primary-foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --sidebar-accent: oklch(0.967 0.001 286.375);
  /* --color-zinc-100 */
  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);
  /* --color-zinc-900 */
  --sidebar-border: oklch(0.92 0.004 286.32);
  /* --color-zinc-200 */
  --sidebar-ring: oklch(0.871 0.006 286.286);
  /* --color-zinc-300 */
}

.dark {
  --background: oklch(0.141 0.005 285.823);
  /* --color-zinc-950 */
  --foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --card: oklch(0.141 0.005 285.823);
  /* --color-zinc-950 */
  --card-foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --popover: oklch(0.141 0.005 285.823);
  /* --color-zinc-950 */
  --popover-foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --primary: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --primary-foreground: oklch(0.21 0.006 285.885);
  /* --color-zinc-900 */
  --secondary: oklch(0.274 0.006 286.033);
  /* --color-zinc-800 */
  --secondary-foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --muted: oklch(0.21 0.006 285.885);
  /* --color-zinc-900 */
  --muted-foreground: oklch(0.65 0.01 286);
  /* 🔥 near --color-zinc-400 */
  --accent: oklch(0.21 0.006 285.885);
  /* --color-zinc-900 */
  --accent-foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --destructive: oklch(0.396 0.141 25.723);
  /* --color-red-900 */
  --destructive-foreground: oklch(0.637 0.237 25.331);
  /* --color-red-500 */
  --border: oklch(0.274 0.006 286.033);
  /* --color-zinc-800 */
  --input: oklch(0.274 0.006 286.033);
  /* --color-zinc-800 */
  --ring: oklch(0.442 0.017 285.786);
  /* --color-zinc-600 */
  --chart-1: oklch(0.488 0.243 264.376);
  /* --color-blue-700 */
  --chart-2: oklch(0.696 0.17 162.48);
  /* --color-emerald-500 */
  --chart-3: oklch(0.769 0.188 70.08);
  /* --color-amber-500 */
  --chart-4: oklch(0.627 0.265 303.9);
  /* --color-purple-500 */
  --chart-5: oklch(0.645 0.246 16.439);
  /* --color-rose-500 */
  --sidebar: oklch(0.205 0 0);
  /* --color-neutral-900 */
  --sidebar-foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --sidebar-primary: oklch(0.488 0.243 264.376);
  /* --color-blue-700 */
  --sidebar-primary-foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --sidebar-accent: oklch(0.269 0 0);
  /* --color-neutral-800 */
  --sidebar-accent-foreground: oklch(0.985 0 0);
  /* --color-zinc-50 */
  --sidebar-border: oklch(0.274 0.006 286.033);
  /* --color-zinc-800 */
  --sidebar-ring: oklch(0.442 0.017 285.786);
  /* --color-zinc-600 */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* originui css below */

@theme inline {
  --font-heading: var(--font-heading);
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono",
    "Courier New", monospace;
}

@layer base {

  /* Custom scrollbar styling */
  pre::-webkit-scrollbar {
    width: 5px;
  }

  pre::-webkit-scrollbar-track {
    background: transparent;
  }

  pre::-webkit-scrollbar-thumb {
    background: var(--border);
    border-radius: 5px;
  }

  pre {
    scrollbar-width: thin;
    scrollbar-color: var(--border) transparent;
  }
}