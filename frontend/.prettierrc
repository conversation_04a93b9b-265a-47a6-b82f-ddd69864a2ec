{"semi": false, "trailingComma": "es5", "singleQuote": true, "tabWidth": 2, "useTabs": false, "printWidth": 88, "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "always", "endOfLine": "lf", "quoteProps": "as-needed", "jsxSingleQuote": true, "plugins": ["prettier-plugin-tailwindcss"], "overrides": [{"files": "*.md", "options": {"printWidth": 100, "proseWrap": "always"}}, {"files": "*.json", "options": {"printWidth": 120}}]}