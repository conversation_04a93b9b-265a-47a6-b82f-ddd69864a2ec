#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to automatically fix B904 violations by adding 'from e' to raise statements.
"""

import json
import subprocess
import sys
from pathlib import Path


def get_b904_violations():
    """Get all B904 violations from ruff."""
    result = subprocess.run(
        ["uv", "run", "ruff", "check", "--select", "B904", "--output-format", "json"],
        capture_output=True,
        text=True,
        cwd="/home/<USER>/dev/codebase-query"
    )

    if result.returncode == 0:
        return []  # No violations

    try:
        violations = json.loads(result.stdout)
        return violations
    except json.JSONDecodeError:
        print(f"Error parsing ruff output: {result.stdout}")
        return []


def fix_violation(violation):
    """Fix a single B904 violation by adding 'from e' to the raise statement."""
    filename = violation["filename"]
    start_line = violation["location"]["row"]
    end_line = violation["end_location"]["row"]

    # Read the file
    with open(filename, 'r') as f:
        lines = f.readlines()

    # Find the last line of the raise statement (which should end with ')')
    for i in range(end_line - 1, start_line - 2, -1):
        line = lines[i]
        if line.strip().endswith(')'):
            # Check if it's part of a raise statement by looking backwards
            is_raise_statement = False
            for j in range(i, start_line - 2, -1):
                if 'raise ' in lines[j]:
                    is_raise_statement = True
                    break

            if is_raise_statement:
                # Add 'from e' before the newline, but need to determine the exception variable
                # Look for 'except SomeException as var:' pattern
                exception_var = 'e'  # default
                for j in range(start_line - 1, -1, -1):
                    if 'except ' in lines[j] and ' as ' in lines[j]:
                        parts = lines[j].split(' as ')
                        if len(parts) > 1:
                            exception_var = parts[1].split(':')[0].strip()
                            break

                lines[i] = line.rstrip() + f' from {exception_var}\n'
                break

    # Write the file back
    with open(filename, 'w') as f:
        f.writelines(lines)

    print(f"Fixed violation in {filename} at line {start_line}")


def main():
    """Main function to fix all B904 violations."""
    violations = get_b904_violations()

    if not violations:
        print("No B904 violations found!")
        return

    print(f"Found {len(violations)} B904 violations")

    for violation in violations:
        try:
            fix_violation(violation)
        except Exception as e:
            print(f"Error fixing violation: {e}")
            continue

    # Check if all violations are fixed
    remaining_violations = get_b904_violations()
    print(f"Remaining violations: {len(remaining_violations)}")


if __name__ == "__main__":
    main()
