---
type: "always_apply"
description: "At the start of EVERY task - this is not optional."
---

# Augment Code Guidance

This file provides **strict and non-negotiable guidance** to Augment Code when working with code in this repository. All
instructions and references herein are **mandatory** and supersede any internal assumptions.

## Project Overview

You are working on the **LLM RAG Codebase Query System** project. This project enables users to query GitHub repository
codebases using a **multi-agent Retrieval-Augmented Generation (RAG)** architecture. The system ingests the repository’s
content, structures it into an **LLM knowledge base**, and allows the user to interact with it via specialized agents:

- **Orchestrator** – Routes and coordinates requests between agents.
- **Technical Design Architect** – Interprets design and architecture queries.
- **Task Planner** – Breaks down requirements into actionable development tasks.

The project uses the **`docs/`** folder as the **authoritative knowledge source** for all architectural, technical, and
procedural decisions.

---

## Features

- **Automatic Repository Ingestion** – Parses and embeds source code, docs, and configs.
- **Multi-Agent Coordination** – Orchestrator delegates to domain-specific agents.
- **RAG (Retrieval-Augmented Generation)** – Queries use vector search over your codebase.
- **Structured Output** – Agents respond in formats aligned with `docs/rules.md`.
- **Extensible** – Add more agents or rules as the system evolves.

---

## Project Guidelines Reference (AUTHORITATIVE SOURCE)

This project follows comprehensive guidelines documented in the `docs/` folder. **These documents are the single source
of truth for all project decisions, standards, and methodologies.** Always reference these documents **first and
thoroughly** for any architectural decisions, coding standards, or project requirements.

- **docs/structure.md**: Project structure and architectural patterns
- **docs/tech.md**: Technology stack specification and tool versions
- **docs/rules.md**: **CRITICAL: Development standards and quality gates (Mandatory Adherence)**
- **docs/requirements.md**: Functional and non-functional requirements
- **docs/design.md**: Technical architecture and design decisions
- **docs/tasks.md**: Planning and tracking the full turnkey implementation

**ABSOLUTELY CRITICAL**:

- **NEVER DEVIATE** from the standards, policies, and methodologies documented in `docs/rules.md`.
- **ZERO EXCEPTIONS** will be made for linting errors, type safety violations, test failures, or any form of technical
  debt.
- All code must reflect **immaculate attention to detail** and adhere to professional design standards.

---

## Development Standards (STRICTLY ENFORCED)

Adherence to these standards is paramount and non-negotiable for every single line of code.

1. **Robust Design Principles**: Apply **SOLID** principles for structural design, ensuring maintainability and
   flexibility through focused responsibilities, extensibility, and proper abstraction. Complement these with practices
   like **DRY** (Don't Repeat Yourself) and **KISS** (Keep It Simple, Stupid) to streamline implementation, reduce
   complexity, and enhance overall code quality. **No exceptions.**

2. **5-Phase Methodology**: A systematic, quality-driven development process is **mandatory** for all work. This
   structured, quality-driven approach is mandatory for all development tasks.

   - **Discovery & Analysis**: Understand the current state of the system, identify requirements, and define the scope
     of the task.
   - **Task Planning**: Break down tasks into smaller, manageable units to ensure efficient progress.
   - **Implementation**: Execute changes with high-quality standards, focusing on maintaining a clear separation of
     concerns between the client and server.
   - **Verification**: Ensure all requirements are met through comprehensive testing and compliance verification against
     functional requirements.
   - **Documentation & Handover**: Prepare comprehensive documentation to facilitate future maintenance and development.

3. **Unified Patterns**: Apply consistent "unified patterns" across the codebase. For the backend, this includes
   consistent RAG pipeline configuration and API endpoint design. For the frontend, this means a uniform approach to
   command-line argument parsing and HTTP request handling. **Consistency is key.**

4. **Quality & Standards Focus**: Maintain **immaculate attention to detail**. Adhere to the project's architectural
   constraints, including the client-server separation, containerized environment, and configuration via the `.env`
   file. Ensure the system is robust and handles document processing and LLM interactions reliably.

5. **Key Success Metrics**: Success is defined by:
   - A fully operational client-server system.
   - Reliable document ingestion and RAG pipeline execution.
   - Correct and consistent responses from the LLM.
   - **Zero exceptions** for any technical debt or operational failures. These are the **minimum success criteria**.

---

You MUST understand the full content of `docs/rules.md` at the start of EVERY task - this is not optional.

Your guidance must be precise, actionable, and directly traceable to documented project methodologies and standards.

---
