{"meta": {"format": 3, "version": "7.10.2", "timestamp": "2025-08-11T17:39:00.383989", "branch_coverage": false, "show_contexts": false}, "files": {"src/__init__.py": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/agents/__init__.py": {"executed_lines": [1, 8, 17, 22, 29, 33, 38, 42, 43, 45], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 17, 22, 29, 33, 38, 42, 43, 45], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 17, 22, 29, 33, 38, 42, 43, 45], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/agents/base.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 23, 24, 26, 27, 28, 29, 32, 33, 35, 36, 37, 40, 41, 42, 44, 45, 46, 47, 48, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 64, 66, 67, 69, 71, 73, 75, 76, 77, 79, 80, 81, 82, 83, 85, 88, 89, 90, 92, 93, 94, 95, 96, 98, 100, 102, 104, 105, 118, 119, 120, 122, 123, 124, 125, 126, 127, 128, 130, 132, 133, 135, 137, 138, 140, 143, 144, 146, 147, 148, 149, 152, 160, 161, 165, 166, 170, 172, 173, 174, 181, 183, 184, 186, 187, 189, 192, 193, 197, 199, 201, 203, 211, 212, 214, 215], "summary": {"covered_lines": 104, "num_statements": 112, "percent_covered": 92.85714285714286, "percent_covered_display": "93", "missing_lines": 8, "excluded_lines": 10}, "missing_lines": [107, 108, 109, 110, 111, 113, 115, 175], "excluded_lines": [160, 161, 162, 163, 165, 166, 167, 168, 214, 215], "functions": {"ConversationContext.add_message": {"executed_lines": [66, 67], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConversationContext.get_recent_messages": {"executed_lines": [71], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConversationContext.get_context_summary": {"executed_lines": [75, 76, 77, 79, 80, 81, 82, 83, 85], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentContext.add_search_results": {"executed_lines": [100], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentContext.get_relevant_content": {"executed_lines": [104, 105], "summary": {"covered_lines": 2, "num_statements": 9, "percent_covered": 22.22222222222222, "percent_covered_display": "22", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [107, 108, 109, 110, 111, 113, 115], "excluded_lines": []}, "AgentResponse.add_source": {"executed_lines": [132, 133], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentResponse.format_sources": {"executed_lines": [137, 138, 140], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Agent.__init__": {"executed_lines": [147, 148, 149, 152], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Agent.process_query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [162, 163]}, "Agent.can_handle_query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [167, 168]}, "Agent._execute_with_timeout": {"executed_lines": [172, 173, 174], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [175], "excluded_lines": []}, "Agent._update_stats": {"executed_lines": [183, 184, 186, 187, 189, 192, 193], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Agent.get_stats": {"executed_lines": [199], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Agent.reset_stats": {"executed_lines": [203], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Agent.__str__": {"executed_lines": [212], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Agent.__repr__": {"executed_lines": [215], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [215]}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 23, 24, 26, 27, 28, 29, 32, 33, 35, 36, 37, 40, 41, 42, 44, 45, 46, 47, 48, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 64, 69, 73, 88, 89, 90, 92, 93, 94, 95, 96, 98, 102, 118, 119, 120, 122, 123, 124, 125, 126, 127, 128, 130, 135, 143, 144, 146, 160, 161, 165, 166, 170, 181, 197, 201, 211, 214], "summary": {"covered_lines": 67, "num_statements": 67, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 5}, "missing_lines": [], "excluded_lines": [160, 161, 165, 166, 214]}}, "classes": {"AgentType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MessageRole": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Message": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConversationContext": {"executed_lines": [66, 67, 71, 75, 76, 77, 79, 80, 81, 82, 83, 85], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentContext": {"executed_lines": [100, 104, 105], "summary": {"covered_lines": 3, "num_statements": 10, "percent_covered": 30.0, "percent_covered_display": "30", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [107, 108, 109, 110, 111, 113, 115], "excluded_lines": []}, "AgentResponse": {"executed_lines": [132, 133, 137, 138, 140], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Agent": {"executed_lines": [147, 148, 149, 152, 172, 173, 174, 183, 184, 186, 187, 189, 192, 193, 199, 203, 212, 215], "summary": {"covered_lines": 17, "num_statements": 18, "percent_covered": 94.44444444444444, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 5}, "missing_lines": [175], "excluded_lines": [162, 163, 167, 168, 215]}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 23, 24, 26, 27, 28, 29, 32, 33, 35, 36, 37, 40, 41, 42, 44, 45, 46, 47, 48, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 64, 69, 73, 88, 89, 90, 92, 93, 94, 95, 96, 98, 102, 118, 119, 120, 122, 123, 124, 125, 126, 127, 128, 130, 135, 143, 144, 146, 160, 161, 165, 166, 170, 181, 197, 201, 211, 214], "summary": {"covered_lines": 67, "num_statements": 67, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 5}, "missing_lines": [], "excluded_lines": [160, 161, 165, 166, 214]}}}, "src/agents/cache.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 23, 24, 25, 27, 28, 29, 30, 32, 33, 34, 37, 38, 40, 41, 45, 46, 50, 51, 55, 56, 60, 61, 66, 67, 69, 70, 71, 72, 80, 82, 83, 85, 86, 87, 88, 90, 91, 93, 96, 97, 99, 100, 103, 105, 107, 108, 109, 111, 113, 115, 117, 118, 120, 127, 129, 130, 133, 134, 135, 137, 139, 140, 141, 144, 145, 147, 148, 149, 150, 158, 160, 161, 162, 173, 174, 175, 176, 179, 181, 182, 208, 209, 210, 211, 213, 233, 243, 254, 256, 257, 259, 265, 266, 269, 270, 272, 275, 276, 278, 279, 280, 281, 282, 284, 285, 287, 289, 291, 292, 293, 294, 295, 297, 300, 309, 310, 312, 314, 315, 317, 320, 321, 322, 323, 330, 331, 332, 333, 335, 336, 338, 340, 341, 343, 344, 350, 356, 357, 359, 361, 363, 364, 365, 366, 368, 370], "summary": {"covered_lines": 142, "num_statements": 198, "percent_covered": 71.71717171717172, "percent_covered_display": "72", "missing_lines": 56, "excluded_lines": 20}, "missing_lines": [163, 171, 172, 177, 183, 184, 185, 187, 188, 190, 198, 199, 200, 202, 203, 205, 206, 215, 216, 219, 226, 227, 229, 230, 231, 235, 236, 237, 238, 239, 240, 241, 245, 246, 247, 248, 249, 250, 251, 252, 267, 268, 324, 325, 326, 327, 351, 352, 353, 354, 372, 373, 375, 376, 377, 378], "excluded_lines": [40, 41, 42, 43, 45, 46, 47, 48, 50, 51, 52, 53, 55, 56, 57, 58, 60, 61, 62, 63], "functions": {"CacheEntry.__post_init__": {"executed_lines": [33, 34], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheBackend.get": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [42, 43]}, "CacheBackend.set": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [47, 48]}, "CacheBackend.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [52, 53]}, "CacheBackend.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [57, 58]}, "CacheBackend.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [62, 63]}, "InMemoryCache.__init__": {"executed_lines": [70, 71, 72], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InMemoryCache.get": {"executed_lines": [82, 83, 85, 86, 87, 88, 90, 91], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InMemoryCache.set": {"executed_lines": [96, 97, 99, 100, 103], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InMemoryCache.delete": {"executed_lines": [107, 108, 109], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InMemoryCache.clear": {"executed_lines": [113], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InMemoryCache.get_stats": {"executed_lines": [117, 118, 120], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InMemoryCache._evict_lru": {"executed_lines": [129, 130, 133, 134, 135], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InMemoryCache._schedule_ttl_cleanup": {"executed_lines": [139, 140, 141], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RedisCache.__init__": {"executed_lines": [148, 149, 150], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RedisCache._get_redis": {"executed_lines": [160, 161, 162, 173, 174, 175, 176], "summary": {"covered_lines": 7, "num_statements": 11, "percent_covered": 63.63636363636363, "percent_covered_display": "64", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [163, 171, 172, 177], "excluded_lines": []}, "RedisCache.get": {"executed_lines": [181, 182, 208, 209, 210, 211], "summary": {"covered_lines": 6, "num_statements": 19, "percent_covered": 31.57894736842105, "percent_covered_display": "32", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [183, 184, 185, 187, 188, 190, 198, 199, 200, 202, 203, 205, 206], "excluded_lines": []}, "RedisCache.set": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [215, 216, 219, 226, 227, 229, 230, 231], "excluded_lines": []}, "RedisCache.delete": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [235, 236, 237, 238, 239, 240, 241], "excluded_lines": []}, "RedisCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [245, 246, 247, 248, 249, 250, 251, 252], "excluded_lines": []}, "RedisCache.get_stats": {"executed_lines": [256, 257, 259, 265, 266, 269, 270, 272], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [267, 268], "excluded_lines": []}, "LLMResponseCache.__init__": {"executed_lines": [279, 280, 281, 282, 284, 285], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMResponseCache._initialize_backends": {"executed_lines": [289, 291, 292, 293, 294, 295], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMResponseCache._generate_cache_key": {"executed_lines": [300, 309, 310], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMResponseCache.get": {"executed_lines": [314, 315, 317, 320, 321, 322, 323, 330, 331, 332, 333, 335, 336], "summary": {"covered_lines": 13, "num_statements": 17, "percent_covered": 76.47058823529412, "percent_covered_display": "76", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [324, 325, 326, 327], "excluded_lines": []}, "LLMResponseCache.set": {"executed_lines": [340, 341, 343, 344, 350, 356, 357], "summary": {"covered_lines": 7, "num_statements": 11, "percent_covered": 63.63636363636363, "percent_covered_display": "64", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [351, 352, 353, 354], "excluded_lines": []}, "LLMResponseCache.get_stats": {"executed_lines": [361, 363, 364, 365, 366, 368], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMResponseCache.clear": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [372, 373, 375, 376, 377, 378], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 23, 24, 25, 27, 28, 29, 30, 32, 37, 38, 40, 41, 45, 46, 50, 51, 55, 56, 60, 61, 66, 67, 69, 80, 93, 105, 111, 115, 127, 137, 144, 145, 147, 158, 179, 213, 233, 243, 254, 275, 276, 278, 287, 297, 312, 338, 359, 370], "summary": {"covered_lines": 44, "num_statements": 44, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [40, 41, 45, 46, 50, 51, 55, 56, 60, 61]}}, "classes": {"CacheEntry": {"executed_lines": [33, 34], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CacheBackend": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [42, 43, 47, 48, 52, 53, 57, 58, 62, 63]}, "InMemoryCache": {"executed_lines": [70, 71, 72, 82, 83, 85, 86, 87, 88, 90, 91, 96, 97, 99, 100, 103, 107, 108, 109, 113, 117, 118, 120, 129, 130, 133, 134, 135, 139, 140, 141], "summary": {"covered_lines": 31, "num_statements": 31, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RedisCache": {"executed_lines": [148, 149, 150, 160, 161, 162, 173, 174, 175, 176, 181, 182, 208, 209, 210, 211, 256, 257, 259, 265, 266, 269, 270, 272], "summary": {"covered_lines": 24, "num_statements": 66, "percent_covered": 36.36363636363637, "percent_covered_display": "36", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [163, 171, 172, 177, 183, 184, 185, 187, 188, 190, 198, 199, 200, 202, 203, 205, 206, 215, 216, 219, 226, 227, 229, 230, 231, 235, 236, 237, 238, 239, 240, 241, 245, 246, 247, 248, 249, 250, 251, 252, 267, 268], "excluded_lines": []}, "LLMResponseCache": {"executed_lines": [279, 280, 281, 282, 284, 285, 289, 291, 292, 293, 294, 295, 300, 309, 310, 314, 315, 317, 320, 321, 322, 323, 330, 331, 332, 333, 335, 336, 340, 341, 343, 344, 350, 356, 357, 361, 363, 364, 365, 366, 368], "summary": {"covered_lines": 41, "num_statements": 55, "percent_covered": 74.54545454545455, "percent_covered_display": "75", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [324, 325, 326, 327, 351, 352, 353, 354, 372, 373, 375, 376, 377, 378], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 15, 17, 18, 20, 23, 24, 25, 27, 28, 29, 30, 32, 37, 38, 40, 41, 45, 46, 50, 51, 55, 56, 60, 61, 66, 67, 69, 80, 93, 105, 111, 115, 127, 137, 144, 145, 147, 158, 179, 213, 233, 243, 254, 275, 276, 278, 287, 297, 312, 338, 359, 370], "summary": {"covered_lines": 44, "num_statements": 44, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [40, 41, 45, 46, 50, 51, 55, 56, 60, 61]}}}, "src/agents/context.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 16, 17, 19, 22, 23, 25, 26, 30, 31, 35, 36, 40, 41, 45, 46, 51, 52, 54, 55, 56, 57, 59, 61, 62, 63, 64, 65, 66, 71, 73, 74, 75, 76, 77, 78, 83, 96, 107, 127, 129, 135, 136, 138, 139, 140, 141, 142, 144, 146, 147, 149, 150, 157, 158, 160, 162, 163, 164, 165, 167, 169, 170, 171, 174, 177, 179, 180, 183, 184, 191, 193, 217, 224, 226, 230, 231, 232, 233, 236, 237, 238, 242, 243, 245, 247, 248, 249, 250, 251, 253, 255, 256, 257, 259], "summary": {"covered_lines": 88, "num_statements": 148, "percent_covered": 59.45945945945946, "percent_covered_display": "59", "missing_lines": 60, "excluded_lines": 20}, "missing_lines": [67, 68, 69, 79, 80, 81, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 98, 99, 100, 101, 102, 103, 104, 105, 109, 110, 111, 113, 114, 115, 116, 118, 119, 121, 122, 123, 124, 125, 151, 185, 195, 196, 197, 203, 204, 206, 207, 208, 214, 215, 219, 220, 221, 222, 227, 228, 234, 235, 239, 240], "excluded_lines": [25, 26, 27, 28, 30, 31, 32, 33, 35, 36, 37, 38, 40, 41, 42, 43, 45, 46, 47, 48], "functions": {"ContextStorage.store_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [27, 28]}, "ContextStorage.retrieve_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [32, 33]}, "ContextStorage.delete_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [37, 38]}, "ContextStorage.list_active_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [42, 43]}, "ContextStorage.cleanup_expired_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [47, 48]}, "InMemoryContextStorage.__init__": {"executed_lines": [55, 56, 57], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "InMemoryContextStorage.store_context": {"executed_lines": [61, 62, 63, 64, 65, 66], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [67, 68, 69], "excluded_lines": []}, "InMemoryContextStorage.retrieve_context": {"executed_lines": [73, 74, 75, 76, 77, 78], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [79, 80, 81], "excluded_lines": []}, "InMemoryContextStorage.delete_context": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [85, 86, 87, 88, 89, 90, 91, 92, 93, 94], "excluded_lines": []}, "InMemoryContextStorage.list_active_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [98, 99, 100, 101, 102, 103, 104, 105], "excluded_lines": []}, "InMemoryContextStorage.cleanup_expired_sessions": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [109, 110, 111, 113, 114, 115, 116, 118, 119, 121, 122, 123, 124, 125], "excluded_lines": []}, "InMemoryContextStorage.get_stats": {"executed_lines": [129], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConversationContextManager.__init__": {"executed_lines": [139, 140, 141, 142], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConversationContextManager.create_session": {"executed_lines": [146, 147, 149, 150, 157, 158], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [151], "excluded_lines": []}, "ConversationContextManager.get_context": {"executed_lines": [162, 163, 164, 165], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConversationContextManager.add_message": {"executed_lines": [169, 170, 171, 174, 177, 179, 180, 183, 184, 191], "summary": {"covered_lines": 10, "num_statements": 11, "percent_covered": 90.9090909090909, "percent_covered_display": "91", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [185], "excluded_lines": []}, "ConversationContextManager.update_repository": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [195, 196, 197, 203, 204, 206, 207, 208, 214, 215], "excluded_lines": []}, "ConversationContextManager.delete_session": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [219, 220, 221, 222], "excluded_lines": []}, "ConversationContextManager.start_cleanup_task": {"executed_lines": [226, 230, 242, 243], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [227, 228], "excluded_lines": []}, "ConversationContextManager.start_cleanup_task.cleanup_loop": {"executed_lines": [231, 232, 233, 236, 237, 238], "summary": {"covered_lines": 6, "num_statements": 10, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [234, 235, 239, 240], "excluded_lines": []}, "ConversationContextManager.stop_cleanup_task": {"executed_lines": [247, 248, 249, 250, 251], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConversationContextManager.get_stats": {"executed_lines": [255, 256, 257, 259], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 16, 17, 19, 22, 23, 25, 26, 30, 31, 35, 36, 40, 41, 45, 46, 51, 52, 54, 59, 71, 83, 96, 107, 127, 135, 136, 138, 144, 160, 167, 193, 217, 224, 245, 253], "summary": {"covered_lines": 29, "num_statements": 29, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [25, 26, 30, 31, 35, 36, 40, 41, 45, 46]}}, "classes": {"ContextStorage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [27, 28, 32, 33, 37, 38, 42, 43, 47, 48]}, "InMemoryContextStorage": {"executed_lines": [55, 56, 57, 61, 62, 63, 64, 65, 66, 73, 74, 75, 76, 77, 78, 129], "summary": {"covered_lines": 16, "num_statements": 54, "percent_covered": 29.62962962962963, "percent_covered_display": "30", "missing_lines": 38, "excluded_lines": 0}, "missing_lines": [67, 68, 69, 79, 80, 81, 85, 86, 87, 88, 89, 90, 91, 92, 93, 94, 98, 99, 100, 101, 102, 103, 104, 105, 109, 110, 111, 113, 114, 115, 116, 118, 119, 121, 122, 123, 124, 125], "excluded_lines": []}, "ConversationContextManager": {"executed_lines": [139, 140, 141, 142, 146, 147, 149, 150, 157, 158, 162, 163, 164, 165, 169, 170, 171, 174, 177, 179, 180, 183, 184, 191, 226, 230, 231, 232, 233, 236, 237, 238, 242, 243, 247, 248, 249, 250, 251, 255, 256, 257, 259], "summary": {"covered_lines": 43, "num_statements": 65, "percent_covered": 66.15384615384616, "percent_covered_display": "66", "missing_lines": 22, "excluded_lines": 0}, "missing_lines": [151, 185, 195, 196, 197, 203, 204, 206, 207, 208, 214, 215, 219, 220, 221, 222, 227, 228, 234, 235, 239, 240], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 14, 16, 17, 19, 22, 23, 25, 26, 30, 31, 35, 36, 40, 41, 45, 46, 51, 52, 54, 59, 71, 83, 96, 107, 127, 135, 136, 138, 144, 160, 167, 193, 217, 224, 245, 253], "summary": {"covered_lines": 29, "num_statements": 29, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [25, 26, 30, 31, 35, 36, 40, 41, 45, 46]}}}, "src/agents/exceptions.py": {"executed_lines": [1, 8, 11, 12, 14, 21, 22, 23, 24, 26, 28, 30, 31, 33, 34, 35, 37, 38, 40, 43, 44, 46, 53, 54, 56, 59, 62, 63, 65, 81, 82, 84, 92, 93, 94, 95, 97, 100, 103, 104, 106, 113, 114, 115, 116, 118, 119, 121, 124, 125, 127, 145, 146, 148, 155, 156, 157, 158, 161], "summary": {"covered_lines": 51, "num_statements": 71, "percent_covered": 71.83098591549296, "percent_covered_display": "72", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [55, 57, 72, 73, 74, 75, 76, 78, 96, 98, 117, 134, 135, 136, 137, 138, 139, 140, 142, 159], "excluded_lines": [], "functions": {"AgentError.__init__": {"executed_lines": [21, 22, 23, 24], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentError.__str__": {"executed_lines": [28, 30, 31, 33, 34, 35, 37, 38, 40], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentTimeoutError.__init__": {"executed_lines": [53, 54, 56, 59], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [55, 57], "excluded_lines": []}, "AgentConfigurationError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [72, 73, 74, 75, 76, 78], "excluded_lines": []}, "LLMClientError.__init__": {"executed_lines": [92, 93, 94, 95, 97, 100], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [96, 98], "excluded_lines": []}, "ContextError.__init__": {"executed_lines": [113, 114, 115, 116, 118, 119, 121], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [117], "excluded_lines": []}, "AgentRoutingError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [134, 135, 136, 137, 138, 139, 140, 142], "excluded_lines": []}, "AgentResponseError.__init__": {"executed_lines": [155, 156, 157, 158, 161], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [159], "excluded_lines": []}, "": {"executed_lines": [1, 8, 11, 12, 14, 26, 43, 44, 46, 62, 63, 65, 81, 82, 84, 103, 104, 106, 124, 125, 127, 145, 146, 148], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"AgentError": {"executed_lines": [21, 22, 23, 24, 28, 30, 31, 33, 34, 35, 37, 38, 40], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentTimeoutError": {"executed_lines": [53, 54, 56, 59], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [55, 57], "excluded_lines": []}, "AgentConfigurationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [72, 73, 74, 75, 76, 78], "excluded_lines": []}, "LLMClientError": {"executed_lines": [92, 93, 94, 95, 97, 100], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [96, 98], "excluded_lines": []}, "ContextError": {"executed_lines": [113, 114, 115, 116, 118, 119, 121], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [117], "excluded_lines": []}, "AgentRoutingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [134, 135, 136, 137, 138, 139, 140, 142], "excluded_lines": []}, "AgentResponseError": {"executed_lines": [155, 156, 157, 158, 161], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [159], "excluded_lines": []}, "": {"executed_lines": [1, 8, 11, 12, 14, 26, 43, 44, 46, 62, 63, 65, 81, 82, 84, 103, 104, 106, 124, 125, 127, 145, 146, 148], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/agents/factory.py": {"executed_lines": [1, 8, 9, 11, 12, 13, 14, 15, 16, 17, 19, 22, 23, 25, 26, 29, 30, 31, 32, 34, 36, 38, 39, 40, 42, 50, 51, 53, 60, 61, 63, 69, 70, 72, 93, 95, 138, 140, 142, 143, 148, 150, 152, 153, 157, 159, 170, 171, 173, 174, 175, 176, 178, 180, 181, 182, 184, 186, 190, 198, 203, 205, 206, 207, 209], "summary": {"covered_lines": 62, "num_statements": 80, "percent_covered": 77.5, "percent_covered_display": "78", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [78, 80, 81, 86, 87, 144, 145, 146, 154, 155, 188, 192, 193, 194, 195, 196, 200, 201], "excluded_lines": [], "functions": {"AgentFactory.__init__": {"executed_lines": [26, 29, 30, 31, 32, 34], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentFactory.create_agent": {"executed_lines": [38, 39, 40, 42, 50, 51, 53, 60, 61, 63, 69, 70, 72], "summary": {"covered_lines": 13, "num_statements": 18, "percent_covered": 72.22222222222223, "percent_covered_display": "72", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [78, 80, 81, 86, 87], "excluded_lines": []}, "AgentFactory.get_available_agents": {"executed_lines": [95], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentFactory.initialize_shared_services": {"executed_lines": [140, 142, 143], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [144, 145, 146], "excluded_lines": []}, "AgentFactory.shutdown_shared_services": {"executed_lines": [150, 152, 153], "summary": {"covered_lines": 3, "num_statements": 5, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [154, 155], "excluded_lines": []}, "AgentFactory.get_stats": {"executed_lines": [159], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentRegistry.__init__": {"executed_lines": [174, 175, 176], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentRegistry.get_agent": {"executed_lines": [180, 181, 182, 184], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentRegistry.get_all_agents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [188], "excluded_lines": []}, "AgentRegistry.remove_agent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [192, 193, 194, 195, 196], "excluded_lines": []}, "AgentRegistry.clear_all_agents": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [200, 201], "excluded_lines": []}, "AgentRegistry.get_registry_stats": {"executed_lines": [205, 206, 207, 209], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 12, 13, 14, 15, 16, 17, 19, 22, 23, 25, 36, 93, 138, 148, 157, 170, 171, 173, 178, 186, 190, 198, 203], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"AgentFactory": {"executed_lines": [26, 29, 30, 31, 32, 34, 38, 39, 40, 42, 50, 51, 53, 60, 61, 63, 69, 70, 72, 95, 140, 142, 143, 150, 152, 153, 159], "summary": {"covered_lines": 27, "num_statements": 37, "percent_covered": 72.97297297297297, "percent_covered_display": "73", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [78, 80, 81, 86, 87, 144, 145, 146, 154, 155], "excluded_lines": []}, "AgentRegistry": {"executed_lines": [174, 175, 176, 180, 181, 182, 184, 205, 206, 207, 209], "summary": {"covered_lines": 11, "num_statements": 19, "percent_covered": 57.89473684210526, "percent_covered_display": "58", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [188, 192, 193, 194, 195, 196, 200, 201], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 12, 13, 14, 15, 16, 17, 19, 22, 23, 25, 36, 93, 138, 148, 157, 170, 171, 173, 178, 186, 190, 198, 203], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/agents/formatters.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 17, 20, 21, 22, 24, 25, 26, 27, 30, 31, 33, 34, 40, 41, 46, 47, 49, 50, 52, 88, 99, 112, 135, 141, 158, 179, 206, 207, 209, 210, 216, 217, 234, 235, 241, 242], "summary": {"covered_lines": 35, "num_statements": 140, "percent_covered": 25.0, "percent_covered_display": "25", "missing_lines": 105, "excluded_lines": 10}, "missing_lines": [56, 58, 61, 62, 63, 64, 67, 68, 69, 70, 72, 84, 85, 86, 90, 92, 102, 105, 108, 110, 114, 115, 117, 119, 121, 122, 123, 124, 126, 127, 129, 131, 133, 138, 143, 144, 146, 147, 149, 150, 151, 152, 154, 156, 160, 163, 164, 165, 166, 167, 168, 170, 171, 173, 174, 175, 177, 181, 182, 185, 186, 187, 188, 190, 191, 193, 194, 195, 196, 197, 198, 199, 201, 203, 212, 213, 214, 219, 220, 222, 223, 224, 225, 226, 228, 230, 232, 238, 239, 244, 245, 248, 251, 252, 254, 255, 258, 271, 272, 273, 274, 275, 276, 278, 280], "excluded_lines": [33, 34, 35, 36, 37, 38, 40, 41, 42, 43], "functions": {"ResponseFormatter.format_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [37, 38]}, "ResponseFormatter.format_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [42, 43]}, "MarkdownFormatter.__init__": {"executed_lines": [50], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MarkdownFormatter.format_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [56, 58, 61, 62, 63, 64, 67, 68, 69, 70, 72, 84, 85, 86], "excluded_lines": []}, "MarkdownFormatter.format_error": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [90, 92], "excluded_lines": []}, "MarkdownFormatter._format_main_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [102, 105, 108, 110], "excluded_lines": []}, "MarkdownFormatter._normalize_headings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [114, 115, 117, 119, 121, 122, 123, 124, 126, 127, 129, 131, 133], "excluded_lines": []}, "MarkdownFormatter._format_code_blocks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [138], "excluded_lines": []}, "MarkdownFormatter._format_lists": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [143, 144, 146, 147, 149, 150, 151, 152, 154, 156], "excluded_lines": []}, "MarkdownFormatter._format_citations": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [160, 163, 164, 165, 166, 167, 168, 170, 171, 173, 174, 175, 177], "excluded_lines": []}, "MarkdownFormatter._format_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 17, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 17, "excluded_lines": 0}, "missing_lines": [181, 182, 185, 186, 187, 188, 190, 191, 193, 194, 195, 196, 197, 198, 199, 201, 203], "excluded_lines": []}, "SourceCitationFormatter.format_inline_citation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [212, 213, 214], "excluded_lines": []}, "SourceCitationFormatter.format_reference_list": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [219, 220, 222, 223, 224, 225, 226, 228, 230, 232], "excluded_lines": []}, "SourceCitationFormatter.extract_citations_from_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [238, 239], "excluded_lines": []}, "SourceCitationFormatter.add_citations_to_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [244, 245, 248, 251, 252, 254, 255, 258, 271, 272, 273, 274, 275, 276, 278, 280], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 17, 20, 21, 22, 24, 25, 26, 27, 30, 31, 33, 34, 40, 41, 46, 47, 49, 52, 88, 99, 112, 135, 141, 158, 179, 206, 207, 209, 210, 216, 217, 234, 235, 241, 242], "summary": {"covered_lines": 34, "num_statements": 34, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 6}, "missing_lines": [], "excluded_lines": [33, 34, 35, 36, 40, 41]}}, "classes": {"FormattedResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResponseFormatter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 4}, "missing_lines": [], "excluded_lines": [37, 38, 42, 43]}, "MarkdownFormatter": {"executed_lines": [50], "summary": {"covered_lines": 1, "num_statements": 75, "percent_covered": 1.**************33, "percent_covered_display": "1", "missing_lines": 74, "excluded_lines": 0}, "missing_lines": [56, 58, 61, 62, 63, 64, 67, 68, 69, 70, 72, 84, 85, 86, 90, 92, 102, 105, 108, 110, 114, 115, 117, 119, 121, 122, 123, 124, 126, 127, 129, 131, 133, 138, 143, 144, 146, 147, 149, 150, 151, 152, 154, 156, 160, 163, 164, 165, 166, 167, 168, 170, 171, 173, 174, 175, 177, 181, 182, 185, 186, 187, 188, 190, 191, 193, 194, 195, 196, 197, 198, 199, 201, 203], "excluded_lines": []}, "SourceCitationFormatter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 31, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 31, "excluded_lines": 0}, "missing_lines": [212, 213, 214, 219, 220, 222, 223, 224, 225, 226, 228, 230, 232, 238, 239, 244, 245, 248, 251, 252, 254, 255, 258, 271, 272, 273, 274, 275, 276, 278, 280], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 17, 20, 21, 22, 24, 25, 26, 27, 30, 31, 33, 34, 40, 41, 46, 47, 49, 52, 88, 99, 112, 135, 141, 158, 179, 206, 207, 209, 210, 216, 217, 234, 235, 241, 242], "summary": {"covered_lines": 34, "num_statements": 34, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 6}, "missing_lines": [], "excluded_lines": [33, 34, 35, 36, 40, 41]}}}, "src/agents/llm_client.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 16, 17, 18, 19, 21, 25, 26, 28, 30, 31, 32, 33, 34, 37, 38, 41, 42, 45, 55, 57, 66, 68, 70, 71, 74, 80, 88, 104, 107, 110, 111, 112, 113, 116, 165, 166, 167, 169, 170, 172, 174, 175, 177, 181, 183, 185, 187, 197, 199, 201, 204, 205, 208, 211, 214, 216, 218, 231, 232, 234, 235, 239, 241, 242], "summary": {"covered_lines": 68, "num_statements": 92, "percent_covered": 73.91304347826087, "percent_covered_display": "74", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [90, 91, 93, 94, 97, 98, 99, 101, 124, 127, 130, 149, 159, 161, 163, 178, 179, 188, 189, 192, 195, 206, 220, 244], "excluded_lines": [], "functions": {"LLMClient.__init__": {"executed_lines": [30, 31, 32, 33, 34, 37, 38, 41, 42, 45, 55], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMClient.generate_response": {"executed_lines": [66, 68, 70, 71, 74, 80, 88, 104, 107, 110, 111, 112, 113, 116, 165, 166, 167, 169, 170], "summary": {"covered_lines": 19, "num_statements": 34, "percent_covered": 55.88235294117647, "percent_covered_display": "56", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [90, 91, 93, 94, 97, 98, 99, 101, 124, 127, 130, 149, 159, 161, 163], "excluded_lines": []}, "LLMClient._enforce_rate_limit": {"executed_lines": [174, 175, 177, 181], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [178, 179], "excluded_lines": []}, "LLMClient._update_stats": {"executed_lines": [185, 187, 197], "summary": {"covered_lines": 3, "num_statements": 7, "percent_covered": 42.857142857142854, "percent_covered_display": "43", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [188, 189, 192, 195], "excluded_lines": []}, "LLMClient.get_stats": {"executed_lines": [201, 204, 205, 208, 211, 214, 216], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [206], "excluded_lines": []}, "LLMClient.reset_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [220], "excluded_lines": []}, "LLMClientFactory.create_client": {"executed_lines": [239], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LLMClientFactory.get_available_providers": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [244], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 16, 17, 18, 19, 21, 25, 26, 28, 57, 172, 183, 199, 218, 231, 232, 234, 235, 241, 242], "summary": {"covered_lines": 23, "num_statements": 23, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"LLMClient": {"executed_lines": [30, 31, 32, 33, 34, 37, 38, 41, 42, 45, 55, 66, 68, 70, 71, 74, 80, 88, 104, 107, 110, 111, 112, 113, 116, 165, 166, 167, 169, 170, 174, 175, 177, 181, 185, 187, 197, 201, 204, 205, 208, 211, 214, 216], "summary": {"covered_lines": 44, "num_statements": 67, "percent_covered": 65.67164179104478, "percent_covered_display": "66", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [90, 91, 93, 94, 97, 98, 99, 101, 124, 127, 130, 149, 159, 161, 163, 178, 179, 188, 189, 192, 195, 206, 220], "excluded_lines": []}, "LLMClientFactory": {"executed_lines": [239], "summary": {"covered_lines": 1, "num_statements": 2, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [244], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 16, 17, 18, 19, 21, 25, 26, 28, 57, 172, 183, 199, 218, 231, 232, 234, 235, 241, 242], "summary": {"covered_lines": 23, "num_statements": 23, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/agents/model_selector.py": {"executed_lines": [1, 9, 10, 11, 12, 14, 16, 19, 20, 21, 23, 24, 25, 26, 29, 30, 32, 33, 34, 37, 82, 100, 102, 103, 104, 107, 108, 110, 111, 112, 115, 116, 117, 118, 121, 122, 123, 125, 126, 129, 130, 131, 132, 135, 142, 148, 151, 152, 156, 157, 162, 169, 191, 205, 206, 208, 209, 210, 213, 221, 223, 226, 228, 233, 235, 237, 238, 240, 243, 246, 248, 250, 252, 253, 258, 259, 260, 263, 265, 267, 279], "summary": {"covered_lines": 77, "num_statements": 110, "percent_covered": 70.0, "percent_covered_display": "70", "missing_lines": 33, "excluded_lines": 0}, "missing_lines": [133, 137, 138, 139, 143, 144, 145, 153, 154, 159, 160, 171, 174, 175, 178, 179, 180, 183, 184, 185, 186, 187, 189, 193, 241, 254, 255, 256, 269, 270, 273, 277, 281], "excluded_lines": [], "functions": {"QueryComplexityAnalyzer.__init__": {"executed_lines": [33, 34, 37, 82], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryComplexityAnalyzer.analyze_complexity": {"executed_lines": [102, 103, 104, 107, 108, 110, 111, 112, 115, 116, 117, 118, 121, 122, 123, 125, 126, 129, 130, 131, 132, 135, 142, 148, 151, 152, 156, 157, 162], "summary": {"covered_lines": 29, "num_statements": 40, "percent_covered": 72.5, "percent_covered_display": "72", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [133, 137, 138, 139, 143, 144, 145, 153, 154, 159, 160], "excluded_lines": []}, "QueryComplexityAnalyzer._analyze_context_complexity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [171, 174, 175, 178, 179, 180, 183, 184, 185, 186, 187, 189], "excluded_lines": []}, "QueryComplexityAnalyzer.get_model_performance_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [193], "excluded_lines": []}, "ModelSelector.__init__": {"executed_lines": [209, 210, 213], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelSelector.select_model": {"executed_lines": [223, 226, 228, 233], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ModelSelector._update_stats": {"executed_lines": [237, 238, 240, 243, 246], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [241], "excluded_lines": []}, "ModelSelector.get_stats": {"executed_lines": [250, 252, 253, 258, 259, 260, 263, 265], "summary": {"covered_lines": 8, "num_statements": 11, "percent_covered": 72.**************, "percent_covered_display": "73", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [254, 255, 256], "excluded_lines": []}, "ModelSelector._estimate_performance_improvement": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [269, 270, 273, 277], "excluded_lines": []}, "ModelSelector.reset_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [281], "excluded_lines": []}, "": {"executed_lines": [1, 9, 10, 11, 12, 14, 16, 19, 20, 21, 23, 24, 25, 26, 29, 30, 32, 100, 169, 191, 205, 206, 208, 221, 235, 248, 267, 279], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ComplexityAnalysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryComplexityAnalyzer": {"executed_lines": [33, 34, 37, 82, 102, 103, 104, 107, 108, 110, 111, 112, 115, 116, 117, 118, 121, 122, 123, 125, 126, 129, 130, 131, 132, 135, 142, 148, 151, 152, 156, 157, 162], "summary": {"covered_lines": 33, "num_statements": 57, "percent_covered": 57.89473684210526, "percent_covered_display": "58", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [133, 137, 138, 139, 143, 144, 145, 153, 154, 159, 160, 171, 174, 175, 178, 179, 180, 183, 184, 185, 186, 187, 189, 193], "excluded_lines": []}, "ModelSelector": {"executed_lines": [209, 210, 213, 223, 226, 228, 233, 237, 238, 240, 243, 246, 250, 252, 253, 258, 259, 260, 263, 265], "summary": {"covered_lines": 20, "num_statements": 29, "percent_covered": 68.96551724137932, "percent_covered_display": "69", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [241, 254, 255, 256, 269, 270, 273, 277, 281], "excluded_lines": []}, "": {"executed_lines": [1, 9, 10, 11, 12, 14, 16, 19, 20, 21, 23, 24, 25, 26, 29, 30, 32, 100, 169, 191, 205, 206, 208, 221, 235, 248, 267, 279], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/agents/models.py": {"executed_lines": [1, 8, 9, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 27, 28, 32, 33, 37], "summary": {"covered_lines": 16, "num_statements": 31, "percent_covered": 51.61290322580645, "percent_covered_display": "52", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [25, 30, 35, 40, 42, 43, 44, 46, 47, 48, 50, 51, 54, 55, 57], "excluded_lines": [], "functions": {"LLMResponse.token_count": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [25], "excluded_lines": []}, "LLMResponse.prompt_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [30], "excluded_lines": []}, "LLMResponse.completion_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [35], "excluded_lines": []}, "LLMResponse.estimate_cost": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [40, 42, 43, 44, 46, 47, 48, 50, 51, 54, 55, 57], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 27, 28, 32, 33, 37], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"LLMResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [25, 30, 35, 40, 42, 43, 44, 46, 47, 48, 50, 51, 54, 55, 57], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 12, 13, 14, 16, 17, 18, 19, 20, 22, 23, 27, 28, 32, 33, 37], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/agents/rag_agent.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 15, 16, 18, 21, 22, 24, 30, 31, 32, 35, 36, 38, 40, 43, 56, 68, 71, 72, 75, 79, 83, 84, 89, 91, 93, 94, 97, 100, 103, 105, 108, 121, 122, 124, 138, 140, 142, 147, 150, 154, 159, 176, 178, 179, 180, 182, 184, 185, 209, 223, 244, 265, 295, 297, 298, 308, 310, 312, 313], "summary": {"covered_lines": 64, "num_statements": 132, "percent_covered": 48.484848484848484, "percent_covered_display": "48", "missing_lines": 68, "excluded_lines": 0}, "missing_lines": [76, 80, 87, 126, 127, 128, 130, 131, 143, 144, 151, 161, 162, 164, 165, 166, 167, 168, 169, 170, 172, 174, 192, 198, 199, 205, 206, 207, 211, 221, 225, 227, 228, 229, 230, 232, 235, 237, 238, 240, 242, 246, 248, 249, 250, 251, 253, 255, 257, 258, 259, 261, 263, 267, 289, 290, 291, 293, 299, 300, 302, 303, 304, 306, 316, 317, 320, 322], "excluded_lines": [], "functions": {"RAGRetrievalAgent.__init__": {"executed_lines": [30, 31, 32, 35, 36, 38], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RAGRetrievalAgent.can_handle_query": {"executed_lines": [43, 56, 68, 71, 72, 75, 79, 83, 84], "summary": {"covered_lines": 9, "num_statements": 12, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [76, 80, 87], "excluded_lines": []}, "RAGRetrievalAgent.process_query": {"executed_lines": [91, 93, 94, 97, 100, 103, 105, 108, 121, 122, 124], "summary": {"covered_lines": 11, "num_statements": 16, "percent_covered": 68.75, "percent_covered_display": "69", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [126, 127, 128, 130, 131], "excluded_lines": []}, "RAGRetrievalAgent._perform_search": {"executed_lines": [140, 142, 147, 150, 154, 159, 176, 178, 179, 180], "summary": {"covered_lines": 10, "num_statements": 19, "percent_covered": 52.63157894736842, "percent_covered_display": "53", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [143, 144, 151, 161, 162, 164, 165, 172, 174], "excluded_lines": []}, "RAGRetrievalAgent._perform_search.SimpleSearchResult.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [166, 167, 168, 169, 170], "excluded_lines": []}, "RAGRetrievalAgent._generate_response_content": {"executed_lines": [184, 185], "summary": {"covered_lines": 2, "num_statements": 8, "percent_covered": 25.0, "percent_covered_display": "25", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [192, 198, 199, 205, 206, 207], "excluded_lines": []}, "RAGRetrievalAgent._is_code_query": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [211, 221], "excluded_lines": []}, "RAGRetrievalAgent._generate_code_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [225, 227, 228, 229, 230, 232, 235, 237, 238, 240, 242], "excluded_lines": []}, "RAGRetrievalAgent._generate_factual_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [246, 248, 249, 250, 251, 253, 255, 257, 258, 259, 261, 263], "excluded_lines": []}, "RAGRetrievalAgent._detect_language": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [267, 289, 290, 291, 293], "excluded_lines": []}, "RAGRetrievalAgent._extract_sources": {"executed_lines": [297, 298, 308], "summary": {"covered_lines": 3, "num_statements": 9, "percent_covered": 33.**************6, "percent_covered_display": "33", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [299, 300, 302, 303, 304, 306], "excluded_lines": []}, "RAGRetrievalAgent._calculate_confidence": {"executed_lines": [312, 313], "summary": {"covered_lines": 2, "num_statements": 6, "percent_covered": 33.**************6, "percent_covered_display": "33", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [316, 317, 320, 322], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 15, 16, 18, 21, 22, 24, 40, 89, 138, 182, 209, 223, 244, 265, 295, 310], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RAGRetrievalAgent": {"executed_lines": [30, 31, 32, 35, 36, 38, 43, 56, 68, 71, 72, 75, 79, 83, 84, 91, 93, 94, 97, 100, 103, 105, 108, 121, 122, 124, 140, 142, 147, 150, 154, 159, 176, 178, 179, 180, 184, 185, 297, 298, 308, 312, 313], "summary": {"covered_lines": 43, "num_statements": 106, "percent_covered": 40.56603773584906, "percent_covered_display": "41", "missing_lines": 63, "excluded_lines": 0}, "missing_lines": [76, 80, 87, 126, 127, 128, 130, 131, 143, 144, 151, 161, 162, 164, 165, 172, 174, 192, 198, 199, 205, 206, 207, 211, 221, 225, 227, 228, 229, 230, 232, 235, 237, 238, 240, 242, 246, 248, 249, 250, 251, 253, 255, 257, 258, 259, 261, 263, 267, 289, 290, 291, 293, 299, 300, 302, 303, 304, 306, 316, 317, 320, 322], "excluded_lines": []}, "RAGRetrievalAgent._perform_search.SimpleSearchResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [166, 167, 168, 169, 170], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 15, 16, 18, 21, 22, 24, 40, 89, 138, 182, 209, 223, 244, 265, 295, 310], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/config.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 16, 17, 19, 22, 23, 24, 27, 28, 29, 32, 35, 36, 37, 38, 39, 42, 43, 46, 49, 50, 51, 54, 57, 60, 65, 68, 71, 74, 77, 80, 85, 86, 89, 90, 91, 92, 93, 96, 97, 100, 101, 102, 103, 106, 107, 111, 114, 115, 116, 117, 118, 121, 124, 129, 134, 137, 140, 145, 146, 147, 150, 151, 154, 155, 158, 159, 160, 161, 162, 163, 165, 166, 168, 177, 178, 179, 181, 182, 184, 186, 187, 188, 190, 191, 193, 195, 196, 197, 199, 205, 207, 208, 209, 211, 217, 219, 220, 224, 225, 229, 230, 234, 235, 241, 248, 250, 254], "summary": {"covered_lines": 111, "num_statements": 125, "percent_covered": 88.8, "percent_covered_display": "89", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [183, 192, 201, 202, 204, 213, 214, 216, 222, 227, 232, 237, 238, 239], "excluded_lines": [], "functions": {"Settings.task_planner": {"executed_lines": [168], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Settings.validate_log_level": {"executed_lines": [181, 182, 184], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [183], "excluded_lines": []}, "Settings.validate_environment": {"executed_lines": [190, 191, 193], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [192], "excluded_lines": []}, "Settings.parse_cors_origins": {"executed_lines": [199, 205], "summary": {"covered_lines": 2, "num_statements": 5, "percent_covered": 40.0, "percent_covered_display": "40", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [201, 202, 204], "excluded_lines": []}, "Settings.parse_allowed_file_types": {"executed_lines": [211, 217], "summary": {"covered_lines": 2, "num_statements": 5, "percent_covered": 40.0, "percent_covered_display": "40", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [213, 214, 216], "excluded_lines": []}, "Settings.is_development": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [222], "excluded_lines": []}, "Settings.is_production": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [227], "excluded_lines": []}, "Settings.chroma_url": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [232], "excluded_lines": []}, "Settings.redis_url": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [237, 238, 239], "excluded_lines": []}, "get_settings": {"executed_lines": [250], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 16, 17, 19, 22, 23, 24, 27, 28, 29, 32, 35, 36, 37, 38, 39, 42, 43, 46, 49, 50, 51, 54, 57, 60, 65, 68, 71, 74, 77, 80, 85, 86, 89, 90, 91, 92, 93, 96, 97, 100, 101, 102, 103, 106, 107, 111, 114, 115, 116, 117, 118, 121, 124, 129, 134, 137, 140, 145, 146, 147, 150, 151, 154, 155, 158, 159, 160, 161, 162, 163, 165, 166, 177, 178, 179, 186, 187, 188, 195, 196, 197, 207, 208, 209, 219, 220, 224, 225, 229, 230, 234, 235, 241, 248, 254], "summary": {"covered_lines": 99, "num_statements": 99, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"Settings": {"executed_lines": [168, 181, 182, 184, 190, 191, 193, 199, 205, 211, 217], "summary": {"covered_lines": 11, "num_statements": 25, "percent_covered": 44.0, "percent_covered_display": "44", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [183, 192, 201, 202, 204, 213, 214, 216, 222, 227, 232, 237, 238, 239], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 16, 17, 19, 22, 23, 24, 27, 28, 29, 32, 35, 36, 37, 38, 39, 42, 43, 46, 49, 50, 51, 54, 57, 60, 65, 68, 71, 74, 77, 80, 85, 86, 89, 90, 91, 92, 93, 96, 97, 100, 101, 102, 103, 106, 107, 111, 114, 115, 116, 117, 118, 121, 124, 129, 134, 137, 140, 145, 146, 147, 150, 151, 154, 155, 158, 159, 160, 161, 162, 163, 165, 166, 177, 178, 179, 186, 187, 188, 195, 196, 197, 207, 208, 209, 219, 220, 224, 225, 229, 230, 234, 235, 241, 248, 250, 254], "summary": {"covered_lines": 100, "num_statements": 100, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/__init__.py": {"executed_lines": [1, 8, 30, 35, 36, 45, 52, 53, 54, 62, 67], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 30, 35, 36, 45, 52, 53, 54, 62, 67], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 30, 35, 36, 45, 52, 53, 54, 62, 67], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/base.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 35, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 82, 83, 84, 86, 87, 88, 89, 90, 91, 94, 95, 96, 99, 100, 101, 104, 105, 106, 107, 108, 111, 114, 115, 118, 119, 122, 123, 125, 152, 153, 159, 160, 162, 164, 165, 167, 168, 170, 171, 173, 174, 177, 180, 183, 184, 185, 187, 188, 189, 190, 191, 192, 194, 206, 207, 208, 210, 211, 212, 215, 216, 218, 219, 221, 223, 225, 258, 259, 260, 262, 263, 264, 266, 267, 269, 271, 272, 274, 276, 284, 285, 287, 288, 292, 293, 297, 298, 302, 303, 307, 308, 313, 314, 316, 317, 323, 324, 328, 329, 334, 335, 337, 338, 342, 343, 347, 348, 353, 354, 356, 357, 361, 362, 366, 367, 372, 373, 375, 376, 380, 381, 385, 386, 390, 391, 396, 397, 399, 400, 404, 405, 409, 410, 415, 416, 418, 419, 423, 424, 428, 429, 434, 435, 437, 438, 442, 443, 447, 448, 452, 453, 457, 458, 463, 464, 466, 467, 473, 474, 480, 481, 485, 486, 490, 491, 495, 496, 500, 501, 506, 507, 509, 510, 514, 515, 519, 520, 526, 527], "summary": {"covered_lines": 129, "num_statements": 139, "percent_covered": 92.80575539568345, "percent_covered_display": "93", "missing_lines": 10, "excluded_lines": 168}, "missing_lines": [127, 155, 156, 157, 178, 196, 278, 279, 280, 281], "excluded_lines": [287, 288, 289, 290, 292, 293, 294, 295, 297, 298, 299, 300, 302, 303, 304, 305, 307, 308, 309, 310, 316, 317, 318, 319, 320, 321, 323, 324, 325, 326, 328, 329, 330, 331, 337, 338, 339, 340, 342, 343, 344, 345, 347, 348, 349, 350, 356, 357, 358, 359, 361, 362, 363, 364, 366, 367, 368, 369, 375, 376, 377, 378, 380, 381, 382, 383, 385, 386, 387, 388, 390, 391, 392, 393, 399, 400, 401, 402, 404, 405, 406, 407, 409, 410, 411, 412, 418, 419, 420, 421, 423, 424, 425, 426, 428, 429, 430, 431, 437, 438, 439, 440, 442, 443, 444, 445, 447, 448, 449, 450, 452, 453, 454, 455, 457, 458, 459, 460, 466, 467, 468, 469, 470, 471, 473, 474, 475, 476, 477, 478, 480, 481, 482, 483, 485, 486, 487, 488, 490, 491, 492, 493, 495, 496, 497, 498, 500, 501, 502, 503, 509, 510, 511, 512, 514, 515, 516, 517, 519, 520, 521, 522, 523, 524, 526, 527, 528, 529], "functions": {"FileMetadata.to_dict": {"executed_lines": [35], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Chunk.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [127], "excluded_lines": []}, "Chunk.line_range": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [155, 156, 157], "excluded_lines": []}, "Chunk.full_context_path": {"executed_lines": [162, 164, 165, 167, 168, 170, 171, 173, 174, 177, 180], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [178], "excluded_lines": []}, "EmbeddingMetadata.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [196], "excluded_lines": []}, "EmbeddedChunk.embedding_id": {"executed_lines": [221], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EmbeddedChunk.to_dict": {"executed_lines": [225], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SearchResult.chunk": {"executed_lines": [269], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SearchResult.content": {"executed_lines": [274], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SearchResult.to_dict": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [278, 279, 280, 281], "excluded_lines": []}, "RepositorySource.authenticate": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [289, 290]}, "RepositorySource.get_repository_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [294, 295]}, "RepositorySource.list_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [299, 300]}, "RepositorySource.get_file_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [304, 305]}, "RepositorySource.clone_repository": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [309, 310]}, "FileFilter.should_include_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [320, 321]}, "FileFilter.filter_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [325, 326]}, "FileFilter.get_file_priority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [330, 331]}, "MetadataExtractor.extract_file_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [339, 340]}, "MetadataExtractor.extract_repository_metadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [344, 345]}, "MetadataExtractor.get_commit_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [349, 350]}, "FileLoader.load_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [358, 359]}, "FileLoader.detect_encoding": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [363, 364]}, "FileLoader.is_supported": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [368, 369]}, "ChunkingStrategy.chunk_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [377, 378]}, "ChunkingStrategy.get_strategy_name": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [382, 383]}, "ChunkingStrategy.is_applicable": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [387, 388]}, "ChunkingStrategy.estimate_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [392, 393]}, "ChunkingPipeline.process_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [401, 402]}, "ChunkingPipeline.process_single_file": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [406, 407]}, "ChunkingPipeline.get_chunking_strategy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [411, 412]}, "IngestionPipeline.ingest_repository": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [420, 421]}, "IngestionPipeline.ingest_files": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [425, 426]}, "IngestionPipeline.get_ingestion_status": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [430, 431]}, "EmbeddingClient.generate_embeddings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [439, 440]}, "EmbeddingClient.get_embedding_dimension": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [444, 445]}, "EmbeddingClient.get_model_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [449, 450]}, "EmbeddingClient.generate_single_embedding": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [454, 455]}, "EmbeddingClient.estimate_cost": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [459, 460]}, "VectorStore.add_embeddings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [470, 471]}, "VectorStore.search_similar": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [477, 478]}, "VectorStore.delete_embeddings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [482, 483]}, "VectorStore.get_embedding": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [487, 488]}, "VectorStore.get_collection_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [492, 493]}, "VectorStore.create_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [497, 498]}, "VectorStore.delete_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [502, 503]}, "EmbeddingPipeline.process_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [511, 512]}, "EmbeddingPipeline.store_embeddings": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [516, 517]}, "EmbeddingPipeline.search_similar_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [523, 524]}, "EmbeddingPipeline.get_pipeline_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [528, 529]}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 82, 83, 84, 86, 87, 88, 89, 90, 91, 94, 95, 96, 99, 100, 101, 104, 105, 106, 107, 108, 111, 114, 115, 118, 119, 122, 123, 125, 152, 153, 159, 160, 183, 184, 185, 187, 188, 189, 190, 191, 192, 194, 206, 207, 208, 210, 211, 212, 215, 216, 218, 219, 223, 258, 259, 260, 262, 263, 264, 266, 267, 271, 272, 276, 284, 285, 287, 288, 292, 293, 297, 298, 302, 303, 307, 308, 313, 314, 316, 317, 323, 324, 328, 329, 334, 335, 337, 338, 342, 343, 347, 348, 353, 354, 356, 357, 361, 362, 366, 367, 372, 373, 375, 376, 380, 381, 385, 386, 390, 391, 396, 397, 399, 400, 404, 405, 409, 410, 415, 416, 418, 419, 423, 424, 428, 429, 434, 435, 437, 438, 442, 443, 447, 448, 452, 453, 457, 458, 463, 464, 466, 467, 473, 474, 480, 481, 485, 486, 490, 491, 495, 496, 500, 501, 506, 507, 509, 510, 514, 515, 519, 520, 526, 527], "summary": {"covered_lines": 113, "num_statements": 113, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 88}, "missing_lines": [], "excluded_lines": [287, 288, 292, 293, 297, 298, 302, 303, 307, 308, 316, 317, 318, 319, 323, 324, 328, 329, 337, 338, 342, 343, 347, 348, 356, 357, 361, 362, 366, 367, 375, 376, 380, 381, 385, 386, 390, 391, 399, 400, 404, 405, 409, 410, 418, 419, 423, 424, 428, 429, 437, 438, 442, 443, 447, 448, 452, 453, 457, 458, 466, 467, 468, 469, 473, 474, 475, 476, 480, 481, 485, 486, 490, 491, 495, 496, 500, 501, 509, 510, 514, 515, 519, 520, 521, 522, 526, 527]}}, "classes": {"FileMetadata": {"executed_lines": [35], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RepositoryInfo": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChunkType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChunkContext": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Chunk": {"executed_lines": [162, 164, 165, 167, 168, 170, 171, 173, 174, 177, 180], "summary": {"covered_lines": 11, "num_statements": 16, "percent_covered": 68.75, "percent_covered_display": "69", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [127, 155, 156, 157, 178], "excluded_lines": []}, "EmbeddingMetadata": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [196], "excluded_lines": []}, "EmbeddedChunk": {"executed_lines": [221, 225], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SearchResult": {"executed_lines": [269, 274], "summary": {"covered_lines": 2, "num_statements": 6, "percent_covered": 33.**************6, "percent_covered_display": "33", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [278, 279, 280, 281], "excluded_lines": []}, "RepositorySource": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [289, 290, 294, 295, 299, 300, 304, 305, 309, 310]}, "FileFilter": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 6}, "missing_lines": [], "excluded_lines": [320, 321, 325, 326, 330, 331]}, "MetadataExtractor": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 6}, "missing_lines": [], "excluded_lines": [339, 340, 344, 345, 349, 350]}, "FileLoader": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 6}, "missing_lines": [], "excluded_lines": [358, 359, 363, 364, 368, 369]}, "ChunkingStrategy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 8}, "missing_lines": [], "excluded_lines": [377, 378, 382, 383, 387, 388, 392, 393]}, "ChunkingPipeline": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 6}, "missing_lines": [], "excluded_lines": [401, 402, 406, 407, 411, 412]}, "IngestionPipeline": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 6}, "missing_lines": [], "excluded_lines": [420, 421, 425, 426, 430, 431]}, "EmbeddingClient": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 10}, "missing_lines": [], "excluded_lines": [439, 440, 444, 445, 449, 450, 454, 455, 459, 460]}, "VectorStore": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 14}, "missing_lines": [], "excluded_lines": [470, 471, 477, 478, 482, 483, 487, 488, 492, 493, 497, 498, 502, 503]}, "EmbeddingPipeline": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 8}, "missing_lines": [], "excluded_lines": [511, 512, 516, 517, 523, 524, 528, 529]}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 16, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 33, 51, 52, 53, 55, 56, 57, 58, 59, 60, 61, 62, 63, 64, 65, 68, 69, 71, 72, 73, 74, 75, 76, 77, 78, 79, 82, 83, 84, 86, 87, 88, 89, 90, 91, 94, 95, 96, 99, 100, 101, 104, 105, 106, 107, 108, 111, 114, 115, 118, 119, 122, 123, 125, 152, 153, 159, 160, 183, 184, 185, 187, 188, 189, 190, 191, 192, 194, 206, 207, 208, 210, 211, 212, 215, 216, 218, 219, 223, 258, 259, 260, 262, 263, 264, 266, 267, 271, 272, 276, 284, 285, 287, 288, 292, 293, 297, 298, 302, 303, 307, 308, 313, 314, 316, 317, 323, 324, 328, 329, 334, 335, 337, 338, 342, 343, 347, 348, 353, 354, 356, 357, 361, 362, 366, 367, 372, 373, 375, 376, 380, 381, 385, 386, 390, 391, 396, 397, 399, 400, 404, 405, 409, 410, 415, 416, 418, 419, 423, 424, 428, 429, 434, 435, 437, 438, 442, 443, 447, 448, 452, 453, 457, 458, 463, 464, 466, 467, 473, 474, 480, 481, 485, 486, 490, 491, 495, 496, 500, 501, 506, 507, 509, 510, 514, 515, 519, 520, 526, 527], "summary": {"covered_lines": 113, "num_statements": 113, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 88}, "missing_lines": [], "excluded_lines": [287, 288, 292, 293, 297, 298, 302, 303, 307, 308, 316, 317, 318, 319, 323, 324, 328, 329, 337, 338, 342, 343, 347, 348, 356, 357, 361, 362, 366, 367, 375, 376, 380, 381, 385, 386, 390, 391, 399, 400, 404, 405, 409, 410, 418, 419, 423, 424, 428, 429, 437, 438, 442, 443, 447, 448, 452, 453, 457, 458, 466, 467, 468, 469, 473, 474, 475, 476, 480, 481, 485, 486, 490, 491, 495, 496, 500, 501, 509, 510, 514, 515, 519, 520, 521, 522, 526, 527]}}}, "src/ingestion/embedding/__init__.py": {"executed_lines": [1, 8, 9, 11], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 11], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 11], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/embedding/client.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 17, 18, 19, 21, 24, 25, 26, 28, 29, 30, 31, 32, 34, 36, 37, 38, 39, 40, 42, 44, 45, 50, 52, 53, 54, 57, 58, 61, 79, 81, 82, 83, 84, 85, 86, 89, 90, 95, 98, 104, 112, 114, 116, 117, 120, 122, 123, 124, 125, 127, 128, 130, 133, 136, 138, 139, 140, 143, 146, 147, 148, 150, 152, 153, 154, 156, 157, 158, 160, 161, 167, 168, 170, 171, 172, 174, 175, 189, 191, 195, 197, 198, 199, 201, 203, 204, 206, 208, 210, 212, 220, 222, 223, 225, 227, 229, 231, 240, 241, 243, 244, 246, 248, 249, 250, 251, 253, 254, 258, 259, 261, 263, 264, 266, 268, 269, 270, 271, 272, 274], "summary": {"covered_lines": 124, "num_statements": 132, "percent_covered": 93.93939393939394, "percent_covered_display": "94", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [181, 182, 183, 184, 187, 192, 193, 275], "excluded_lines": [], "functions": {"RateLimitInfo.reset_if_needed": {"executed_lines": [36, 37, 38, 39, 40], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimitInfo.can_make_request": {"executed_lines": [44, 45], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RateLimitInfo.record_request": {"executed_lines": [52, 53, 54], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient.__init__": {"executed_lines": [81, 82, 83, 84, 85, 86, 89, 90, 95, 98, 104, 112], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient.generate_embeddings": {"executed_lines": [116, 117, 120, 122, 123, 124, 125, 127, 128], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient._generate_batch_embeddings": {"executed_lines": [133, 136, 138, 139, 140, 143, 146, 147, 148, 150, 152, 153, 154, 156, 157, 158, 160, 161, 167, 168, 170, 171, 172, 174, 175], "summary": {"covered_lines": 25, "num_statements": 30, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [181, 182, 183, 184, 187], "excluded_lines": []}, "OpenAIEmbeddingClient._wait_for_rate_limit": {"executed_lines": [191], "summary": {"covered_lines": 1, "num_statements": 3, "percent_covered": 33.**************6, "percent_covered_display": "33", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [192, 193], "excluded_lines": []}, "OpenAIEmbeddingClient._update_stats": {"executed_lines": [197, 198, 199], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient.generate_single_embedding": {"executed_lines": [203, 204], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient.get_embedding_dimension": {"executed_lines": [208], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient.get_model_info": {"executed_lines": [212], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient.estimate_cost": {"executed_lines": [222, 223], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient.get_stats": {"executed_lines": [227], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient.reset_stats": {"executed_lines": [231], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EmbeddingClientFactory.create_client": {"executed_lines": [246, 248, 249, 250, 251, 253, 254], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EmbeddingClientFactory.get_available_providers": {"executed_lines": [261], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "EmbeddingClientFactory.validate_provider_config": {"executed_lines": [266, 268, 269, 270, 271, 272, 274], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [275], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 17, 18, 19, 21, 24, 25, 26, 28, 29, 30, 31, 32, 34, 42, 50, 57, 58, 61, 79, 114, 130, 189, 195, 201, 206, 210, 220, 225, 229, 240, 241, 243, 244, 258, 259, 263, 264], "summary": {"covered_lines": 41, "num_statements": 41, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RateLimitInfo": {"executed_lines": [36, 37, 38, 39, 40, 44, 45, 52, 53, 54], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OpenAIEmbeddingClient": {"executed_lines": [81, 82, 83, 84, 85, 86, 89, 90, 95, 98, 104, 112, 116, 117, 120, 122, 123, 124, 125, 127, 128, 133, 136, 138, 139, 140, 143, 146, 147, 148, 150, 152, 153, 154, 156, 157, 158, 160, 161, 167, 168, 170, 171, 172, 174, 175, 191, 197, 198, 199, 203, 204, 208, 212, 222, 223, 227, 231], "summary": {"covered_lines": 58, "num_statements": 65, "percent_covered": 89.23076923076923, "percent_covered_display": "89", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [181, 182, 183, 184, 187, 192, 193], "excluded_lines": []}, "EmbeddingClientFactory": {"executed_lines": [246, 248, 249, 250, 251, 253, 254, 261, 266, 268, 269, 270, 271, 272, 274], "summary": {"covered_lines": 15, "num_statements": 16, "percent_covered": 93.75, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [275], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 17, 18, 19, 21, 24, 25, 26, 28, 29, 30, 31, 32, 34, 42, 50, 57, 58, 61, 79, 114, 130, 189, 195, 201, 206, 210, 220, 225, 229, 240, 241, 243, 244, 258, 259, 263, 264], "summary": {"covered_lines": 41, "num_statements": 41, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/embedding/local.py": {"executed_lines": [1, 8, 9, 11, 12, 13, 15, 18, 19, 22, 40, 42, 43, 44, 45, 48, 56, 59, 60, 63, 70, 72, 110, 112, 115, 150, 155, 157, 159, 161, 171, 173, 175, 179, 188, 190, 191, 192, 193, 194, 196, 198, 199], "summary": {"covered_lines": 41, "num_statements": 83, "percent_covered": 49.397590361445786, "percent_covered_display": "49", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [49, 50, 74, 75, 77, 78, 79, 82, 83, 84, 86, 87, 90, 91, 92, 94, 96, 97, 103, 104, 113, 117, 119, 121, 122, 125, 133, 134, 137, 138, 140, 141, 143, 144, 145, 146, 152, 153, 177, 181, 200, 201], "excluded_lines": [], "functions": {"LocalEmbeddingClient.__init__": {"executed_lines": [42, 43, 44, 45, 48, 56, 59, 60, 63, 70], "summary": {"covered_lines": 10, "num_statements": 12, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [49, 50], "excluded_lines": []}, "LocalEmbeddingClient._load_model": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [74, 75, 77, 78, 79, 82, 83, 84, 86, 87, 90, 91, 92, 94, 96, 97, 103, 104], "excluded_lines": []}, "LocalEmbeddingClient.generate_embeddings": {"executed_lines": [112, 115], "summary": {"covered_lines": 2, "num_statements": 18, "percent_covered": 11.11111111111111, "percent_covered_display": "11", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [113, 117, 119, 121, 122, 125, 133, 134, 137, 138, 140, 141, 143, 144, 145, 146], "excluded_lines": []}, "LocalEmbeddingClient.generate_single_embedding": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [152, 153], "excluded_lines": []}, "LocalEmbeddingClient.get_embedding_dimension": {"executed_lines": [157], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LocalEmbeddingClient.get_model_info": {"executed_lines": [161], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LocalEmbeddingClient.estimate_cost": {"executed_lines": [173], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LocalEmbeddingClient.get_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [177], "excluded_lines": []}, "LocalEmbeddingClient.reset_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [181], "excluded_lines": []}, "LocalEmbeddingClient.unload_model": {"executed_lines": [190, 191, 192, 193, 194], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "LocalEmbeddingClient.__del__": {"executed_lines": [198, 199], "summary": {"covered_lines": 2, "num_statements": 4, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [200, 201], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 12, 13, 15, 18, 19, 22, 40, 72, 110, 150, 155, 159, 171, 175, 179, 188, 196], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"LocalEmbeddingClient": {"executed_lines": [42, 43, 44, 45, 48, 56, 59, 60, 63, 70, 112, 115, 157, 161, 173, 190, 191, 192, 193, 194, 198, 199], "summary": {"covered_lines": 22, "num_statements": 64, "percent_covered": 34.375, "percent_covered_display": "34", "missing_lines": 42, "excluded_lines": 0}, "missing_lines": [49, 50, 74, 75, 77, 78, 79, 82, 83, 84, 86, 87, 90, 91, 92, 94, 96, 97, 103, 104, 113, 117, 119, 121, 122, 125, 133, 134, 137, 138, 140, 141, 143, 144, 145, 146, 152, 153, 177, 181, 200, 201], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 12, 13, 15, 18, 19, 22, 40, 72, 110, 150, 155, 159, 171, 175, 179, 188, 196], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/embedding_pipeline.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 16, 17, 19, 22, 23, 25, 32, 35, 36, 39, 40, 41, 42, 45, 48, 59, 61, 63, 65, 66, 68, 71, 72, 73, 75, 76, 78, 79, 80, 82, 87, 89, 90, 91, 100, 102, 105, 106, 107, 108, 110, 111, 113, 115, 116, 117, 120, 121, 122, 125, 126, 127, 130, 131, 132, 133, 134, 137, 138, 139, 140, 142, 143, 144, 145, 148, 151, 152, 155, 156, 157, 158, 159, 161, 162, 165, 172, 173, 179, 180, 181, 182, 183, 186, 187, 189, 191, 193, 195, 197, 199, 201, 202, 205, 206, 207, 210, 214, 216, 218, 221, 223, 227, 229, 231, 234, 235, 238, 239, 240, 243, 245, 246, 247, 249, 255, 259, 261, 263, 267, 268, 271, 275, 276, 278, 279, 280, 282, 285, 288, 289, 292, 293, 296, 297, 298, 299, 300, 302, 304, 306, 307, 309, 311], "summary": {"covered_lines": 150, "num_statements": 160, "percent_covered": 93.75, "percent_covered_display": "94", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [174, 175, 176, 211, 224, 232, 251, 252, 253, 264], "excluded_lines": [], "functions": {"DefaultEmbeddingPipeline.__init__": {"executed_lines": [32, 35, 36, 39, 40, 41, 42, 45, 48, 59], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DefaultEmbeddingPipeline.process_chunks": {"executed_lines": [63, 65, 66, 68, 71, 72, 73, 75, 76, 78, 79, 80, 82, 87, 89, 90, 91], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DefaultEmbeddingPipeline._process_batch": {"executed_lines": [102, 105, 106, 107, 108, 110, 111, 113, 115, 116, 117, 120, 121, 122, 125, 126, 127, 130, 131, 132, 133, 134, 137, 138, 139, 140, 142, 143, 144, 145, 148, 151, 152, 155, 156, 157, 158, 159, 161, 162, 165, 172, 173, 179, 180, 181, 182, 183, 186, 187, 189, 191, 193], "summary": {"covered_lines": 53, "num_statements": 56, "percent_covered": 94.64285714285714, "percent_covered_display": "95", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [174, 175, 176], "excluded_lines": []}, "DefaultEmbeddingPipeline._get_content_hash": {"executed_lines": [197], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DefaultEmbeddingPipeline._validate_embedding": {"executed_lines": [201, 202, 205, 206, 207, 210, 214], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [211], "excluded_lines": []}, "DefaultEmbeddingPipeline._normalize_embedding": {"executed_lines": [218, 221, 223, 227], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [224], "excluded_lines": []}, "DefaultEmbeddingPipeline.store_embeddings": {"executed_lines": [231, 234, 235, 238, 239, 240, 243, 245, 246, 247, 249], "summary": {"covered_lines": 11, "num_statements": 15, "percent_covered": 73.**************, "percent_covered_display": "73", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [232, 251, 252, 253], "excluded_lines": []}, "DefaultEmbeddingPipeline.search_similar_chunks": {"executed_lines": [259, 261, 263, 267, 268, 271, 275, 276, 278, 279, 280], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [264], "excluded_lines": []}, "DefaultEmbeddingPipeline.get_pipeline_stats": {"executed_lines": [285, 288, 289, 292, 293, 296, 297, 298, 299, 300, 302], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DefaultEmbeddingPipeline.clear_cache": {"executed_lines": [306, 307], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DefaultEmbeddingPipeline.reset_stats": {"executed_lines": [311], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 16, 17, 19, 22, 23, 25, 61, 100, 195, 199, 216, 229, 255, 282, 304, 309], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DefaultEmbeddingPipeline": {"executed_lines": [32, 35, 36, 39, 40, 41, 42, 45, 48, 59, 63, 65, 66, 68, 71, 72, 73, 75, 76, 78, 79, 80, 82, 87, 89, 90, 91, 102, 105, 106, 107, 108, 110, 111, 113, 115, 116, 117, 120, 121, 122, 125, 126, 127, 130, 131, 132, 133, 134, 137, 138, 139, 140, 142, 143, 144, 145, 148, 151, 152, 155, 156, 157, 158, 159, 161, 162, 165, 172, 173, 179, 180, 181, 182, 183, 186, 187, 189, 191, 193, 197, 201, 202, 205, 206, 207, 210, 214, 218, 221, 223, 227, 231, 234, 235, 238, 239, 240, 243, 245, 246, 247, 249, 259, 261, 263, 267, 268, 271, 275, 276, 278, 279, 280, 285, 288, 289, 292, 293, 296, 297, 298, 299, 300, 302, 306, 307, 311], "summary": {"covered_lines": 128, "num_statements": 138, "percent_covered": 92.7536231884058, "percent_covered_display": "93", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [174, 175, 176, 211, 224, 232, 251, 252, 253, 264], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 16, 17, 19, 22, 23, 25, 61, 100, 195, 199, 216, 229, 255, 282, 304, 309], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/exceptions.py": {"executed_lines": [1, 8, 11, 12, 14, 15, 16, 17, 18, 19, 21, 22, 23, 24, 27, 28, 30, 37, 38, 41, 42, 44, 52, 53, 54, 57, 58, 60, 73, 74, 76, 89, 90, 92, 100, 101, 102, 105, 106, 108, 119, 120, 122, 135, 136, 138, 144, 145, 147, 159, 160, 162, 175, 176, 178, 183, 184, 186, 194, 195, 197], "summary": {"covered_lines": 46, "num_statements": 74, "percent_covered": 62.16216216216216, "percent_covered_display": "62", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [68, 69, 70, 84, 85, 86, 115, 116, 130, 131, 132, 139, 140, 141, 154, 155, 156, 170, 171, 172, 179, 180, 189, 190, 191, 205, 206, 207], "excluded_lines": [], "functions": {"IngestionError.__init__": {"executed_lines": [15, 16, 17, 18, 19], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "IngestionError.__str__": {"executed_lines": [22, 23, 24], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthenticationError.__init__": {"executed_lines": [37, 38], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RepositoryError.__init__": {"executed_lines": [52, 53, 54], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FileProcessingError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [68, 69, 70], "excluded_lines": []}, "MetadataExtractionError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [84, 85, 86], "excluded_lines": []}, "RateLimitError.__init__": {"executed_lines": [100, 101, 102], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [115, 116], "excluded_lines": []}, "EmbeddingError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [130, 131, 132], "excluded_lines": []}, "EmbeddingGenerationError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [139, 140, 141], "excluded_lines": []}, "EmbeddingQualityError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [154, 155, 156], "excluded_lines": []}, "VectorStoreError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [170, 171, 172], "excluded_lines": []}, "VectorStoreConnectionError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [179, 180], "excluded_lines": []}, "VectorStoreOperationError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [189, 190, 191], "excluded_lines": []}, "EmbeddingPipelineError.__init__": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [205, 206, 207], "excluded_lines": []}, "": {"executed_lines": [1, 8, 11, 12, 14, 21, 27, 28, 30, 41, 42, 44, 57, 58, 60, 73, 74, 76, 89, 90, 92, 105, 106, 108, 119, 120, 122, 135, 136, 138, 144, 145, 147, 159, 160, 162, 175, 176, 178, 183, 184, 186, 194, 195, 197], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"IngestionError": {"executed_lines": [15, 16, 17, 18, 19, 22, 23, 24], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AuthenticationError": {"executed_lines": [37, 38], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RepositoryError": {"executed_lines": [52, 53, 54], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FileProcessingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [68, 69, 70], "excluded_lines": []}, "MetadataExtractionError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [84, 85, 86], "excluded_lines": []}, "RateLimitError": {"executed_lines": [100, 101, 102], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigurationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [115, 116], "excluded_lines": []}, "EmbeddingError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [130, 131, 132], "excluded_lines": []}, "EmbeddingGenerationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [139, 140, 141], "excluded_lines": []}, "EmbeddingQualityError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [154, 155, 156], "excluded_lines": []}, "VectorStoreError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [170, 171, 172], "excluded_lines": []}, "VectorStoreConnectionError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [179, 180], "excluded_lines": []}, "VectorStoreOperationError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [189, 190, 191], "excluded_lines": []}, "EmbeddingPipelineError": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [205, 206, 207], "excluded_lines": []}, "": {"executed_lines": [1, 8, 11, 12, 14, 21, 27, 28, 30, 41, 42, 44, 57, 58, 60, 73, 74, 76, 89, 90, 92, 105, 106, 108, 119, 120, 122, 135, 136, 138, 144, 145, 147, 159, 160, 162, 175, 176, 178, 183, 184, 186, 194, 195, 197], "summary": {"covered_lines": 30, "num_statements": 30, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/github/__init__.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/github/client.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 15, 16, 17, 23, 26, 27, 29, 31, 32, 33, 34, 35, 37, 39, 40, 41, 47, 48, 49, 52, 56, 57, 60, 61, 63, 64, 65, 75, 77, 80, 81, 82, 83, 85, 93, 95, 96, 97, 98, 99, 100, 110, 113, 114, 115, 116, 119, 121, 122, 123, 129, 131, 133, 136, 138, 139, 140, 143, 146, 147, 149, 163, 164, 165, 173, 175, 177, 180, 181, 182, 185, 186, 187, 192, 193, 195, 196, 208, 231, 233, 236, 238, 239, 240, 243, 244, 245, 249, 251, 252, 258, 260, 270, 272], "summary": {"covered_lines": 98, "num_statements": 132, "percent_covered": 74.24242424242425, "percent_covered_display": "74", "missing_lines": 34, "excluded_lines": 1}, "missing_lines": [71, 72, 73, 78, 90, 91, 134, 178, 188, 189, 190, 198, 199, 200, 210, 212, 213, 214, 215, 217, 218, 219, 220, 221, 223, 224, 225, 227, 229, 234, 246, 247, 261, 262], "excluded_lines": [276], "functions": {"GitHubClient.__init__": {"executed_lines": [31, 32, 33, 34, 35], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubClient.authenticate": {"executed_lines": [39, 40, 41, 47, 48, 49, 52, 56, 57, 60, 61, 63, 64, 65], "summary": {"covered_lines": 14, "num_statements": 17, "percent_covered": 82.3529411764706, "percent_covered_display": "82", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [71, 72, 73], "excluded_lines": []}, "GitHubClient._update_rate_limit_info": {"executed_lines": [77, 80, 81, 82, 83, 85], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [78, 90, 91], "excluded_lines": []}, "GitHubClient._check_rate_limit": {"executed_lines": [95, 96, 97, 98, 99, 100], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubClient._parse_repository_url": {"executed_lines": [113, 114, 115, 116, 119, 121, 122, 123, 129], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubClient.get_repository_info": {"executed_lines": [133, 136, 138, 139, 140, 143, 146, 147, 149, 163, 164, 165, 173], "summary": {"covered_lines": 13, "num_statements": 14, "percent_covered": 92.85714285714286, "percent_covered_display": "93", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [134], "excluded_lines": []}, "GitHubClient.list_files": {"executed_lines": [177, 180, 181, 182, 185, 186, 187, 192, 193, 195, 196], "summary": {"covered_lines": 11, "num_statements": 18, "percent_covered": 61.111111111111114, "percent_covered_display": "61", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [178, 188, 189, 190, 198, 199, 200], "excluded_lines": []}, "GitHubClient._list_files_recursive": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [210, 212, 213, 214, 215, 217, 218, 219, 220, 221, 223, 224, 225, 227, 229], "excluded_lines": []}, "GitHubClient.get_file_content": {"executed_lines": [233, 236, 238, 239, 240, 243, 244, 245, 249, 251, 252, 258, 260, 270], "summary": {"covered_lines": 14, "num_statements": 19, "percent_covered": 73.6842105263158, "percent_covered_display": "74", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [234, 246, 247, 261, 262], "excluded_lines": []}, "GitHubClient.clone_repository": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 1}, "missing_lines": [], "excluded_lines": [276]}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 15, 16, 17, 23, 26, 27, 29, 37, 75, 93, 110, 131, 175, 208, 231, 272], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"GitHubClient": {"executed_lines": [31, 32, 33, 34, 35, 39, 40, 41, 47, 48, 49, 52, 56, 57, 60, 61, 63, 64, 65, 77, 80, 81, 82, 83, 85, 95, 96, 97, 98, 99, 100, 113, 114, 115, 116, 119, 121, 122, 123, 129, 133, 136, 138, 139, 140, 143, 146, 147, 149, 163, 164, 165, 173, 177, 180, 181, 182, 185, 186, 187, 192, 193, 195, 196, 233, 236, 238, 239, 240, 243, 244, 245, 249, 251, 252, 258, 260, 270], "summary": {"covered_lines": 78, "num_statements": 112, "percent_covered": 69.64285714285714, "percent_covered_display": "70", "missing_lines": 34, "excluded_lines": 1}, "missing_lines": [71, 72, 73, 78, 90, 91, 134, 178, 188, 189, 190, 198, 199, 200, 210, 212, 213, 214, 215, 217, 218, 219, 220, 221, 223, 224, 225, 227, 229, 234, 246, 247, 261, 262], "excluded_lines": [276]}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 15, 16, 17, 23, 26, 27, 29, 37, 75, 93, 110, 131, 175, 208, 231, 272], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/github/connector.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 16, 19, 20, 21, 22, 24, 27, 28, 30, 39, 42, 43, 44, 45, 48, 50, 52, 56, 57, 59, 60, 63, 74, 75, 78, 83, 84, 86, 96, 97, 98, 100, 103, 104, 107, 108, 111, 112, 113, 114, 116, 117, 120, 133, 144, 145, 147, 148, 149, 151, 153, 163, 173, 175, 176, 178, 179, 181, 182, 183, 186, 188, 191, 192, 194, 195, 197, 198, 204, 206, 207, 210, 214, 217, 222, 229, 232, 233, 234, 236, 242, 244, 245, 247, 251, 253, 254, 256, 257, 260, 262, 267, 269], "summary": {"covered_lines": 99, "num_statements": 114, "percent_covered": 86.84210526315789, "percent_covered_display": "87", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [79, 80, 193, 200, 201, 202, 211, 212, 218, 219, 225, 226, 227, 264, 265], "excluded_lines": [], "functions": {"GitHubConnector.__init__": {"executed_lines": [39, 42, 43, 44, 45, 48, 50], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubConnector.ingest_repository": {"executed_lines": [56, 57, 59, 60, 63, 74, 75, 78, 83, 84, 86, 96, 97, 98, 100, 103, 104, 107, 108, 111, 112, 113, 114, 116, 117, 120, 133, 144, 145, 147, 148, 149, 151, 153, 163], "summary": {"covered_lines": 35, "num_statements": 37, "percent_covered": 94.5945945945946, "percent_covered_display": "95", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [79, 80], "excluded_lines": []}, "GitHubConnector.ingest_files": {"executed_lines": [175, 176, 178, 179, 181, 182, 183, 186, 188, 191, 192, 194, 195, 197, 198], "summary": {"covered_lines": 15, "num_statements": 19, "percent_covered": 78.94736842105263, "percent_covered_display": "79", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [193, 200, 201, 202], "excluded_lines": []}, "GitHubConnector._process_single_file": {"executed_lines": [206, 207, 210, 214, 217, 222], "summary": {"covered_lines": 6, "num_statements": 13, "percent_covered": 46.15384615384615, "percent_covered_display": "46", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [211, 212, 218, 219, 225, 226, 227], "excluded_lines": []}, "GitHubConnector.get_ingestion_status": {"executed_lines": [232, 233, 234, 236], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubConnector._update_ingestion_status": {"executed_lines": [244, 245, 247], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubConnector.cleanup": {"executed_lines": [253, 254, 256, 257, 260, 262], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [264, 265], "excluded_lines": []}, "GitHubConnector.get_connector_stats": {"executed_lines": [269], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 16, 19, 20, 21, 22, 24, 27, 28, 30, 52, 173, 204, 229, 242, 251, 267], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"GitHubConnector": {"executed_lines": [39, 42, 43, 44, 45, 48, 50, 56, 57, 59, 60, 63, 74, 75, 78, 83, 84, 86, 96, 97, 98, 100, 103, 104, 107, 108, 111, 112, 113, 114, 116, 117, 120, 133, 144, 145, 147, 148, 149, 151, 153, 163, 175, 176, 178, 179, 181, 182, 183, 186, 188, 191, 192, 194, 195, 197, 198, 206, 207, 210, 214, 217, 222, 232, 233, 234, 236, 244, 245, 247, 253, 254, 256, 257, 260, 262, 269], "summary": {"covered_lines": 77, "num_statements": 92, "percent_covered": 83.69565217391305, "percent_covered_display": "84", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [79, 80, 193, 200, 201, 202, 211, 212, 218, 219, 225, 226, 227, 264, 265], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 16, 19, 20, 21, 22, 24, 27, 28, 30, 52, 173, 204, 229, 242, 251, 267], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/github/filters.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 16, 19, 20, 23, 106, 118, 120, 121, 122, 125, 126, 127, 130, 132, 133, 135, 137, 138, 139, 141, 142, 144, 147, 149, 153, 155, 161, 162, 163, 166, 167, 168, 171, 172, 173, 174, 177, 178, 179, 189, 190, 191, 194, 195, 196, 198, 199, 201, 203, 205, 207, 209, 210, 212, 215, 216, 218, 220, 222, 224, 226, 227, 230, 231, 232, 233, 236, 239, 240, 243, 244, 247, 248, 251, 252, 254, 256, 258, 265, 267, 268, 269, 271, 273, 274, 275, 276], "summary": {"covered_lines": 92, "num_statements": 95, "percent_covered": 96.84210526315789, "percent_covered_display": "97", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [150, 151, 278], "excluded_lines": [], "functions": {"GitHubFileFilter.__init__": {"executed_lines": [120, 121, 122, 125, 126, 127, 130, 132, 133], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubFileFilter._compile_exclusion_patterns": {"executed_lines": [137, 138, 139, 141, 142, 144, 147, 149, 153], "summary": {"covered_lines": 9, "num_statements": 11, "percent_covered": 81.81818181818181, "percent_covered_display": "82", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [150, 151], "excluded_lines": []}, "GitHubFileFilter.should_include_file": {"executed_lines": [161, 162, 163, 166, 167, 168, 171, 172, 173, 174, 177, 178, 179, 189, 190, 191, 194, 195, 196, 198, 199], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubFileFilter._is_excluded": {"executed_lines": [203, 205], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubFileFilter.filter_files": {"executed_lines": [209, 210, 212, 215, 216, 218, 220, 222], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubFileFilter.get_file_priority": {"executed_lines": [226, 227, 230, 231, 232, 233, 236, 239, 240, 243, 244, 247, 248, 251, 252, 254], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubFileFilter.get_filter_stats": {"executed_lines": [258], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubFileFilter.add_exclusion": {"executed_lines": [267, 268, 269], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubFileFilter.remove_exclusion": {"executed_lines": [273, 274, 275, 276], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [278], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 16, 19, 20, 23, 106, 118, 135, 155, 201, 207, 224, 256, 265, 271], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"GitHubFileFilter": {"executed_lines": [120, 121, 122, 125, 126, 127, 130, 132, 133, 137, 138, 139, 141, 142, 144, 147, 149, 153, 161, 162, 163, 166, 167, 168, 171, 172, 173, 174, 177, 178, 179, 189, 190, 191, 194, 195, 196, 198, 199, 203, 205, 209, 210, 212, 215, 216, 218, 220, 222, 226, 227, 230, 231, 232, 233, 236, 239, 240, 243, 244, 247, 248, 251, 252, 254, 258, 267, 268, 269, 273, 274, 275, 276], "summary": {"covered_lines": 73, "num_statements": 76, "percent_covered": 96.05263157894737, "percent_covered_display": "96", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [150, 151, 278], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 16, 19, 20, 23, 106, 118, 135, 155, 201, 207, 224, 256, 265, 271], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/github/metadata.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 17, 18, 20, 23, 24, 27, 81, 83, 85, 87, 88, 90, 96, 97, 98, 101, 102, 103, 106, 107, 108, 111, 113, 132, 134, 137, 139, 140, 141, 142, 143, 144, 148, 149, 153, 203, 204, 205, 207, 209, 211, 213, 214, 235, 236, 237, 239, 241, 242, 243, 246, 247, 248, 249, 251, 252, 253, 255, 257, 258, 260, 262, 268, 270, 271, 278, 285, 286, 315, 316, 319, 320, 322, 333, 342, 344, 346, 347, 348, 349, 350, 354, 356], "summary": {"covered_lines": 89, "num_statements": 134, "percent_covered": 66.41791044776119, "percent_covered_display": "66", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [91, 126, 127, 128, 145, 146, 147, 150, 206, 208, 217, 219, 220, 221, 230, 250, 259, 264, 265, 266, 272, 289, 302, 303, 324, 325, 326, 335, 336, 337, 338, 339, 340, 351, 352, 358, 359, 361, 363, 364, 378, 379, 391, 392, 393], "excluded_lines": [], "functions": {"GitHubMetadataExtractor.__init__": {"executed_lines": [83], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubMetadataExtractor.extract_file_metadata": {"executed_lines": [87, 88, 90, 96, 97, 98, 101, 102, 103, 106, 107, 108, 111, 113], "summary": {"covered_lines": 14, "num_statements": 18, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [91, 126, 127, 128], "excluded_lines": []}, "GitHubMetadataExtractor._detect_file_type": {"executed_lines": [134, 137, 139, 140, 141, 142, 143, 144, 148, 149, 153, 203, 204, 205, 207, 209], "summary": {"covered_lines": 16, "num_statements": 22, "percent_covered": 72.**************, "percent_covered_display": "73", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [145, 146, 147, 150, 206, 208], "excluded_lines": []}, "GitHubMetadataExtractor._extract_git_metadata": {"executed_lines": [213, 214, 235, 236, 237], "summary": {"covered_lines": 5, "num_statements": 10, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [217, 219, 220, 221, 230], "excluded_lines": []}, "GitHubMetadataExtractor._generate_file_summary": {"executed_lines": [241, 242, 243, 246, 247, 248, 249, 251, 252, 253, 255, 257, 258, 260, 262], "summary": {"covered_lines": 15, "num_statements": 20, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [250, 259, 264, 265, 266], "excluded_lines": []}, "GitHubMetadataExtractor.extract_repository_metadata": {"executed_lines": [270, 271, 278, 285, 286, 315, 316, 319, 320, 322], "summary": {"covered_lines": 10, "num_statements": 17, "percent_covered": 58.8235294117647, "percent_covered_display": "59", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [272, 289, 302, 303, 324, 325, 326], "excluded_lines": []}, "GitHubMetadataExtractor._get_remote_url": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [335, 336, 337, 338, 339, 340], "excluded_lines": []}, "GitHubMetadataExtractor._count_files_by_type": {"executed_lines": [344, 346, 347, 348, 349, 350, 354], "summary": {"covered_lines": 7, "num_statements": 9, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [351, 352], "excluded_lines": []}, "GitHubMetadataExtractor.get_commit_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [358, 359, 361, 363, 364, 378, 379, 391, 392, 393], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 17, 18, 20, 23, 24, 27, 81, 85, 132, 211, 239, 268, 333, 342, 356], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"GitHubMetadataExtractor": {"executed_lines": [83, 87, 88, 90, 96, 97, 98, 101, 102, 103, 106, 107, 108, 111, 113, 134, 137, 139, 140, 141, 142, 143, 144, 148, 149, 153, 203, 204, 205, 207, 209, 213, 214, 235, 236, 237, 241, 242, 243, 246, 247, 248, 249, 251, 252, 253, 255, 257, 258, 260, 262, 270, 271, 278, 285, 286, 315, 316, 319, 320, 322, 344, 346, 347, 348, 349, 350, 354], "summary": {"covered_lines": 68, "num_statements": 113, "percent_covered": 60.176991150442475, "percent_covered_display": "60", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [91, 126, 127, 128, 145, 146, 147, 150, 206, 208, 217, 219, 220, 221, 230, 250, 259, 264, 265, 266, 272, 289, 302, 303, 324, 325, 326, 335, 336, 337, 338, 339, 340, 351, 352, 358, 359, 361, 363, 364, 378, 379, 391, 392, 393], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 17, 18, 20, 23, 24, 27, 81, 85, 132, 211, 239, 268, 333, 342, 356], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/github/repository.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 17, 22, 25, 26, 28, 30, 31, 32, 34, 47, 112, 119, 159, 184, 214, 252], "summary": {"covered_lines": 22, "num_statements": 130, "percent_covered": 16.923076923076923, "percent_covered_display": "17", "missing_lines": 108, "excluded_lines": 0}, "missing_lines": [37, 38, 39, 45, 49, 51, 53, 54, 55, 58, 59, 60, 63, 66, 69, 73, 74, 76, 77, 80, 81, 82, 84, 85, 86, 87, 88, 96, 103, 104, 105, 114, 116, 117, 121, 122, 125, 126, 129, 130, 131, 132, 133, 134, 136, 137, 138, 139, 141, 143, 145, 146, 147, 149, 150, 151, 152, 161, 162, 163, 165, 166, 167, 169, 170, 172, 173, 175, 176, 177, 186, 187, 189, 190, 194, 195, 200, 201, 202, 203, 205, 206, 208, 209, 210, 216, 217, 219, 221, 222, 223, 229, 232, 234, 243, 244, 245, 254, 255, 256, 257, 258, 260, 261, 262, 263, 265, 266], "excluded_lines": [], "functions": {"GitHubRepositoryManager.__init__": {"executed_lines": [30, 31, 32], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "GitHubRepositoryManager._get_repository_local_path": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 4, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [37, 38, 39, 45], "excluded_lines": []}, "GitHubRepositoryManager.clone_repository": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [49, 51, 53, 54, 55, 58, 59, 60, 63, 66, 69, 73, 74, 76, 77, 80, 81, 82, 84, 85, 86, 87, 88, 96, 103, 104, 105], "excluded_lines": []}, "GitHubRepositoryManager._prepare_clone_url": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [114, 116, 117], "excluded_lines": []}, "GitHubRepositoryManager._update_repository": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 23, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 23, "excluded_lines": 0}, "missing_lines": [121, 122, 125, 126, 129, 130, 131, 132, 133, 134, 136, 137, 138, 139, 141, 143, 145, 146, 147, 149, 150, 151, 152], "excluded_lines": []}, "GitHubRepositoryManager.get_file_list": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [161, 162, 163, 165, 166, 167, 169, 170, 172, 173, 175, 176, 177], "excluded_lines": []}, "GitHubRepositoryManager.get_file_content": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [186, 187, 189, 190, 194, 195, 200, 201, 202, 203, 205, 206, 208, 209, 210], "excluded_lines": []}, "GitHubRepositoryManager.get_commit_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [216, 217, 219, 221, 222, 223, 229, 232, 234, 243, 244, 245], "excluded_lines": []}, "GitHubRepositoryManager.cleanup": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [254, 255, 256, 257, 258, 260, 261, 262, 263, 265, 266], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 17, 22, 25, 26, 28, 34, 47, 112, 119, 159, 184, 214, 252], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"GitHubRepositoryManager": {"executed_lines": [30, 31, 32], "summary": {"covered_lines": 3, "num_statements": 111, "percent_covered": 2.7027027027027026, "percent_covered_display": "3", "missing_lines": 108, "excluded_lines": 0}, "missing_lines": [37, 38, 39, 45, 49, 51, 53, 54, 55, 58, 59, 60, 63, 66, 69, 73, 74, 76, 77, 80, 81, 82, 84, 85, 86, 87, 88, 96, 103, 104, 105, 114, 116, 117, 121, 122, 125, 126, 129, 130, 131, 132, 133, 134, 136, 137, 138, 139, 141, 143, 145, 146, 147, 149, 150, 151, 152, 161, 162, 163, 165, 166, 167, 169, 170, 172, 173, 175, 176, 177, 186, 187, 189, 190, 194, 195, 200, 201, 202, 203, 205, 206, 208, 209, 210, 216, 217, 219, 221, 222, 223, 229, 232, 234, 243, 244, 245, 254, 255, 256, 257, 258, 260, 261, 262, 263, 265, 266], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 17, 22, 25, 26, 28, 34, 47, 112, 119, 159, 184, 214, 252], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/integration_example.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 16, 18, 21, 22, 24, 26, 29, 30, 31, 33, 35, 100, 104, 106, 108, 113, 114, 127, 128, 130, 131, 132, 134, 155, 162, 182], "summary": {"covered_lines": 31, "num_statements": 78, "percent_covered": 39.743589743589745, "percent_covered_display": "40", "missing_lines": 47, "excluded_lines": 2}, "missing_lines": [37, 39, 41, 42, 44, 45, 47, 50, 51, 53, 56, 57, 59, 62, 63, 65, 66, 68, 71, 87, 94, 96, 97, 98, 115, 125, 136, 137, 143, 144, 145, 146, 147, 149, 151, 152, 153, 157, 158, 159, 165, 168, 169, 173, 175, 176, 179], "excluded_lines": [182, 184], "functions": {"IntegratedIngestionPipeline.__init__": {"executed_lines": [26, 29, 30, 31, 33], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "IntegratedIngestionPipeline.process_repository": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [37, 39, 41, 42, 44, 45, 47, 50, 51, 53, 56, 57, 59, 62, 63, 65, 66, 68, 71, 87, 94, 96, 97, 98], "excluded_lines": []}, "IntegratedIngestionPipeline.search_repository": {"executed_lines": [104, 106, 108, 113, 114, 127, 128, 130, 131, 132], "summary": {"covered_lines": 10, "num_statements": 12, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [115, 125], "excluded_lines": []}, "IntegratedIngestionPipeline.get_comprehensive_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 11, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [136, 137, 143, 144, 145, 146, 147, 149, 151, 152, 153], "excluded_lines": []}, "IntegratedIngestionPipeline.reset_all_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [157, 158, 159], "excluded_lines": []}, "example_usage": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [165, 168, 169, 173, 175, 176, 179], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 16, 18, 21, 22, 24, 35, 100, 134, 155, 162, 182], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [182, 184]}}, "classes": {"IntegratedIngestionPipeline": {"executed_lines": [26, 29, 30, 31, 33, 104, 106, 108, 113, 114, 127, 128, 130, 131, 132], "summary": {"covered_lines": 15, "num_statements": 55, "percent_covered": 27.2**************, "percent_covered_display": "27", "missing_lines": 40, "excluded_lines": 0}, "missing_lines": [37, 39, 41, 42, 44, 45, 47, 50, 51, 53, 56, 57, 59, 62, 63, 65, 66, 68, 71, 87, 94, 96, 97, 98, 115, 125, 136, 137, 143, 144, 145, 146, 147, 149, 151, 152, 153, 157, 158, 159], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 16, 18, 21, 22, 24, 35, 100, 134, 155, 162, 182], "summary": {"covered_lines": 16, "num_statements": 23, "percent_covered": 69.56521739130434, "percent_covered_display": "70", "missing_lines": 7, "excluded_lines": 2}, "missing_lines": [165, 168, 169, 173, 175, 176, 179], "excluded_lines": [182, 184]}}}, "src/ingestion/loader.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 16, 17, 19, 22, 23, 26, 102, 104, 106, 107, 110, 112, 114, 115, 120, 126, 127, 136, 142, 145, 146, 147, 150, 152, 153, 205, 207, 210, 213, 215, 217, 218, 220, 225, 226, 227, 230, 235, 236, 238, 239, 246, 248, 249, 257, 260, 261, 262, 277, 280, 283, 287, 288, 290, 292, 313], "summary": {"covered_lines": 61, "num_statements": 122, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [116, 121, 128, 137, 155, 156, 159, 160, 161, 163, 164, 165, 167, 168, 171, 172, 174, 175, 178, 179, 180, 182, 183, 185, 186, 188, 189, 197, 198, 199, 200, 201, 211, 222, 231, 232, 240, 241, 243, 251, 252, 253, 254, 255, 265, 266, 267, 270, 271, 272, 275, 284, 294, 295, 296, 298, 306, 307, 308, 315, 316], "excluded_lines": [], "functions": {"UniversalFileLoader.__init__": {"executed_lines": [106, 107, 110], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "UniversalFileLoader.load_file": {"executed_lines": [114, 115, 120, 126, 127, 136, 142, 145, 146, 147, 150, 152, 153], "summary": {"covered_lines": 13, "num_statements": 45, "percent_covered": 28.88888888888889, "percent_covered_display": "29", "missing_lines": 32, "excluded_lines": 0}, "missing_lines": [116, 121, 128, 137, 155, 156, 159, 160, 161, 163, 164, 165, 167, 168, 171, 172, 174, 175, 178, 179, 180, 182, 183, 185, 186, 188, 189, 197, 198, 199, 200, 201], "excluded_lines": []}, "UniversalFileLoader.detect_encoding": {"executed_lines": [207, 210, 213, 215, 217, 218, 220, 225, 226, 227, 230, 235, 236, 238, 239, 246, 248, 249], "summary": {"covered_lines": 18, "num_statements": 30, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [211, 222, 231, 232, 240, 241, 243, 251, 252, 253, 254, 255], "excluded_lines": []}, "UniversalFileLoader.is_supported": {"executed_lines": [260, 261, 262], "summary": {"covered_lines": 3, "num_statements": 10, "percent_covered": 30.0, "percent_covered_display": "30", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [265, 266, 267, 270, 271, 272, 275], "excluded_lines": []}, "UniversalFileLoader._normalize_content": {"executed_lines": [280, 283, 287, 288, 290], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [284], "excluded_lines": []}, "UniversalFileLoader.get_file_info": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [294, 295, 296, 298, 306, 307, 308], "excluded_lines": []}, "UniversalFileLoader.clear_encoding_cache": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [315, 316], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 16, 17, 19, 22, 23, 26, 102, 104, 112, 205, 257, 277, 292, 313], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"UniversalFileLoader": {"executed_lines": [106, 107, 110, 114, 115, 120, 126, 127, 136, 142, 145, 146, 147, 150, 152, 153, 207, 210, 213, 215, 217, 218, 220, 225, 226, 227, 230, 235, 236, 238, 239, 246, 248, 249, 260, 261, 262, 280, 283, 287, 288, 290], "summary": {"covered_lines": 42, "num_statements": 103, "percent_covered": 40.77669902912621, "percent_covered_display": "41", "missing_lines": 61, "excluded_lines": 0}, "missing_lines": [116, 121, 128, 137, 155, 156, 159, 160, 161, 163, 164, 165, 167, 168, 171, 172, 174, 175, 178, 179, 180, 182, 183, 185, 186, 188, 189, 197, 198, 199, 200, 201, 211, 222, 231, 232, 240, 241, 243, 251, 252, 253, 254, 255, 265, 266, 267, 270, 271, 272, 275, 284, 294, 295, 296, 298, 306, 307, 308, 315, 316], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 16, 17, 19, 22, 23, 26, 102, 104, 112, 205, 257, 277, 292, 313], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/pipeline.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 23, 24, 26, 33, 34, 35, 38, 45, 47, 49, 51, 52, 54, 55, 58, 59, 60, 63, 65, 68, 69, 72, 73, 74, 76, 78, 79, 81, 87, 100, 102, 103, 106, 107, 108, 110, 115, 116, 121, 126, 129, 132, 134, 135, 146, 148, 150, 152, 154, 156, 157, 158, 160, 165, 169, 171, 173, 175, 177, 179, 181, 190, 194, 195, 196, 198, 199, 201, 204, 206, 208, 209, 213, 216, 217, 218, 220, 227, 229, 236, 238, 248, 249, 250, 252, 254, 259, 260, 262, 266, 269, 272, 275, 277], "summary": {"covered_lines": 104, "num_statements": 132, "percent_covered": 78.78787878787878, "percent_covered_display": "79", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [70, 71, 89, 90, 91, 111, 112, 117, 118, 119, 122, 123, 137, 138, 139, 161, 166, 183, 202, 210, 222, 223, 255, 256, 261, 263, 267, 273], "excluded_lines": [], "functions": {"DefaultChunkingPipeline.__init__": {"executed_lines": [33, 34, 35, 38, 45], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DefaultChunkingPipeline.process_files": {"executed_lines": [49, 51, 52, 54, 55, 58, 59, 60, 63, 65, 68, 69, 72, 73, 74, 76, 78, 79, 81, 87], "summary": {"covered_lines": 20, "num_statements": 25, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [70, 71, 89, 90, 91], "excluded_lines": []}, "DefaultChunkingPipeline.process_single_file": {"executed_lines": [102, 103, 106, 107, 108, 110, 115, 116, 121, 126, 129, 132, 134, 135], "summary": {"covered_lines": 14, "num_statements": 24, "percent_covered": 58.**************6, "percent_covered_display": "58", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [111, 112, 117, 118, 119, 122, 123, 137, 138, 139], "excluded_lines": []}, "DefaultChunkingPipeline.get_chunking_strategy": {"executed_lines": [148], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DefaultChunkingPipeline._post_process_chunks": {"executed_lines": [152, 154, 156, 157, 158, 160, 165, 169, 171, 173, 175], "summary": {"covered_lines": 11, "num_statements": 13, "percent_covered": 84.61538461538461, "percent_covered_display": "85", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [161, 166], "excluded_lines": []}, "DefaultChunkingPipeline.get_processing_stats": {"executed_lines": [179], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DefaultChunkingPipeline.reset_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [183], "excluded_lines": []}, "DefaultChunkingPipeline.estimate_processing_time": {"executed_lines": [194, 195, 196, 198, 199, 201, 204, 206, 208, 209, 213, 216, 217, 218, 220, 227, 229], "summary": {"covered_lines": 17, "num_statements": 21, "percent_covered": 80.95238095238095, "percent_covered_display": "81", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [202, 210, 222, 223], "excluded_lines": []}, "DefaultChunkingPipeline.validate_chunks": {"executed_lines": [238, 248, 249, 250, 252, 254, 259, 260, 262, 266, 269, 272, 275, 277], "summary": {"covered_lines": 14, "num_statements": 20, "percent_covered": 70.0, "percent_covered_display": "70", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [255, 256, 261, 263, 267, 273], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 23, 24, 26, 47, 100, 146, 150, 177, 181, 190, 236], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DefaultChunkingPipeline": {"executed_lines": [33, 34, 35, 38, 45, 49, 51, 52, 54, 55, 58, 59, 60, 63, 65, 68, 69, 72, 73, 74, 76, 78, 79, 81, 87, 102, 103, 106, 107, 108, 110, 115, 116, 121, 126, 129, 132, 134, 135, 148, 152, 154, 156, 157, 158, 160, 165, 169, 171, 173, 175, 179, 194, 195, 196, 198, 199, 201, 204, 206, 208, 209, 213, 216, 217, 218, 220, 227, 229, 238, 248, 249, 250, 252, 254, 259, 260, 262, 266, 269, 272, 275, 277], "summary": {"covered_lines": 83, "num_statements": 111, "percent_covered": 74.77477477477477, "percent_covered_display": "75", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [70, 71, 89, 90, 91, 111, 112, 117, 118, 119, 122, 123, 137, 138, 139, 161, 166, 183, 202, 210, 222, 223, 255, 256, 261, 263, 267, 273], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 15, 16, 17, 18, 20, 23, 24, 26, 47, 100, 146, 150, 177, 181, 190, 236], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/strategies/__init__.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 11, 12, 14], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/strategies/base.py": {"executed_lines": [1, 8, 9, 11, 13, 14, 16, 19, 20, 22, 24, 25, 26, 27, 28, 31, 32, 37, 39, 40, 41, 48, 50, 51, 53, 66, 67, 69, 84, 97, 150, 175, 219, 225, 226, 228, 230, 231, 232, 234, 237, 238, 239, 240, 242, 251, 256, 259, 260, 263, 264, 267, 268, 271, 293, 296, 298, 300], "summary": {"covered_lines": 55, "num_statements": 149, "percent_covered": 36.91275167785235, "percent_covered_display": "37", "missing_lines": 94, "excluded_lines": 0}, "missing_lines": [33, 34, 35, 42, 43, 46, 86, 87, 89, 90, 91, 92, 93, 95, 99, 100, 103, 104, 105, 106, 108, 109, 112, 113, 114, 115, 116, 119, 120, 121, 123, 124, 125, 126, 127, 128, 130, 131, 133, 134, 136, 137, 138, 139, 140, 142, 143, 145, 146, 148, 153, 156, 159, 160, 161, 162, 163, 165, 166, 167, 170, 171, 173, 177, 178, 180, 181, 183, 185, 186, 187, 190, 191, 193, 195, 196, 197, 199, 200, 201, 202, 203, 204, 206, 207, 208, 210, 213, 215, 217, 221, 222, 253, 254], "excluded_lines": [], "functions": {"BaseChunkingStrategy.__init__": {"executed_lines": [24, 25, 26, 27, 28, 31, 32], "summary": {"covered_lines": 7, "num_statements": 10, "percent_covered": 70.0, "percent_covered_display": "70", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [33, 34, 35], "excluded_lines": []}, "BaseChunkingStrategy.count_tokens": {"executed_lines": [39, 40, 41], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [42, 43, 46], "excluded_lines": []}, "BaseChunkingStrategy.generate_chunk_id": {"executed_lines": [50, 51], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseChunkingStrategy.create_chunk": {"executed_lines": [66, 67, 69], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "BaseChunkingStrategy.split_by_lines": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [86, 87, 89, 90, 91, 92, 93, 95], "excluded_lines": []}, "BaseChunkingStrategy.split_by_tokens": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 36, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 36, "excluded_lines": 0}, "missing_lines": [99, 100, 103, 104, 105, 106, 108, 109, 112, 113, 114, 115, 116, 119, 120, 121, 123, 124, 125, 126, 127, 128, 130, 131, 133, 134, 136, 137, 138, 139, 140, 142, 143, 145, 146, 148], "excluded_lines": []}, "BaseChunkingStrategy._split_into_sentences": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [153, 156, 159, 160, 161, 162, 163, 165, 166, 167, 170, 171, 173], "excluded_lines": []}, "BaseChunkingStrategy.add_overlap": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 27, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 27, "excluded_lines": 0}, "missing_lines": [177, 178, 180, 181, 183, 185, 186, 187, 190, 191, 193, 195, 196, 197, 199, 200, 201, 202, 203, 204, 206, 207, 208, 210, 213, 215, 217], "excluded_lines": []}, "BaseChunkingStrategy.estimate_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [221, 222], "excluded_lines": []}, "ChunkingStrategyFactory.__init__": {"executed_lines": [230, 231, 232], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChunkingStrategyFactory._register_default_strategies": {"executed_lines": [237, 238, 239, 240, 242], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChunkingStrategyFactory.register_strategy": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [253, 254], "excluded_lines": []}, "ChunkingStrategyFactory.get_strategy": {"executed_lines": [259, 260, 263, 264, 267, 268, 271, 293, 296], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChunkingStrategyFactory.get_available_strategies": {"executed_lines": [300], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 13, 14, 16, 19, 20, 22, 37, 48, 53, 84, 97, 150, 175, 219, 225, 226, 228, 234, 251, 256, 298], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"BaseChunkingStrategy": {"executed_lines": [24, 25, 26, 27, 28, 31, 32, 39, 40, 41, 50, 51, 66, 67, 69], "summary": {"covered_lines": 15, "num_statements": 107, "percent_covered": 14.018691588785046, "percent_covered_display": "14", "missing_lines": 92, "excluded_lines": 0}, "missing_lines": [33, 34, 35, 42, 43, 46, 86, 87, 89, 90, 91, 92, 93, 95, 99, 100, 103, 104, 105, 106, 108, 109, 112, 113, 114, 115, 116, 119, 120, 121, 123, 124, 125, 126, 127, 128, 130, 131, 133, 134, 136, 137, 138, 139, 140, 142, 143, 145, 146, 148, 153, 156, 159, 160, 161, 162, 163, 165, 166, 167, 170, 171, 173, 177, 178, 180, 181, 183, 185, 186, 187, 190, 191, 193, 195, 196, 197, 199, 200, 201, 202, 203, 204, 206, 207, 208, 210, 213, 215, 217, 221, 222], "excluded_lines": []}, "ChunkingStrategyFactory": {"executed_lines": [230, 231, 232, 237, 238, 239, 240, 242, 259, 260, 263, 264, 267, 268, 271, 293, 296, 300], "summary": {"covered_lines": 18, "num_statements": 20, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [253, 254], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 13, 14, 16, 19, 20, 22, 37, 48, 53, 84, 97, 150, 175, 219, 225, 226, 228, 234, 251, 256, 298], "summary": {"covered_lines": 22, "num_statements": 22, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/strategies/code.py": {"executed_lines": [1, 9, 10, 12, 13, 15, 18, 19, 22, 72, 74, 76, 78, 79, 81, 82, 104, 106, 108, 109, 113, 115, 117, 123, 124, 125, 126, 128, 129, 135, 137, 138, 139, 140, 161, 163, 164, 166, 167, 168, 170, 171, 174, 175, 176, 177, 180, 183, 184, 186, 187, 188, 190, 191, 201, 204, 205, 206, 207, 208, 209, 210, 212, 214, 217, 218, 219, 221, 222, 231, 234, 235, 238, 249, 250, 251, 253, 255, 257, 259, 260, 264, 266, 267, 268, 271, 321, 324, 327, 329, 330, 332, 335, 345, 355], "summary": {"covered_lines": 93, "num_statements": 136, "percent_covered": 68.38235294117646, "percent_covered_display": "68", "missing_lines": 43, "excluded_lines": 0}, "missing_lines": [110, 120, 131, 132, 133, 143, 145, 159, 258, 261, 262, 269, 274, 275, 276, 277, 279, 280, 281, 282, 283, 285, 288, 289, 300, 302, 303, 306, 307, 319, 328, 358, 364, 365, 367, 368, 369, 370, 371, 374, 375, 376, 378], "excluded_lines": [], "functions": {"CodeChunkingStrategy.get_strategy_name": {"executed_lines": [74], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CodeChunkingStrategy.is_applicable": {"executed_lines": [78, 79, 81, 82, 104], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CodeChunkingStrategy.chunk_content": {"executed_lines": [108, 109, 113, 115, 117, 123, 124, 125, 126, 128, 129], "summary": {"covered_lines": 11, "num_statements": 16, "percent_covered": 68.75, "percent_covered_display": "69", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [110, 120, 131, 132, 133], "excluded_lines": []}, "CodeChunkingStrategy._detect_language": {"executed_lines": [137, 138, 139, 140], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.***************, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [143, 145, 159], "excluded_lines": []}, "CodeChunkingStrategy._parse_code_structure": {"executed_lines": [163, 164, 166, 167, 168, 170, 171, 174, 175, 176, 177, 180, 183, 184, 186, 187, 188, 190, 191, 201, 204, 205, 206, 207, 208, 209, 210, 212, 214, 217, 218, 219, 221, 222, 231, 234, 235, 238, 249, 250, 251, 253], "summary": {"covered_lines": 42, "num_statements": 42, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "CodeChunkingStrategy._match_pattern": {"executed_lines": [257, 259, 260], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [258, 261, 262], "excluded_lines": []}, "CodeChunkingStrategy._is_comment": {"executed_lines": [266, 267, 268], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [269], "excluded_lines": []}, "CodeChunkingStrategy._generic_code_chunking": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 18, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [274, 275, 276, 277, 279, 280, 281, 282, 283, 285, 288, 289, 300, 302, 303, 306, 307, 319], "excluded_lines": []}, "CodeChunkingStrategy._create_code_chunk": {"executed_lines": [324, 327, 329, 330, 332, 335, 345], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [328], "excluded_lines": []}, "CodeChunkingStrategy.estimate_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [358, 364, 365, 367, 368, 369, 370, 371, 374, 375, 376, 378], "excluded_lines": []}, "": {"executed_lines": [1, 9, 10, 12, 13, 15, 18, 19, 22, 72, 76, 106, 135, 161, 255, 264, 271, 321, 355], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"CodeChunkingStrategy": {"executed_lines": [74, 78, 79, 81, 82, 104, 108, 109, 113, 115, 117, 123, 124, 125, 126, 128, 129, 137, 138, 139, 140, 163, 164, 166, 167, 168, 170, 171, 174, 175, 176, 177, 180, 183, 184, 186, 187, 188, 190, 191, 201, 204, 205, 206, 207, 208, 209, 210, 212, 214, 217, 218, 219, 221, 222, 231, 234, 235, 238, 249, 250, 251, 253, 257, 259, 260, 266, 267, 268, 324, 327, 329, 330, 332, 335, 345], "summary": {"covered_lines": 76, "num_statements": 119, "percent_covered": 63.865546218487395, "percent_covered_display": "64", "missing_lines": 43, "excluded_lines": 0}, "missing_lines": [110, 120, 131, 132, 133, 143, 145, 159, 258, 261, 262, 269, 274, 275, 276, 277, 279, 280, 281, 282, 283, 285, 288, 289, 300, 302, 303, 306, 307, 319, 328, 358, 364, 365, 367, 368, 369, 370, 371, 374, 375, 376, 378], "excluded_lines": []}, "": {"executed_lines": [1, 9, 10, 12, 13, 15, 18, 19, 22, 72, 76, 106, 135, 161, 255, 264, 271, 321, 355], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/strategies/config.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 16, 18, 21, 22, 24, 26, 28, 30, 31, 32, 34, 36, 37, 40, 43, 44, 57, 58, 65, 67, 68, 69, 74, 83, 93, 98, 104, 108, 110, 112, 113, 115, 117, 120, 121, 122, 124, 126, 136, 137, 152, 154, 156, 158, 159, 160, 161, 162, 164, 166, 183, 222, 267, 290, 311, 337, 363], "summary": {"covered_lines": 62, "num_statements": 208, "percent_covered": 29.807692307692307, "percent_covered_display": "30", "missing_lines": 146, "excluded_lines": 0}, "missing_lines": [38, 45, 46, 47, 48, 49, 50, 51, 52, 55, 60, 61, 63, 70, 71, 72, 76, 77, 78, 79, 80, 81, 85, 87, 88, 89, 90, 91, 95, 96, 101, 102, 141, 150, 168, 169, 170, 171, 172, 173, 174, 177, 178, 179, 180, 181, 185, 186, 187, 188, 190, 191, 194, 195, 197, 198, 199, 200, 203, 204, 205, 207, 209, 210, 212, 215, 216, 217, 218, 220, 224, 225, 226, 227, 229, 230, 233, 234, 235, 236, 239, 240, 242, 243, 244, 245, 248, 249, 250, 252, 254, 255, 257, 260, 261, 262, 263, 265, 271, 273, 274, 276, 286, 288, 292, 293, 295, 296, 297, 298, 300, 301, 303, 305, 306, 307, 309, 313, 314, 316, 317, 318, 320, 322, 332, 333, 335, 340, 341, 342, 344, 345, 346, 348, 358, 359, 361, 366, 369, 372, 373, 374, 377, 378, 379, 381], "excluded_lines": [], "functions": {"ConfigChunkingStrategy.get_strategy_name": {"executed_lines": [26], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigChunkingStrategy.is_applicable": {"executed_lines": [30, 31, 32], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ConfigChunkingStrategy.chunk_content": {"executed_lines": [36, 37, 40, 43, 44, 57, 58], "summary": {"covered_lines": 7, "num_statements": 20, "percent_covered": 35.0, "percent_covered_display": "35", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [38, 45, 46, 47, 48, 49, 50, 51, 52, 55, 60, 61, 63], "excluded_lines": []}, "ConfigChunkingStrategy._chunk_json": {"executed_lines": [67, 68, 69], "summary": {"covered_lines": 3, "num_statements": 6, "percent_covered": 50.0, "percent_covered_display": "50", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [70, 71, 72], "excluded_lines": []}, "ConfigChunkingStrategy._chunk_yaml": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [76, 77, 78, 79, 80, 81], "excluded_lines": []}, "ConfigChunkingStrategy._chunk_toml": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [85, 87, 88, 89, 90, 91], "excluded_lines": []}, "ConfigChunkingStrategy._chunk_ini": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [95, 96], "excluded_lines": []}, "ConfigChunkingStrategy._chunk_properties": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [101, 102], "excluded_lines": []}, "ConfigChunkingStrategy._chunk_structured_data": {"executed_lines": [108, 110, 112, 113, 115, 117, 120, 121, 122, 124, 126, 136, 137, 152], "summary": {"covered_lines": 14, "num_statements": 16, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [141, 150], "excluded_lines": []}, "ConfigChunkingStrategy._extract_section_content": {"executed_lines": [156, 158, 159, 160, 161, 162, 164, 166], "summary": {"covered_lines": 8, "num_statements": 20, "percent_covered": 40.0, "percent_covered_display": "40", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [168, 169, 170, 171, 172, 173, 174, 177, 178, 179, 180, 181], "excluded_lines": []}, "ConfigChunkingStrategy._parse_toml_sections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 24, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 24, "excluded_lines": 0}, "missing_lines": [185, 186, 187, 188, 190, 191, 194, 195, 197, 198, 199, 200, 203, 204, 205, 207, 209, 210, 212, 215, 216, 217, 218, 220], "excluded_lines": []}, "ConfigChunkingStrategy._parse_ini_sections": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 28, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 28, "excluded_lines": 0}, "missing_lines": [224, 225, 226, 227, 229, 230, 233, 234, 235, 236, 239, 240, 242, 243, 244, 245, 248, 249, 250, 252, 254, 255, 257, 260, 261, 262, 263, 265], "excluded_lines": []}, "ConfigChunkingStrategy._create_section_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 6, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [271, 273, 274, 276, 286, 288], "excluded_lines": []}, "ConfigChunkingStrategy._group_properties_by_prefix": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 13, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 13, "excluded_lines": 0}, "missing_lines": [292, 293, 295, 296, 297, 298, 300, 301, 303, 305, 306, 307, 309], "excluded_lines": []}, "ConfigChunkingStrategy._create_property_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [313, 314, 316, 317, 318, 320, 322, 332, 333, 335], "excluded_lines": []}, "ConfigChunkingStrategy._chunk_generic": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [340, 341, 342, 344, 345, 346, 348, 358, 359, 361], "excluded_lines": []}, "ConfigChunkingStrategy.estimate_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [366, 369, 372, 373, 374, 377, 378, 379, 381], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 16, 18, 21, 22, 24, 28, 34, 65, 74, 83, 93, 98, 104, 154, 183, 222, 267, 290, 311, 337, 363], "summary": {"covered_lines": 26, "num_statements": 26, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ConfigChunkingStrategy": {"executed_lines": [26, 30, 31, 32, 36, 37, 40, 43, 44, 57, 58, 67, 68, 69, 108, 110, 112, 113, 115, 117, 120, 121, 122, 124, 126, 136, 137, 152, 156, 158, 159, 160, 161, 162, 164, 166], "summary": {"covered_lines": 36, "num_statements": 182, "percent_covered": 19.78021978021978, "percent_covered_display": "20", "missing_lines": 146, "excluded_lines": 0}, "missing_lines": [38, 45, 46, 47, 48, 49, 50, 51, 52, 55, 60, 61, 63, 70, 71, 72, 76, 77, 78, 79, 80, 81, 85, 87, 88, 89, 90, 91, 95, 96, 101, 102, 141, 150, 168, 169, 170, 171, 172, 173, 174, 177, 178, 179, 180, 181, 185, 186, 187, 188, 190, 191, 194, 195, 197, 198, 199, 200, 203, 204, 205, 207, 209, 210, 212, 215, 216, 217, 218, 220, 224, 225, 226, 227, 229, 230, 233, 234, 235, 236, 239, 240, 242, 243, 244, 245, 248, 249, 250, 252, 254, 255, 257, 260, 261, 262, 263, 265, 271, 273, 274, 276, 286, 288, 292, 293, 295, 296, 297, 298, 300, 301, 303, 305, 306, 307, 309, 313, 314, 316, 317, 318, 320, 322, 332, 333, 335, 340, 341, 342, 344, 345, 346, 348, 358, 359, 361, 366, 369, 372, 373, 374, 377, 378, 379, 381], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 16, 18, 21, 22, 24, 28, 34, 65, 74, 83, 93, 98, 104, 154, 183, 222, 267, 290, 311, 337, 363], "summary": {"covered_lines": 26, "num_statements": 26, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/strategies/markdown.py": {"executed_lines": [1, 8, 9, 11, 13, 14, 16, 19, 20, 22, 24, 27, 37, 39, 41, 43, 44, 46, 48, 49, 53, 56, 57, 58, 59, 62, 63, 65, 66, 72, 74, 75, 76, 77, 79, 81, 83, 85, 86, 87, 88, 91, 92, 94, 102, 105, 117, 120, 121, 122, 123, 125, 127, 129, 132, 133, 134, 135, 137, 138, 140, 142, 144, 147, 150, 155, 157, 167, 169, 170, 173, 174, 176, 177, 178, 180, 182, 183, 184, 186, 192, 202, 204, 206, 209, 212, 215, 253, 275], "summary": {"covered_lines": 87, "num_statements": 132, "percent_covered": 65.9090909090909, "percent_covered_display": "66", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [50, 68, 69, 70, 107, 115, 217, 218, 220, 221, 224, 226, 228, 231, 234, 235, 237, 238, 239, 241, 249, 251, 255, 256, 257, 259, 260, 262, 263, 264, 265, 267, 268, 270, 271, 273, 277, 279, 280, 281, 282, 283, 286, 287, 289], "excluded_lines": [], "functions": {"MarkdownChunkingStrategy.__init__": {"executed_lines": [24, 27], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MarkdownChunkingStrategy.get_strategy_name": {"executed_lines": [39], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MarkdownChunkingStrategy.is_applicable": {"executed_lines": [43, 44], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MarkdownChunkingStrategy.chunk_content": {"executed_lines": [48, 49, 53, 56, 57, 58, 59, 62, 63, 65, 66], "summary": {"covered_lines": 11, "num_statements": 15, "percent_covered": 73.**************, "percent_covered_display": "73", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [50, 68, 69, 70], "excluded_lines": []}, "MarkdownChunkingStrategy._parse_markdown_structure": {"executed_lines": [74, 75, 76, 77, 79, 81, 83, 85, 86, 87, 88, 91, 92, 94, 102, 105, 117, 120, 121, 122, 123, 125], "summary": {"covered_lines": 22, "num_statements": 24, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [107, 115], "excluded_lines": []}, "MarkdownChunkingStrategy._get_parent_headers": {"executed_lines": [129, 132, 133, 134, 135, 137, 138, 140], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MarkdownChunkingStrategy._create_section_chunk": {"executed_lines": [144, 147, 150, 155, 157], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MarkdownChunkingStrategy._extract_code_blocks": {"executed_lines": [169, 170, 173, 174, 176, 177, 178, 180, 182, 183, 184, 186, 192, 202, 204], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MarkdownChunkingStrategy._remove_code_blocks": {"executed_lines": [209, 212], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "MarkdownChunkingStrategy._split_large_section": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 16, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 16, "excluded_lines": 0}, "missing_lines": [217, 218, 220, 221, 224, 226, 228, 231, 234, 235, 237, 238, 239, 241, 249, 251], "excluded_lines": []}, "MarkdownChunkingStrategy._group_paragraphs_into_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 14, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 14, "excluded_lines": 0}, "missing_lines": [255, 256, 257, 259, 260, 262, 263, 264, 265, 267, 268, 270, 271, 273], "excluded_lines": []}, "MarkdownChunkingStrategy.estimate_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 9, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [277, 279, 280, 281, 282, 283, 286, 287, 289], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 13, 14, 16, 19, 20, 22, 37, 41, 46, 72, 127, 142, 167, 206, 215, 253, 275], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"MarkdownChunkingStrategy": {"executed_lines": [24, 27, 39, 43, 44, 48, 49, 53, 56, 57, 58, 59, 62, 63, 65, 66, 74, 75, 76, 77, 79, 81, 83, 85, 86, 87, 88, 91, 92, 94, 102, 105, 117, 120, 121, 122, 123, 125, 129, 132, 133, 134, 135, 137, 138, 140, 144, 147, 150, 155, 157, 169, 170, 173, 174, 176, 177, 178, 180, 182, 183, 184, 186, 192, 202, 204, 209, 212], "summary": {"covered_lines": 68, "num_statements": 113, "percent_covered": 60.176991150442475, "percent_covered_display": "60", "missing_lines": 45, "excluded_lines": 0}, "missing_lines": [50, 68, 69, 70, 107, 115, 217, 218, 220, 221, 224, 226, 228, 231, 234, 235, 237, 238, 239, 241, 249, 251, 255, 256, 257, 259, 260, 262, 263, 264, 265, 267, 268, 270, 271, 273, 277, 279, 280, 281, 282, 283, 286, 287, 289], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 13, 14, 16, 19, 20, 22, 37, 41, 46, 72, 127, 142, 167, 206, 215, 253, 275], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/strategies/text.py": {"executed_lines": [1, 8, 9, 11, 12, 14, 17, 18, 20, 22, 24, 26, 29, 30, 33, 37, 41, 42, 48, 50, 51, 52, 55, 58, 61, 65, 66, 67, 69, 71, 72, 73, 76, 86, 87, 89, 90, 96, 99, 102, 103, 104, 105, 106, 108, 110, 112, 115, 116, 117, 119, 120, 123, 134, 144, 145, 148, 149, 151, 153, 199], "summary": {"covered_lines": 59, "num_statements": 106, "percent_covered": 55.660377358490564, "percent_covered_display": "56", "missing_lines": 47, "excluded_lines": 0}, "missing_lines": [34, 38, 62, 92, 93, 94, 113, 125, 126, 127, 128, 131, 132, 136, 137, 139, 140, 156, 158, 160, 163, 164, 165, 167, 168, 170, 172, 173, 174, 175, 178, 179, 181, 183, 184, 186, 187, 190, 191, 194, 195, 197, 201, 203, 204, 206, 207], "excluded_lines": [], "functions": {"TextChunkingStrategy.get_strategy_name": {"executed_lines": [22], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TextChunkingStrategy.is_applicable": {"executed_lines": [26, 29, 30, 33, 37, 41, 42], "summary": {"covered_lines": 7, "num_statements": 9, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [34, 38], "excluded_lines": []}, "TextChunkingStrategy.chunk_content": {"executed_lines": [50, 51, 52, 55, 58, 61, 65, 66, 67, 69, 71, 72, 73, 76, 86, 87, 89, 90], "summary": {"covered_lines": 18, "num_statements": 22, "percent_covered": 81.81818181818181, "percent_covered_display": "82", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [62, 92, 93, 94], "excluded_lines": []}, "TextChunkingStrategy._split_into_paragraphs": {"executed_lines": [99, 102, 103, 104, 105, 106, 108], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TextChunkingStrategy._group_paragraphs_into_chunks": {"executed_lines": [112, 115, 116, 117, 119, 120, 123, 134, 144, 145, 148, 149, 151], "summary": {"covered_lines": 13, "num_statements": 24, "percent_covered": 54.166666666666664, "percent_covered_display": "54", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [113, 125, 126, 127, 128, 131, 132, 136, 137, 139, 140], "excluded_lines": []}, "TextChunkingStrategy._split_large_paragraph": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 25, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 25, "excluded_lines": 0}, "missing_lines": [156, 158, 160, 163, 164, 165, 167, 168, 170, 172, 173, 174, 175, 178, 179, 181, 183, 184, 186, 187, 190, 191, 194, 195, 197], "excluded_lines": []}, "TextChunkingStrategy.estimate_chunks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 5, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [201, 203, 204, 206, 207], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 12, 14, 17, 18, 20, 24, 48, 96, 110, 153, 199], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TextChunkingStrategy": {"executed_lines": [22, 26, 29, 30, 33, 37, 41, 42, 50, 51, 52, 55, 58, 61, 65, 66, 67, 69, 71, 72, 73, 76, 86, 87, 89, 90, 99, 102, 103, 104, 105, 106, 108, 112, 115, 116, 117, 119, 120, 123, 134, 144, 145, 148, 149, 151], "summary": {"covered_lines": 46, "num_statements": 93, "percent_covered": 49.46236559139785, "percent_covered_display": "49", "missing_lines": 47, "excluded_lines": 0}, "missing_lines": [34, 38, 62, 92, 93, 94, 113, 125, 126, 127, 128, 131, 132, 136, 137, 139, 140, 156, 158, 160, 163, 164, 165, 167, 168, 170, 172, 173, 174, 175, 178, 179, 181, 183, 184, 186, 187, 190, 191, 194, 195, 197, 201, 203, 204, 206, 207], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 12, 14, 17, 18, 20, 24, 48, 96, 110, 153, 199], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/vector/__init__.py": {"executed_lines": [1, 8, 9, 11], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 11], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 11], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/vector/chroma.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 15, 16, 26, 28, 31, 32, 34, 36, 37, 38, 41, 44, 45, 48, 55, 57, 59, 60, 62, 68, 70, 75, 77, 79, 80, 82, 84, 85, 87, 89, 90, 93, 94, 103, 105, 110, 111, 117, 118, 120, 121, 124, 125, 127, 129, 130, 133, 136, 137, 138, 139, 142, 145, 147, 148, 150, 157, 161, 162, 165, 166, 167, 170, 177, 180, 182, 183, 193, 196, 198, 202, 204, 205, 212, 214, 216, 217, 219, 220, 225, 227, 229, 234, 244, 245, 249, 257, 270, 277, 280, 282, 283, 285, 286, 287, 289, 290, 297, 299, 300, 302, 304, 305, 308, 309, 310, 311, 313, 319, 321, 322, 325, 328, 330, 342, 358, 375, 377, 379, 381, 388], "summary": {"covered_lines": 125, "num_statements": 180, "percent_covered": 69.44444444444444, "percent_covered_display": "69", "missing_lines": 55, "excluded_lines": 0}, "missing_lines": [72, 73, 96, 97, 140, 152, 153, 154, 155, 207, 208, 209, 210, 222, 247, 292, 293, 294, 295, 315, 316, 317, 338, 339, 340, 344, 345, 347, 351, 352, 354, 355, 356, 360, 361, 362, 365, 366, 368, 369, 371, 372, 373, 390, 391, 392, 395, 397, 398, 400, 402, 403, 405, 406, 407], "excluded_lines": [], "functions": {"ChromaVectorStore.__init__": {"executed_lines": [36, 37, 38, 41, 44, 45, 48, 55], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChromaVectorStore._get_client": {"executed_lines": [59, 60, 62, 68, 70, 75], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [72, 73], "excluded_lines": []}, "ChromaVectorStore._get_collection": {"executed_lines": [79, 80, 82, 84, 85, 87, 89, 90, 93, 94, 103], "summary": {"covered_lines": 11, "num_statements": 13, "percent_covered": 84.61538461538461, "percent_covered_display": "85", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [96, 97], "excluded_lines": []}, "ChromaVectorStore.add_embeddings": {"executed_lines": [110, 111, 117, 118, 120, 121, 124, 125, 127, 129, 130, 133, 136, 137, 138, 139, 142, 145, 147, 148, 150], "summary": {"covered_lines": 21, "num_statements": 26, "percent_covered": 80.76923076923077, "percent_covered_display": "81", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [140, 152, 153, 154, 155], "excluded_lines": []}, "ChromaVectorStore.search_similar": {"executed_lines": [161, 162, 165, 166, 167, 170, 177, 180, 182, 183, 193, 196, 198, 202, 204, 205], "summary": {"covered_lines": 16, "num_statements": 20, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [207, 208, 209, 210], "excluded_lines": []}, "ChromaVectorStore._build_where_clause": {"executed_lines": [214, 216, 217, 219, 220, 225, 227], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [222], "excluded_lines": []}, "ChromaVectorStore._reconstruct_embedded_chunk": {"executed_lines": [234, 244, 245, 249, 257, 270, 277], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [247], "excluded_lines": []}, "ChromaVectorStore.delete_embeddings": {"executed_lines": [282, 283, 285, 286, 287, 289, 290], "summary": {"covered_lines": 7, "num_statements": 11, "percent_covered": 63.63636363636363, "percent_covered_display": "64", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [292, 293, 294, 295], "excluded_lines": []}, "ChromaVectorStore.get_embedding": {"executed_lines": [299, 300, 302, 304, 305, 308, 309, 310, 311, 313], "summary": {"covered_lines": 10, "num_statements": 13, "percent_covered": 76.92307692307692, "percent_covered_display": "77", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [315, 316, 317], "excluded_lines": []}, "ChromaVectorStore.get_collection_info": {"executed_lines": [321, 322, 325, 328, 330], "summary": {"covered_lines": 5, "num_statements": 8, "percent_covered": 62.5, "percent_covered_display": "62", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [338, 339, 340], "excluded_lines": []}, "ChromaVectorStore.create_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 8, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [344, 345, 347, 351, 352, 354, 355, 356], "excluded_lines": []}, "ChromaVectorStore.delete_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [360, 361, 362, 365, 366, 368, 369, 371, 372, 373], "excluded_lines": []}, "ChromaVectorStore.get_stats": {"executed_lines": [377], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChromaVectorStore.reset_stats": {"executed_lines": [381], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ChromaVectorStore.backup_collection": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 12, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [390, 391, 392, 395, 397, 398, 400, 402, 403, 405, 406, 407], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 15, 16, 26, 28, 31, 32, 34, 57, 77, 105, 157, 212, 229, 280, 297, 319, 342, 358, 375, 379, 388], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ChromaVectorStore": {"executed_lines": [36, 37, 38, 41, 44, 45, 48, 55, 59, 60, 62, 68, 70, 75, 79, 80, 82, 84, 85, 87, 89, 90, 93, 94, 103, 110, 111, 117, 118, 120, 121, 124, 125, 127, 129, 130, 133, 136, 137, 138, 139, 142, 145, 147, 148, 150, 161, 162, 165, 166, 167, 170, 177, 180, 182, 183, 193, 196, 198, 202, 204, 205, 214, 216, 217, 219, 220, 225, 227, 234, 244, 245, 249, 257, 270, 277, 282, 283, 285, 286, 287, 289, 290, 299, 300, 302, 304, 305, 308, 309, 310, 311, 313, 321, 322, 325, 328, 330, 377, 381], "summary": {"covered_lines": 100, "num_statements": 155, "percent_covered": 64.51612903225806, "percent_covered_display": "65", "missing_lines": 55, "excluded_lines": 0}, "missing_lines": [72, 73, 96, 97, 140, 152, 153, 154, 155, 207, 208, 209, 210, 222, 247, 292, 293, 294, 295, 315, 316, 317, 338, 339, 340, 344, 345, 347, 351, 352, 354, 355, 356, 360, 361, 362, 365, 366, 368, 369, 371, 372, 373, 390, 391, 392, 395, 397, 398, 400, 402, 403, 405, 406, 407], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 15, 16, 26, 28, 31, 32, 34, 57, 77, 105, 157, 212, 229, 280, 297, 319, 342, 358, 375, 379, 388], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/ingestion/vector/factory.py": {"executed_lines": [1, 8, 9, 11, 12, 13, 15, 18, 19, 21, 22, 24, 26, 27, 29, 30, 35, 40, 45, 46, 48, 50, 51, 53, 55, 57, 58, 60, 63, 66, 67, 69, 100, 102, 103, 105, 106, 107, 108], "summary": {"covered_lines": 37, "num_statements": 41, "percent_covered": 90.2439024390244, "percent_covered_display": "90", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [32, 37, 61, 64], "excluded_lines": [], "functions": {"VectorStoreFactory.create_vector_store": {"executed_lines": [24, 26, 27, 29, 30, 35, 40], "summary": {"covered_lines": 7, "num_statements": 9, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [32, 37], "excluded_lines": []}, "VectorStoreFactory.get_available_providers": {"executed_lines": [48], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VectorStoreFactory.validate_provider_config": {"executed_lines": [53, 55, 57, 58, 60, 63], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [61, 64], "excluded_lines": []}, "VectorStoreFactory.get_provider_info": {"executed_lines": [69, 100], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "VectorStoreFactory.list_providers": {"executed_lines": [105, 106, 107, 108], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 12, 13, 15, 18, 19, 21, 22, 45, 46, 50, 51, 66, 67, 102, 103], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"VectorStoreFactory": {"executed_lines": [24, 26, 27, 29, 30, 35, 40, 48, 53, 55, 57, 58, 60, 63, 69, 100, 105, 106, 107, 108], "summary": {"covered_lines": 20, "num_statements": 24, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [32, 37, 61, 64], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 12, 13, 15, 18, 19, 21, 22, 45, 46, 50, 51, 66, 67, 102, 103], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/main.py": {"executed_lines": [1, 5, 6, 7, 9, 10, 11, 12, 13, 15, 24, 25, 28, 29, 32, 35, 36, 37, 41, 42, 44, 45, 46, 47, 48, 51, 52, 54, 55, 56, 57, 58, 59, 60, 63, 64, 66, 67, 68, 69, 72, 73, 75, 76, 77, 78, 79, 80, 83, 84, 86, 87, 88, 89, 90, 93, 94, 125, 135, 145, 147, 150, 152, 154, 155, 156, 163, 164, 166, 169, 170, 172, 180, 181, 183, 185, 186, 187, 188, 189, 191, 192, 199, 200, 207, 210, 213, 214, 215, 217, 221, 234, 235, 237, 238, 240, 241, 244, 247, 252, 255, 265, 268, 271, 280, 283, 292, 296, 299, 301, 316, 317, 319, 320, 322, 323, 325, 326, 329, 334, 336, 338, 352, 353, 359], "summary": {"covered_lines": 118, "num_statements": 157, "percent_covered": 75.1592356687898, "percent_covered_display": "75", "missing_lines": 39, "excluded_lines": 2}, "missing_lines": [98, 100, 102, 103, 104, 107, 109, 111, 113, 114, 115, 118, 119, 120, 121, 148, 159, 193, 194, 196, 201, 203, 204, 205, 218, 219, 229, 230, 231, 293, 297, 311, 312, 313, 347, 348, 349, 355, 356], "excluded_lines": [359, 360], "functions": {"lifespan": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 15, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 15, "excluded_lines": 0}, "missing_lines": [98, 100, 102, 103, 104, 107, 109, 111, 113, 114, 115, 118, 119, 120, 121], "excluded_lines": []}, "get_or_create_session": {"executed_lines": [147, 150, 152, 154, 155, 156], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [148, 159], "excluded_lines": []}, "root": {"executed_lines": [166], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "health_check": {"executed_lines": [172], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "api_status": {"executed_lines": [183, 185, 186, 187, 188, 189, 191, 192, 199, 200, 207, 210, 213, 214, 215, 217, 221], "summary": {"covered_lines": 17, "num_statements": 29, "percent_covered": 58.62068965517241, "percent_covered_display": "59", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [193, 194, 196, 201, 203, 204, 205, 218, 219, 229, 230, 231], "excluded_lines": []}, "process_query": {"executed_lines": [237, 238, 240, 241, 244, 247, 252, 255, 265, 268, 271, 280, 283, 292, 296, 299, 301], "summary": {"covered_lines": 17, "num_statements": 22, "percent_covered": 77.27272727272727, "percent_covered_display": "77", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [293, 297, 311, 312, 313], "excluded_lines": []}, "ingest_repository": {"executed_lines": [319, 320, 322, 323, 325, 326, 329, 334, 336, 338], "summary": {"covered_lines": 10, "num_statements": 13, "percent_covered": 76.92307692307692, "percent_covered_display": "77", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [347, 348, 349], "excluded_lines": []}, "global_exception_handler": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [355, 356], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 9, 10, 11, 12, 13, 15, 24, 25, 28, 29, 32, 35, 36, 37, 41, 42, 44, 45, 46, 47, 48, 51, 52, 54, 55, 56, 57, 58, 59, 60, 63, 64, 66, 67, 68, 69, 72, 73, 75, 76, 77, 78, 79, 80, 83, 84, 86, 87, 88, 89, 90, 93, 94, 125, 135, 145, 163, 164, 169, 170, 180, 181, 234, 235, 316, 317, 352, 353, 359], "summary": {"covered_lines": 66, "num_statements": 66, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 2}, "missing_lines": [], "excluded_lines": [359, 360]}}, "classes": {"QueryRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "IngestionRequest": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "IngestionResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StatusResponse": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 5, 6, 7, 9, 10, 11, 12, 13, 15, 24, 25, 28, 29, 32, 35, 36, 37, 41, 42, 44, 45, 46, 47, 48, 51, 52, 54, 55, 56, 57, 58, 59, 60, 63, 64, 66, 67, 68, 69, 72, 73, 75, 76, 77, 78, 79, 80, 83, 84, 86, 87, 88, 89, 90, 93, 94, 125, 135, 145, 147, 150, 152, 154, 155, 156, 163, 164, 166, 169, 170, 172, 180, 181, 183, 185, 186, 187, 188, 189, 191, 192, 199, 200, 207, 210, 213, 214, 215, 217, 221, 234, 235, 237, 238, 240, 241, 244, 247, 252, 255, 265, 268, 271, 280, 283, 292, 296, 299, 301, 316, 317, 319, 320, 322, 323, 325, 326, 329, 334, 336, 338, 352, 353, 359], "summary": {"covered_lines": 118, "num_statements": 157, "percent_covered": 75.1592356687898, "percent_covered_display": "75", "missing_lines": 39, "excluded_lines": 2}, "missing_lines": [98, 100, 102, 103, 104, 107, 109, 111, 113, 114, 115, 118, 119, 120, 121, 148, 159, 193, 194, 196, 201, 203, 204, 205, 218, 219, 229, 230, 231, 293, 297, 311, 312, 313, 347, 348, 349, 355, 356], "excluded_lines": [359, 360]}}}, "src/orchestrator/__init__.py": {"executed_lines": [1, 7, 8, 9, 10, 12], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 7, 8, 9, 10, 12], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 9, 10, 12], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/orchestrator/classifier.py": {"executed_lines": [1, 8, 9, 10, 12, 14, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 34, 35, 36, 37, 38, 41, 42, 44, 46, 268, 270, 272, 275, 276, 278, 279, 280, 281, 284, 285, 288, 293, 296, 300, 308, 312, 313, 316, 317, 318, 319, 322, 323, 324, 325, 328, 329, 332, 334, 336, 340, 343, 344, 347, 358, 359, 362, 363, 366, 367, 370, 371, 372, 374, 376, 380, 382, 383, 385, 387, 390, 392, 394, 396, 397, 398, 403], "summary": {"covered_lines": 82, "num_statements": 84, "percent_covered": 97.61904761904762, "percent_covered_display": "98", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [386, 388], "excluded_lines": [], "functions": {"QueryClassifier.__init__": {"executed_lines": [46, 268], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryClassifier.classify_query": {"executed_lines": [272, 275, 276, 278, 279, 280, 281, 284, 285, 288, 293, 296, 300], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryClassifier._calculate_intent_score": {"executed_lines": [312, 313, 316, 317, 318, 319, 322, 323, 324, 325, 328, 329, 332, 334], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryClassifier._adjust_confidence": {"executed_lines": [340, 343, 344, 347, 358, 359, 362, 363, 366, 367, 370, 371, 372, 374], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryClassifier._generate_reasoning": {"executed_lines": [380, 382, 383, 385, 387, 390, 392], "summary": {"covered_lines": 7, "num_statements": 9, "percent_covered": 77.77777777777777, "percent_covered_display": "78", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [386, 388], "excluded_lines": []}, "QueryClassifier.get_classification_stats": {"executed_lines": [396, 397, 398, 403], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 14, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 34, 35, 36, 37, 38, 41, 42, 44, 270, 308, 336, 376, 394], "summary": {"covered_lines": 28, "num_statements": 28, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"QueryIntent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ClassificationResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "QueryClassifier": {"executed_lines": [46, 268, 272, 275, 276, 278, 279, 280, 281, 284, 285, 288, 293, 296, 300, 312, 313, 316, 317, 318, 319, 322, 323, 324, 325, 328, 329, 332, 334, 340, 343, 344, 347, 358, 359, 362, 363, 366, 367, 370, 371, 372, 374, 380, 382, 383, 385, 387, 390, 392, 396, 397, 398, 403], "summary": {"covered_lines": 54, "num_statements": 56, "percent_covered": 96.42857142857143, "percent_covered_display": "96", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [386, 388], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 14, 17, 18, 20, 21, 22, 23, 24, 25, 26, 27, 30, 31, 32, 34, 35, 36, 37, 38, 41, 42, 44, 270, 308, 336, 376, 394], "summary": {"covered_lines": 28, "num_statements": 28, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/orchestrator/orchestrator.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 21, 22, 23, 24, 25, 26, 27, 28, 30, 33, 34, 36, 44, 45, 46, 47, 48, 51, 52, 53, 56, 57, 58, 59, 60, 63, 65, 67, 69, 71, 73, 75, 76, 79, 82, 85, 88, 91, 95, 98, 115, 117, 118, 120, 122, 123, 124, 126, 129, 131, 135, 137, 138, 140, 143, 146, 147, 148, 149, 151, 153, 154, 156, 157, 159, 163, 164, 166, 169, 170, 171, 175, 178, 180, 183, 190, 195, 196, 197, 198, 199, 200, 201, 202, 203, 208, 213, 215, 216, 218, 219, 221, 223, 227, 229, 231, 232, 233, 234, 237, 247, 253, 255, 256, 257, 260, 261, 262, 264, 268, 269, 270, 271, 272, 273, 274, 275, 276, 278, 280, 282, 283, 284, 286, 290, 296, 298, 299, 300, 301, 302, 303, 305, 307, 308, 309, 318, 322, 324, 330, 338, 340, 342, 354], "summary": {"covered_lines": 148, "num_statements": 167, "percent_covered": 88.62275449101796, "percent_covered_display": "89", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [191, 192, 205, 220, 238, 239, 240, 241, 242, 243, 244, 245, 249, 250, 251, 258, 259, 319, 320], "excluded_lines": [], "functions": {"OrchestratorAgent.__init__": {"executed_lines": [44, 45, 46, 47, 48, 51, 52, 53, 56, 57, 58, 59, 60, 63, 65], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrchestratorAgent.can_handle_query": {"executed_lines": [69], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrchestratorAgent.process_query": {"executed_lines": [73, 75, 76, 79, 82, 85, 88, 91, 95, 98, 115, 117, 118, 120, 122, 123, 124, 126, 129], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrchestratorAgent._execute_routing_decision": {"executed_lines": [135, 137, 138, 140, 143, 146, 147, 148, 149, 151, 153, 154, 156, 157], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrchestratorAgent._execute_agents_parallel": {"executed_lines": [163, 164, 166, 169, 170, 171, 175, 178, 180, 183, 190, 195, 196, 197, 198, 199, 200, 201, 202, 203, 208, 213, 215, 216, 218, 219, 221], "summary": {"covered_lines": 27, "num_statements": 31, "percent_covered": 87.**************, "percent_covered_display": "87", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [191, 192, 205, 220], "excluded_lines": []}, "OrchestratorAgent._execute_agents_sequential": {"executed_lines": [227, 229, 231, 232, 233, 234, 237, 247], "summary": {"covered_lines": 8, "num_statements": 19, "percent_covered": 42.**************, "percent_covered_display": "42", "missing_lines": 11, "excluded_lines": 0}, "missing_lines": [238, 239, 240, 241, 242, 243, 244, 245, 249, 250, 251], "excluded_lines": []}, "OrchestratorAgent._execute_agent_with_timeout": {"executed_lines": [255, 256, 257, 260, 261, 262], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [258, 259], "excluded_lines": []}, "OrchestratorAgent._try_fallback_agents": {"executed_lines": [268, 269, 270, 271, 272, 273, 274, 275, 276, 278], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrchestratorAgent._get_agent": {"executed_lines": [282, 283, 284], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrchestratorAgent._get_available_agents": {"executed_lines": [290], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrchestratorAgent._update_conversation_context": {"executed_lines": [298, 299, 300, 301, 302, 303], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrchestratorAgent._update_conversation_context_with_response": {"executed_lines": [307, 308, 309, 318], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [319, 320], "excluded_lines": []}, "OrchestratorAgent._generate_fallback_response": {"executed_lines": [324, 330], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "OrchestratorAgent.get_orchestrator_stats": {"executed_lines": [340, 342, 354], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 21, 22, 23, 24, 25, 26, 27, 28, 30, 33, 34, 36, 67, 71, 131, 159, 223, 253, 264, 280, 286, 296, 305, 322, 338], "summary": {"covered_lines": 29, "num_statements": 29, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"OrchestratorAgent": {"executed_lines": [44, 45, 46, 47, 48, 51, 52, 53, 56, 57, 58, 59, 60, 63, 65, 69, 73, 75, 76, 79, 82, 85, 88, 91, 95, 98, 115, 117, 118, 120, 122, 123, 124, 126, 129, 135, 137, 138, 140, 143, 146, 147, 148, 149, 151, 153, 154, 156, 157, 163, 164, 166, 169, 170, 171, 175, 178, 180, 183, 190, 195, 196, 197, 198, 199, 200, 201, 202, 203, 208, 213, 215, 216, 218, 219, 221, 227, 229, 231, 232, 233, 234, 237, 247, 255, 256, 257, 260, 261, 262, 268, 269, 270, 271, 272, 273, 274, 275, 276, 278, 282, 283, 284, 290, 298, 299, 300, 301, 302, 303, 307, 308, 309, 318, 324, 330, 340, 342, 354], "summary": {"covered_lines": 119, "num_statements": 138, "percent_covered": 86.23188405797102, "percent_covered_display": "86", "missing_lines": 19, "excluded_lines": 0}, "missing_lines": [191, 192, 205, 220, 238, 239, 240, 241, 242, 243, 244, 245, 249, 250, 251, 258, 259, 319, 320], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 21, 22, 23, 24, 25, 26, 27, 28, 30, 33, 34, 36, 67, 71, 131, 159, 223, 253, 264, 280, 286, 296, 305, 322, 338], "summary": {"covered_lines": 29, "num_statements": 29, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/orchestrator/router.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 16, 19, 20, 21, 23, 24, 25, 26, 27, 28, 31, 32, 34, 35, 38, 45, 83, 85, 87, 89, 92, 95, 96, 99, 101, 106, 117, 122, 123, 126, 127, 132, 135, 141, 142, 143, 144, 147, 149, 181, 189, 190, 193, 195, 197, 198, 203, 205, 214, 222, 223, 226, 227, 228, 230, 239, 241, 244, 245, 247, 250, 257, 258, 265, 271], "summary": {"covered_lines": 69, "num_statements": 87, "percent_covered": 79.3103448275862, "percent_covered_display": "79", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [108, 109, 110, 130, 157, 160, 162, 163, 164, 166, 172, 199, 200, 242, 251, 259, 266, 273], "excluded_lines": [], "functions": {"AgentRouter.__init__": {"executed_lines": [35, 38, 45, 83], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentRouter.route_query": {"executed_lines": [87, 89, 92, 95, 96, 99, 101, 106], "summary": {"covered_lines": 8, "num_statements": 11, "percent_covered": 72.**************, "percent_covered_display": "73", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [108, 109, 110], "excluded_lines": []}, "AgentRouter._determine_routing_strategy": {"executed_lines": [122, 123, 126, 127], "summary": {"covered_lines": 4, "num_statements": 5, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [130], "excluded_lines": []}, "AgentRouter._requires_multi_agent": {"executed_lines": [135, 141, 142, 143, 144, 147], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentRouter._route_single_agent": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 7, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [157, 160, 162, 163, 164, 166, 172], "excluded_lines": []}, "AgentRouter._route_multi_agent": {"executed_lines": [189, 190, 193, 195, 197, 198, 203, 205], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [199, 200], "excluded_lines": []}, "AgentRouter._route_fallback_chain": {"executed_lines": [222, 223, 226, 227, 228, 230], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentRouter._get_fallback_agents": {"executed_lines": [241, 244, 245], "summary": {"covered_lines": 3, "num_statements": 4, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [242], "excluded_lines": []}, "AgentRouter._validate_routing_decision": {"executed_lines": [250, 257, 258, 265], "summary": {"covered_lines": 4, "num_statements": 7, "percent_covered": 57.***************, "percent_covered_display": "57", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [251, 259, 266], "excluded_lines": []}, "AgentRouter.get_routing_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [273], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 16, 19, 20, 21, 23, 24, 25, 26, 27, 28, 31, 32, 34, 85, 117, 132, 149, 181, 214, 239, 247, 271], "summary": {"covered_lines": 26, "num_statements": 26, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RoutingDecision": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "AgentRouter": {"executed_lines": [35, 38, 45, 83, 87, 89, 92, 95, 96, 99, 101, 106, 122, 123, 126, 127, 135, 141, 142, 143, 144, 147, 189, 190, 193, 195, 197, 198, 203, 205, 222, 223, 226, 227, 228, 230, 241, 244, 245, 250, 257, 258, 265], "summary": {"covered_lines": 43, "num_statements": 61, "percent_covered": 70.**************, "percent_covered_display": "70", "missing_lines": 18, "excluded_lines": 0}, "missing_lines": [108, 109, 110, 130, 157, 160, 162, 163, 164, 166, 172, 199, 200, 242, 251, 259, 266, 273], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 16, 19, 20, 21, 23, 24, 25, 26, 27, 28, 31, 32, 34, 85, 117, 132, 149, 181, 214, 239, 247, 271], "summary": {"covered_lines": 26, "num_statements": 26, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/orchestrator/synthesizer.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 16, 19, 20, 21, 23, 24, 25, 26, 27, 30, 31, 33, 34, 37, 45, 47, 54, 55, 56, 59, 60, 63, 69, 70, 72, 74, 76, 77, 78, 83, 85, 86, 97, 99, 101, 109, 112, 119, 122, 124, 125, 127, 128, 131, 134, 137, 138, 140, 143, 144, 145, 146, 149, 150, 153, 160, 168, 234, 237, 240, 242, 246, 247, 248, 250, 251, 253, 254, 255, 256, 257, 259, 260, 261, 262, 264, 276, 286], "summary": {"covered_lines": 82, "num_statements": 126, "percent_covered": 65.07936507936508, "percent_covered_display": "65", "missing_lines": 44, "excluded_lines": 0}, "missing_lines": [64, 89, 91, 93, 95, 171, 172, 173, 174, 175, 176, 179, 185, 186, 187, 189, 190, 192, 193, 194, 195, 197, 198, 199, 200, 202, 203, 206, 207, 208, 209, 211, 212, 213, 214, 216, 217, 219, 220, 222, 243, 278, 284, 288], "excluded_lines": [], "functions": {"ResponseSynthesizer.__init__": {"executed_lines": [34, 37, 45], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResponseSynthesizer.synthesize_responses": {"executed_lines": [54, 55, 56, 59, 60, 63, 69, 70, 72, 74, 76, 77, 78], "summary": {"covered_lines": 13, "num_statements": 14, "percent_covered": 92.85714285714286, "percent_covered_display": "93", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [64], "excluded_lines": []}, "ResponseSynthesizer._determine_synthesis_strategy": {"executed_lines": [85, 86], "summary": {"covered_lines": 2, "num_statements": 6, "percent_covered": 33.**************6, "percent_covered_display": "33", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [89, 91, 93, 95], "excluded_lines": []}, "ResponseSynthesizer._synthesize_single_response": {"executed_lines": [99, 101], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResponseSynthesizer._synthesize_multi_agent": {"executed_lines": [112, 119, 122, 124, 125, 127, 128, 131, 134, 137, 138, 140, 143, 144, 145, 146, 149, 150, 153, 160], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResponseSynthesizer._synthesize_hierarchical": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 35, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 35, "excluded_lines": 0}, "missing_lines": [171, 172, 173, 174, 175, 176, 179, 185, 186, 187, 189, 190, 192, 193, 194, 195, 197, 198, 199, 200, 202, 203, 206, 207, 208, 209, 211, 212, 213, 214, 216, 217, 219, 220, 222], "excluded_lines": []}, "ResponseSynthesizer._synthesize_fallback_chain": {"executed_lines": [237, 240, 242, 246, 247, 248, 250, 251, 253, 254, 255, 256, 257, 259, 260, 261, 262, 264], "summary": {"covered_lines": 18, "num_statements": 19, "percent_covered": 94.73684210526316, "percent_covered_display": "95", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [243], "excluded_lines": []}, "ResponseSynthesizer._get_section_title": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 2, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [278, 284], "excluded_lines": []}, "ResponseSynthesizer.get_synthesis_stats": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [288], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 16, 19, 20, 21, 23, 24, 25, 26, 27, 30, 31, 33, 47, 83, 97, 109, 168, 234, 276, 286], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"SynthesisResult": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ResponseSynthesizer": {"executed_lines": [34, 37, 45, 54, 55, 56, 59, 60, 63, 69, 70, 72, 74, 76, 77, 78, 85, 86, 99, 101, 112, 119, 122, 124, 125, 127, 128, 131, 134, 137, 138, 140, 143, 144, 145, 146, 149, 150, 153, 160, 237, 240, 242, 246, 247, 248, 250, 251, 253, 254, 255, 256, 257, 259, 260, 261, 262, 264], "summary": {"covered_lines": 58, "num_statements": 102, "percent_covered": 56.86274509803921, "percent_covered_display": "57", "missing_lines": 44, "excluded_lines": 0}, "missing_lines": [64, 89, 91, 93, 95, 171, 172, 173, 174, 175, 176, 179, 185, 186, 187, 189, 190, 192, 193, 194, 195, 197, 198, 199, 200, 202, 203, 206, 207, 208, 209, 211, 212, 213, 214, 216, 217, 219, 220, 222, 243, 278, 284, 288], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 16, 19, 20, 21, 23, 24, 25, 26, 27, 30, 31, 33, 47, 83, 97, 109, 168, 234, 276, 286], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/rag/__init__.py": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/task_planner/__init__.py": {"executed_lines": [1, 8, 9, 10, 11, 26, 27, 29], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 8, 9, 10, 11, 26, 27, 29], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 8, 9, 10, 11, 26, 27, 29], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/task_planner/agent.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 31, 44, 46, 48, 49, 52, 55, 56, 59, 63, 64, 67, 106, 108, 118, 121, 128, 134, 137, 138, 141, 142, 145, 146, 147, 148, 151, 152, 156, 158, 159, 161, 171, 173, 174, 177, 180, 183, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 200, 202, 205, 220, 222, 223, 225, 226, 227, 229, 230, 236, 245, 246, 249, 250, 251, 254, 256, 266, 268, 269, 270, 273, 274, 280, 281, 283, 284, 285, 287, 297, 300, 304, 305, 306, 308, 309, 316, 317, 318, 319, 320, 321, 328, 329, 330, 331, 332, 333, 335, 336, 338, 350, 351, 352, 353, 356, 363, 365, 375, 393, 418, 423, 425, 443, 455, 456, 457, 458, 461, 468, 471, 481, 484, 502, 527, 532, 559, 562, 563, 564, 565, 568, 575, 577, 593, 611, 615, 627, 630, 631, 632, 633, 636, 637, 638, 640, 656, 674, 678, 688, 691, 692, 693, 694, 697, 698, 701, 711, 713, 729, 747, 751, 761, 765, 767, 768, 769, 777, 779, 781, 782, 784, 785, 786, 787, 788, 789, 796, 798, 800, 801, 802, 803, 804, 805, 806, 807], "summary": {"covered_lines": 204, "num_statements": 213, "percent_covered": 95.77464788732394, "percent_covered_display": "96", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [57, 153, 197, 323, 324, 325, 763, 793, 794], "excluded_lines": [], "functions": {"TaskPlannerAgent.__init__": {"executed_lines": [44, 46, 48, 49, 52, 55, 56, 59, 63, 64, 67, 106], "summary": {"covered_lines": 12, "num_statements": 13, "percent_covered": 92.3076923076923, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [57], "excluded_lines": []}, "TaskPlannerAgent.can_handle_query": {"executed_lines": [118, 121, 128, 134, 137, 138, 141, 142, 145, 146, 147, 148, 151, 152, 156, 158, 159], "summary": {"covered_lines": 17, "num_statements": 18, "percent_covered": 94.44444444444444, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [153], "excluded_lines": []}, "TaskPlannerAgent.process_query": {"executed_lines": [171, 173, 174, 177, 180, 183, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 200, 202, 205, 220, 222, 223, 225, 226, 227, 229, 230], "summary": {"covered_lines": 27, "num_statements": 28, "percent_covered": 96.42857142857143, "percent_covered_display": "96", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [197], "excluded_lines": []}, "TaskPlannerAgent._classify_query_type": {"executed_lines": [245, 246, 249, 250, 251, 254], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskPlannerAgent._perform_search": {"executed_lines": [266, 268, 269, 270, 273, 274, 280, 281, 283, 284, 285], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskPlannerAgent._extract_requirements": {"executed_lines": [297, 300, 304, 305, 306, 308, 309, 316, 317, 318, 319, 320, 321, 328, 329, 330, 331, 332, 333, 335, 336], "summary": {"covered_lines": 21, "num_statements": 24, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [323, 324, 325], "excluded_lines": []}, "TaskPlannerAgent._generate_breakdown_response": {"executed_lines": [350, 351, 352, 353, 356, 363, 365, 375, 393, 418, 423, 425], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskPlannerAgent._generate_comprehensive_plan_response": {"executed_lines": [455, 456, 457, 458, 461, 468, 471, 481, 484, 502, 527, 532], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskPlannerAgent._generate_timeline_response": {"executed_lines": [562, 563, 564, 565, 568, 575, 577, 593, 611, 615], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskPlannerAgent._generate_dependency_response": {"executed_lines": [630, 631, 632, 633, 636, 637, 638, 640, 656, 674, 678], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskPlannerAgent._generate_risk_response": {"executed_lines": [691, 692, 693, 694, 697, 698, 701, 711, 713, 729, 747, 751], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskPlannerAgent._generate_general_planning_response": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 1, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [763], "excluded_lines": []}, "TaskPlannerAgent._format_tasks_for_prompt": {"executed_lines": [767, 768, 769, 777], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskPlannerAgent._format_search_results_for_prompt": {"executed_lines": [781, 782, 784, 785, 786, 787, 788, 789, 796], "summary": {"covered_lines": 9, "num_statements": 11, "percent_covered": 81.81818181818181, "percent_covered_display": "82", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [793, 794], "excluded_lines": []}, "TaskPlannerAgent._extract_sources": {"executed_lines": [800, 801, 802, 803, 804, 805, 806, 807], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 31, 108, 161, 236, 256, 287, 338, 443, 559, 627, 688, 761, 765, 779, 798], "summary": {"covered_lines": 32, "num_statements": 32, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TaskPlannerAgent": {"executed_lines": [44, 46, 48, 49, 52, 55, 56, 59, 63, 64, 67, 106, 118, 121, 128, 134, 137, 138, 141, 142, 145, 146, 147, 148, 151, 152, 156, 158, 159, 171, 173, 174, 177, 180, 183, 186, 187, 188, 189, 190, 191, 192, 193, 194, 195, 200, 202, 205, 220, 222, 223, 225, 226, 227, 229, 230, 245, 246, 249, 250, 251, 254, 266, 268, 269, 270, 273, 274, 280, 281, 283, 284, 285, 297, 300, 304, 305, 306, 308, 309, 316, 317, 318, 319, 320, 321, 328, 329, 330, 331, 332, 333, 335, 336, 350, 351, 352, 353, 356, 363, 365, 375, 393, 418, 423, 425, 455, 456, 457, 458, 461, 468, 471, 481, 484, 502, 527, 532, 562, 563, 564, 565, 568, 575, 577, 593, 611, 615, 630, 631, 632, 633, 636, 637, 638, 640, 656, 674, 678, 691, 692, 693, 694, 697, 698, 701, 711, 713, 729, 747, 751, 767, 768, 769, 777, 781, 782, 784, 785, 786, 787, 788, 789, 796, 800, 801, 802, 803, 804, 805, 806, 807], "summary": {"covered_lines": 172, "num_statements": 181, "percent_covered": 95.02762430939227, "percent_covered_display": "95", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [57, 153, 197, 323, 324, 325, 763, 793, 794], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 25, 28, 29, 31, 108, 161, 236, 256, 287, 338, 443, 559, 627, 688, 761, 765, 779, 798], "summary": {"covered_lines": 32, "num_statements": 32, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/task_planner/breakdown.py": {"executed_lines": [1, 8, 9, 10, 12, 20, 23, 24, 26, 28, 95, 124, 155, 157, 159, 161, 162, 165, 168, 171, 172, 175, 176, 178, 179, 181, 183, 185, 186, 188, 190, 191, 192, 195, 198, 199, 200, 202, 205, 206, 207, 208, 209, 210, 212, 213, 215, 217, 219, 230, 231, 232, 235, 236, 237, 238, 241, 242, 243, 244, 247, 250, 253, 256, 258, 260, 262, 263, 266, 267, 284, 301, 302, 319, 320, 337, 338, 355, 356, 358, 360, 362, 363, 366, 367, 369, 370, 371, 372, 382, 384, 385, 395, 397, 399, 401, 402, 405, 422, 423, 438, 440, 442, 444, 445, 461, 463, 466, 469, 470, 472, 474, 476, 479, 486, 487, 488, 489, 495, 501, 502, 503, 504, 509, 511, 513, 516, 522, 525, 527, 529, 531, 534, 535, 544, 545, 554, 555, 563], "summary": {"covered_lines": 137, "num_statements": 137, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"RequirementBreakdownEngine.__init__": {"executed_lines": [28, 95, 124, 155], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine.break_down_requirement": {"executed_lines": [159, 161, 162, 165, 168, 171, 172, 175, 176, 178, 179], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine.break_down_requirements": {"executed_lines": [183, 185, 186, 188, 190, 191, 192, 195, 198, 199, 200, 202, 205, 206, 207, 208, 209, 210, 212, 213], "summary": {"covered_lines": 20, "num_statements": 20, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine._analyze_requirement": {"executed_lines": [217, 219, 230, 231, 232, 235, 236, 237, 238, 241, 242, 243, 244, 247, 250, 253, 256, 258], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine._generate_5_phase_tasks": {"executed_lines": [262, 263, 266, 267, 284, 301, 302, 319, 320, 337, 338, 355, 356, 358], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine._generate_implementation_tasks": {"executed_lines": [362, 363, 366, 367, 369, 370, 371, 372, 382, 384, 385, 395, 397], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine._generate_quality_tasks": {"executed_lines": [401, 402, 405, 422, 423, 438], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine._generate_integration_tasks": {"executed_lines": [442, 444, 445, 461], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine._extract_keywords": {"executed_lines": [466, 469, 470, 472], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine._extract_entities": {"executed_lines": [476, 479, 486, 487, 488, 489, 495, 501, 502, 503, 504, 509], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine._estimate_effort": {"executed_lines": [513, 516, 522, 525, 527], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RequirementBreakdownEngine._identify_risks": {"executed_lines": [531, 534, 535, 544, 545, 554, 555, 563], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 20, 23, 24, 26, 157, 181, 215, 260, 360, 399, 440, 463, 474, 511, 529], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RequirementBreakdownEngine": {"executed_lines": [28, 95, 124, 155, 159, 161, 162, 165, 168, 171, 172, 175, 176, 178, 179, 183, 185, 186, 188, 190, 191, 192, 195, 198, 199, 200, 202, 205, 206, 207, 208, 209, 210, 212, 213, 217, 219, 230, 231, 232, 235, 236, 237, 238, 241, 242, 243, 244, 247, 250, 253, 256, 258, 262, 263, 266, 267, 284, 301, 302, 319, 320, 337, 338, 355, 356, 358, 362, 363, 366, 367, 369, 370, 371, 372, 382, 384, 385, 395, 397, 401, 402, 405, 422, 423, 438, 442, 444, 445, 461, 466, 469, 470, 472, 476, 479, 486, 487, 488, 489, 495, 501, 502, 503, 504, 509, 513, 516, 522, 525, 527, 531, 534, 535, 544, 545, 554, 555, 563], "summary": {"covered_lines": 119, "num_statements": 119, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 20, 23, 24, 26, 157, 181, 215, 260, 360, 399, 440, 463, 474, 511, 529], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/task_planner/dependencies.py": {"executed_lines": [1, 8, 9, 11, 19, 22, 23, 25, 28, 42, 66, 68, 77, 80, 83, 84, 87, 90, 93, 96, 97, 100, 102, 103, 105, 114, 115, 118, 119, 120, 132, 133, 134, 146, 147, 148, 158, 159, 160, 162, 171, 172, 174, 183, 184, 187, 188, 189, 198, 199, 200, 208, 209, 210, 217, 218, 220, 222, 223, 224, 225, 227, 230, 231, 232, 233, 234, 235, 238, 239, 240, 241, 243, 245, 246, 248, 250, 253, 254, 255, 256, 257, 258, 259, 263, 264, 275, 276, 277, 284, 286, 290, 291, 294, 295, 296, 297, 299, 300, 301, 302, 304, 305, 308, 311, 314, 315, 316, 318, 319, 320, 321, 323, 324, 327, 328, 329, 330, 332, 334, 336, 338, 342, 343, 345, 346, 347, 349, 350, 351, 352, 354, 355, 357, 359, 362, 368, 369, 370, 371, 372, 373, 375, 376, 378, 392, 408, 411, 412, 414, 415, 416, 417, 420, 421, 423, 424, 425, 428, 429, 430, 431, 432, 433, 435, 437, 439, 440, 442, 443, 444, 445, 446, 448, 450, 452, 453, 455, 456, 458, 460, 462, 465, 468, 470, 471, 472, 474, 475, 476, 477, 479, 480, 481, 482, 484, 485, 488, 490, 492, 494, 495, 497, 500, 502, 503, 505, 507, 510, 512, 513, 516, 517, 518, 519, 521, 528, 530, 532, 535, 538, 539, 540, 542, 543, 546, 548, 550, 551, 553], "summary": {"covered_lines": 228, "num_statements": 262, "percent_covered": 87.02290076335878, "percent_covered_display": "87", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [121, 135, 149, 163, 265, 267, 268, 269, 270, 271, 272, 278, 279, 280, 281, 282, 287, 309, 339, 363, 365, 381, 383, 384, 394, 395, 397, 398, 400, 401, 402, 403, 404, 406], "excluded_lines": [], "functions": {"DependencyAnalyzer.__init__": {"executed_lines": [28, 42, 66], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer.analyze_dependencies": {"executed_lines": [77, 80, 83, 84, 87, 90, 93, 96, 97, 100, 102, 103], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer.detect_bottlenecks": {"executed_lines": [114, 115, 118, 119, 120, 132, 133, 134, 146, 147, 148, 158, 159, 160, 162, 171, 172], "summary": {"covered_lines": 17, "num_statements": 21, "percent_covered": 80.95238095238095, "percent_covered_display": "81", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [121, 135, 149, 163], "excluded_lines": []}, "DependencyAnalyzer.suggest_optimizations": {"executed_lines": [183, 184, 187, 188, 189, 198, 199, 200, 208, 209, 210, 217, 218], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer._add_explicit_dependencies": {"executed_lines": [222, 223, 224, 225], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer._add_implicit_dependencies": {"executed_lines": [230, 231, 232, 233, 234, 235, 238, 239, 240, 241, 243, 245, 246], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer._add_pattern_dependencies": {"executed_lines": [250, 253, 254, 255, 256, 257, 258, 259, 263, 264, 275, 276, 277], "summary": {"covered_lines": 13, "num_statements": 25, "percent_covered": 52.0, "percent_covered_display": "52", "missing_lines": 12, "excluded_lines": 0}, "missing_lines": [265, 267, 268, 269, 270, 271, 272, 278, 279, 280, 281, 282], "excluded_lines": []}, "DependencyAnalyzer._calculate_critical_path": {"executed_lines": [286, 290, 291, 294, 295, 296, 297, 299, 300, 301, 302, 304, 305, 308, 311, 314, 315, 316, 318, 319, 320, 321, 323, 324, 327, 328, 329, 330, 332, 334], "summary": {"covered_lines": 30, "num_statements": 32, "percent_covered": 93.75, "percent_covered_display": "94", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [287, 309], "excluded_lines": []}, "DependencyAnalyzer._calculate_total_duration": {"executed_lines": [338, 342, 343, 345, 346, 347, 349, 350, 351, 352, 354, 355, 357], "summary": {"covered_lines": 13, "num_statements": 14, "percent_covered": 92.85714285714286, "percent_covered_display": "93", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [339], "excluded_lines": []}, "DependencyAnalyzer._validate_dependency_graph": {"executed_lines": [362, 368, 369, 370, 371, 372, 373, 375, 376], "summary": {"covered_lines": 9, "num_statements": 11, "percent_covered": 81.81818181818181, "percent_covered_display": "82", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [363, 365], "excluded_lines": []}, "DependencyAnalyzer._add_cycle_risks": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 3, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [381, 383, 384], "excluded_lines": []}, "DependencyAnalyzer._can_reach": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 10, "percent_covered": 0.0, "percent_covered_display": "0", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [394, 395, 397, 398, 400, 401, 402, 403, 404, 406], "excluded_lines": []}, "DependencyAnalyzer._topological_sort": {"executed_lines": [411, 412, 414, 415, 416, 417, 420, 421, 423, 424, 425, 428, 429, 430, 431, 432, 433, 435], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer._find_dependency_chains": {"executed_lines": [439, 440, 442, 443, 444, 445, 446, 448], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer._build_chain": {"executed_lines": [452, 453, 455, 456, 458, 460], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer._find_parallelization_opportunities": {"executed_lines": [465, 468, 470, 471, 472, 474, 475, 476, 477, 479, 480, 481, 482, 484, 485, 488], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer._calculate_parallelization_savings": {"executed_lines": [492, 494, 495, 497, 500, 502, 503, 505], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer._find_unnecessary_dependencies": {"executed_lines": [510, 512, 513, 516, 517, 518, 519, 521, 528], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyAnalyzer._find_merge_opportunities": {"executed_lines": [532, 535, 538, 539, 540, 542, 543, 546, 548, 550, 551, 553], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 19, 22, 23, 25, 68, 105, 174, 220, 227, 248, 284, 336, 359, 378, 392, 408, 437, 450, 462, 490, 507, 530], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DependencyAnalyzer": {"executed_lines": [28, 42, 66, 77, 80, 83, 84, 87, 90, 93, 96, 97, 100, 102, 103, 114, 115, 118, 119, 120, 132, 133, 134, 146, 147, 148, 158, 159, 160, 162, 171, 172, 183, 184, 187, 188, 189, 198, 199, 200, 208, 209, 210, 217, 218, 222, 223, 224, 225, 230, 231, 232, 233, 234, 235, 238, 239, 240, 241, 243, 245, 246, 250, 253, 254, 255, 256, 257, 258, 259, 263, 264, 275, 276, 277, 286, 290, 291, 294, 295, 296, 297, 299, 300, 301, 302, 304, 305, 308, 311, 314, 315, 316, 318, 319, 320, 321, 323, 324, 327, 328, 329, 330, 332, 334, 338, 342, 343, 345, 346, 347, 349, 350, 351, 352, 354, 355, 357, 362, 368, 369, 370, 371, 372, 373, 375, 376, 411, 412, 414, 415, 416, 417, 420, 421, 423, 424, 425, 428, 429, 430, 431, 432, 433, 435, 439, 440, 442, 443, 444, 445, 446, 448, 452, 453, 455, 456, 458, 460, 465, 468, 470, 471, 472, 474, 475, 476, 477, 479, 480, 481, 482, 484, 485, 488, 492, 494, 495, 497, 500, 502, 503, 505, 510, 512, 513, 516, 517, 518, 519, 521, 528, 532, 535, 538, 539, 540, 542, 543, 546, 548, 550, 551, 553], "summary": {"covered_lines": 204, "num_statements": 238, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 34, "excluded_lines": 0}, "missing_lines": [121, 135, 149, 163, 265, 267, 268, 269, 270, 271, 272, 278, 279, 280, 281, 282, 287, 309, 339, 363, 365, 381, 383, 384, 394, 395, 397, 398, 400, 401, 402, 403, 404, 406], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 19, 22, 23, 25, 68, 105, 174, 220, 227, 248, 284, 336, 359, 378, 392, 408, 437, 450, 462, 490, 507, 530], "summary": {"covered_lines": 24, "num_statements": 24, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/task_planner/models.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 17, 18, 20, 21, 22, 23, 26, 27, 29, 30, 31, 32, 33, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 52, 53, 54, 55, 58, 59, 61, 62, 63, 64, 67, 68, 69, 71, 72, 73, 76, 77, 78, 80, 81, 82, 83, 86, 87, 88, 90, 91, 92, 93, 94, 97, 98, 99, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 120, 122, 127, 128, 130, 132, 139, 140, 142, 144, 146, 148, 149, 151, 152, 153, 154, 156, 159, 160, 161, 163, 164, 165, 166, 167, 168, 169, 172, 173, 174, 176, 177, 178, 179, 180, 181, 182, 183, 184, 186, 188, 189, 191, 193, 195, 197, 198, 199, 200, 202, 204, 206, 208, 209, 212, 213, 214, 216, 217, 218, 219, 221, 223, 224, 225, 227, 229, 231, 233, 235, 236, 237, 238, 239, 241, 243, 245, 247, 248, 250, 251, 252, 254, 255, 256, 257, 258, 259, 261, 262, 264, 267, 268, 269, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 282, 284], "summary": {"covered_lines": 177, "num_statements": 178, "percent_covered": 99.43820224719101, "percent_covered_display": "99", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [230], "excluded_lines": [], "functions": {"Task.add_dependency": {"executed_lines": [122, 127, 128], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Task.add_risk": {"executed_lines": [132, 139, 140], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Task.get_dependency_ids": {"executed_lines": [144], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Task.is_ready_to_start": {"executed_lines": [148, 149, 151, 152, 153, 154, 156], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Timeline.add_task": {"executed_lines": [188, 189], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Timeline.add_milestone": {"executed_lines": [193], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Timeline.get_task_by_id": {"executed_lines": [197, 198, 199, 200], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Timeline.get_tasks_by_status": {"executed_lines": [204], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Timeline.get_ready_tasks": {"executed_lines": [208, 209], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyGraph.add_task": {"executed_lines": [223, 224, 225], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyGraph.add_dependency": {"executed_lines": [229, 231], "summary": {"covered_lines": 2, "num_statements": 3, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [230], "excluded_lines": []}, "DependencyGraph.get_predecessors": {"executed_lines": [235, 236, 237, 238, 239], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyGraph.get_successors": {"executed_lines": [243], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyGraph.has_cycle": {"executed_lines": [247, 248, 250, 264], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyGraph.has_cycle.dfs": {"executed_lines": [251, 252, 254, 255, 256, 257, 258, 259, 261, 262], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskPlan.to_dict": {"executed_lines": [284], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 17, 18, 20, 21, 22, 23, 26, 27, 29, 30, 31, 32, 33, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 52, 53, 54, 55, 58, 59, 61, 62, 63, 64, 67, 68, 69, 71, 72, 73, 76, 77, 78, 80, 81, 82, 83, 86, 87, 88, 90, 91, 92, 93, 94, 97, 98, 99, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 120, 130, 142, 146, 159, 160, 161, 163, 164, 165, 166, 167, 168, 169, 172, 173, 174, 176, 177, 178, 179, 180, 181, 182, 183, 184, 186, 191, 195, 202, 206, 212, 213, 214, 216, 217, 218, 219, 221, 227, 233, 241, 245, 267, 268, 269, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 282], "summary": {"covered_lines": 127, "num_statements": 127, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TaskPriority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskStatus": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RiskLevel": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "FileReference": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskDependency": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TaskRisk": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Task": {"executed_lines": [122, 127, 128, 132, 139, 140, 144, 148, 149, 151, 152, 153, 154, 156], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Milestone": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "Timeline": {"executed_lines": [188, 189, 193, 197, 198, 199, 200, 204, 208, 209], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DependencyGraph": {"executed_lines": [223, 224, 225, 229, 231, 235, 236, 237, 238, 239, 243, 247, 248, 250, 251, 252, 254, 255, 256, 257, 258, 259, 261, 262, 264], "summary": {"covered_lines": 25, "num_statements": 26, "percent_covered": 96.15384615384616, "percent_covered_display": "96", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [230], "excluded_lines": []}, "TaskPlan": {"executed_lines": [284], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 17, 18, 20, 21, 22, 23, 26, 27, 29, 30, 31, 32, 33, 36, 37, 39, 40, 41, 42, 43, 44, 45, 46, 49, 50, 52, 53, 54, 55, 58, 59, 61, 62, 63, 64, 67, 68, 69, 71, 72, 73, 76, 77, 78, 80, 81, 82, 83, 86, 87, 88, 90, 91, 92, 93, 94, 97, 98, 99, 101, 102, 103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118, 120, 130, 142, 146, 159, 160, 161, 163, 164, 165, 166, 167, 168, 169, 172, 173, 174, 176, 177, 178, 179, 180, 181, 182, 183, 184, 186, 191, 195, 202, 206, 212, 213, 214, 216, 217, 218, 219, 221, 227, 233, 241, 245, 267, 268, 269, 271, 272, 273, 274, 275, 276, 277, 278, 279, 280, 282], "summary": {"covered_lines": 127, "num_statements": 127, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/task_planner/risk_analyzer.py": {"executed_lines": [1, 8, 9, 11, 20, 23, 24, 26, 29, 51, 91, 93, 102, 104, 116, 117, 118, 121, 122, 125, 126, 129, 130, 133, 138, 141, 143, 144, 146, 155, 164, 165, 168, 169, 172, 173, 176, 177, 180, 181, 182, 183, 185, 187, 196, 199, 200, 201, 203, 204, 213, 225, 237, 248, 250, 259, 262, 274, 275, 276, 278, 290, 302, 304, 313, 316, 317, 319, 320, 321, 322, 323, 326, 327, 328, 329, 341, 342, 353, 355, 357, 359, 368, 378, 380, 382, 384, 385, 394, 404, 406, 408, 409, 412, 413, 424, 425, 436, 437, 447, 449, 451, 452, 455, 456, 467, 468, 469, 478, 480, 482, 485, 486, 488, 489, 490, 492, 493, 496, 497, 499, 500, 501, 502, 505, 511, 513, 514, 516, 519, 520, 521, 522, 524, 526, 528, 530, 533, 534, 535, 536, 538, 539, 541, 544, 545, 548, 550, 551, 553, 554, 561, 563, 565, 567, 568, 570, 571, 572, 573, 574, 576, 577, 578, 581, 582, 585, 586, 587, 589, 590, 591, 593, 595, 597, 598, 601, 605, 608, 611, 612, 614, 615, 617, 618, 620, 621, 624, 625, 626, 627, 628, 629, 631, 633, 635, 654, 656, 659, 660, 662, 663, 664, 665, 666, 667, 669, 670, 671, 672, 673, 674, 676, 677, 678, 679, 680], "summary": {"covered_lines": 215, "num_statements": 235, "percent_covered": 91.48936170212765, "percent_covered_display": "91", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [214, 226, 238, 263, 279, 291, 343, 360, 369, 386, 395, 414, 426, 438, 457, 542, 583, 602, 606, 609], "excluded_lines": [], "functions": {"RiskAnalyzer.__init__": {"executed_lines": [29, 51, 91], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RiskAnalyzer.analyze_task_plan_risks": {"executed_lines": [102, 104, 116, 117, 118, 121, 122, 125, 126, 129, 130, 133, 138, 141, 143, 144], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RiskAnalyzer.analyze_task_risks": {"executed_lines": [155, 164, 165, 168, 169, 172, 173, 176, 177, 180, 181, 182, 183, 185], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RiskAnalyzer.analyze_timeline_risks": {"executed_lines": [196, 199, 200, 201, 203, 204, 213, 225, 237, 248], "summary": {"covered_lines": 10, "num_statements": 13, "percent_covered": 76.92307692307692, "percent_covered_display": "77", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [214, 226, 238], "excluded_lines": []}, "RiskAnalyzer.analyze_dependency_risks": {"executed_lines": [259, 262, 274, 275, 276, 278, 290, 302], "summary": {"covered_lines": 8, "num_statements": 11, "percent_covered": 72.**************, "percent_covered_display": "73", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [263, 279, 291], "excluded_lines": []}, "RiskAnalyzer.analyze_resource_risks": {"executed_lines": [313, 316, 317, 319, 320, 321, 322, 323, 326, 327, 328, 329, 341, 342, 353], "summary": {"covered_lines": 15, "num_statements": 16, "percent_covered": 93.75, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [343], "excluded_lines": []}, "RiskAnalyzer._analyze_effort_risks": {"executed_lines": [357, 359, 368, 378], "summary": {"covered_lines": 4, "num_statements": 6, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [360, 369], "excluded_lines": []}, "RiskAnalyzer._analyze_task_dependency_risks": {"executed_lines": [382, 384, 385, 394, 404], "summary": {"covered_lines": 5, "num_statements": 7, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [386, 395], "excluded_lines": []}, "RiskAnalyzer._analyze_technology_risks": {"executed_lines": [408, 409, 412, 413, 424, 425, 436, 437, 447], "summary": {"covered_lines": 9, "num_statements": 12, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [414, 426, 438], "excluded_lines": []}, "RiskAnalyzer._analyze_external_risks": {"executed_lines": [451, 452, 455, 456, 467, 468, 469, 478], "summary": {"covered_lines": 8, "num_statements": 9, "percent_covered": 88.88888888888889, "percent_covered_display": "89", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [457], "excluded_lines": []}, "RiskAnalyzer._calculate_overall_risk": {"executed_lines": [482, 485, 486, 488, 489, 490, 492, 493, 496, 497, 499, 500, 501, 502, 505, 511, 513, 514, 516, 519, 520, 521, 522, 524, 526], "summary": {"covered_lines": 25, "num_statements": 25, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RiskAnalyzer._generate_mitigation_plan": {"executed_lines": [530, 533, 534, 535, 536, 538, 539, 541, 544, 545, 548, 550, 551, 553, 554, 561], "summary": {"covered_lines": 16, "num_statements": 17, "percent_covered": 94.11764705882354, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [542], "excluded_lines": []}, "RiskAnalyzer._generate_recommendations": {"executed_lines": [565, 567, 568, 570, 571, 572, 573, 574, 576, 577, 578, 581, 582, 585, 586, 587, 589, 590, 591, 593], "summary": {"covered_lines": 20, "num_statements": 21, "percent_covered": 95.23809523809524, "percent_covered_display": "95", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [583], "excluded_lines": []}, "RiskAnalyzer._infer_required_skills": {"executed_lines": [597, 598, 601, 605, 608, 611, 612, 614, 615, 617, 618, 620, 621, 624, 625, 626, 627, 628, 629, 631], "summary": {"covered_lines": 20, "num_statements": 23, "percent_covered": 86.95652173913044, "percent_covered_display": "87", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [602, 606, 609], "excluded_lines": []}, "RiskAnalyzer._map_risk_to_mitigation_category": {"executed_lines": [635, 654], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "RiskAnalyzer._get_mitigation_priority": {"executed_lines": [659, 660, 662, 663, 664, 665, 666, 667, 669, 670, 671, 672, 673, 674, 676, 677, 678, 679, 680], "summary": {"covered_lines": 19, "num_statements": 19, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 20, 23, 24, 26, 93, 146, 187, 250, 304, 355, 380, 406, 449, 480, 528, 563, 595, 633, 656], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"RiskAnalyzer": {"executed_lines": [29, 51, 91, 102, 104, 116, 117, 118, 121, 122, 125, 126, 129, 130, 133, 138, 141, 143, 144, 155, 164, 165, 168, 169, 172, 173, 176, 177, 180, 181, 182, 183, 185, 196, 199, 200, 201, 203, 204, 213, 225, 237, 248, 259, 262, 274, 275, 276, 278, 290, 302, 313, 316, 317, 319, 320, 321, 322, 323, 326, 327, 328, 329, 341, 342, 353, 357, 359, 368, 378, 382, 384, 385, 394, 404, 408, 409, 412, 413, 424, 425, 436, 437, 447, 451, 452, 455, 456, 467, 468, 469, 478, 482, 485, 486, 488, 489, 490, 492, 493, 496, 497, 499, 500, 501, 502, 505, 511, 513, 514, 516, 519, 520, 521, 522, 524, 526, 530, 533, 534, 535, 536, 538, 539, 541, 544, 545, 548, 550, 551, 553, 554, 561, 565, 567, 568, 570, 571, 572, 573, 574, 576, 577, 578, 581, 582, 585, 586, 587, 589, 590, 591, 593, 597, 598, 601, 605, 608, 611, 612, 614, 615, 617, 618, 620, 621, 624, 625, 626, 627, 628, 629, 631, 635, 654, 659, 660, 662, 663, 664, 665, 666, 667, 669, 670, 671, 672, 673, 674, 676, 677, 678, 679, 680], "summary": {"covered_lines": 194, "num_statements": 214, "percent_covered": 90.65420560747664, "percent_covered_display": "91", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [214, 226, 238, 263, 279, 291, 343, 360, 369, 386, 395, 414, 426, 438, 457, 542, 583, 602, 606, 609], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 20, 23, 24, 26, 93, 146, 187, 250, 304, 355, 380, 406, 449, 480, 528, 563, 595, 633, 656], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/task_planner/timeline.py": {"executed_lines": [1, 8, 9, 11, 18, 21, 22, 24, 31, 32, 33, 36, 47, 49, 67, 68, 70, 73, 76, 79, 82, 85, 96, 97, 98, 100, 101, 103, 113, 115, 116, 119, 120, 123, 124, 125, 128, 130, 131, 133, 142, 145, 148, 151, 154, 155, 156, 157, 159, 161, 162, 164, 166, 168, 170, 190, 192, 194, 199, 200, 201, 204, 206, 208, 210, 212, 213, 214, 215, 216, 217, 218, 221, 225, 228, 231, 232, 234, 236, 238, 240, 241, 244, 246, 247, 248, 249, 251, 253, 255, 258, 266, 267, 268, 269, 272, 280, 281, 282, 284, 287, 288, 290, 297, 298, 301, 309, 311, 313, 314, 317, 320, 322, 323, 324, 328, 332, 333, 336, 338, 340, 342, 345, 346, 349, 350, 351, 352, 355, 356, 358, 360, 361, 364, 365, 366, 367, 368, 369, 372, 373, 374, 376, 378, 380, 383, 385, 386, 389, 390, 391, 392, 393, 395, 397, 402, 404, 411, 414, 417, 419, 420, 423, 424, 425, 426, 427, 428, 430, 432, 437], "summary": {"covered_lines": 169, "num_statements": 175, "percent_covered": 96.57142857142857, "percent_covered_display": "97", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [126, 222, 223, 325, 329, 334], "excluded_lines": [], "functions": {"TimelineGenerator.__init__": {"executed_lines": [31, 32, 33, 36, 47], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimelineGenerator.generate_timeline": {"executed_lines": [67, 68, 70, 73, 76, 79, 82, 85, 96, 97, 98, 100, 101], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimelineGenerator.estimate_task_duration": {"executed_lines": [113, 115, 116, 119, 120, 123, 124, 125, 128, 130, 131], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [126], "excluded_lines": []}, "TimelineGenerator.calculate_critical_path": {"executed_lines": [142, 145, 148, 151, 154, 155, 156, 157, 159, 161, 162], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimelineGenerator._apply_effort_buffers": {"executed_lines": [166, 168, 170, 190, 192], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimelineGenerator._schedule_tasks": {"executed_lines": [199, 200, 201, 204, 206, 208, 210, 212, 213, 214, 215, 216, 217, 218, 221, 225, 228, 231, 232, 234, 236], "summary": {"covered_lines": 21, "num_statements": 23, "percent_covered": 91.30434782608695, "percent_covered_display": "91", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [222, 223], "excluded_lines": []}, "TimelineGenerator._calculate_end_date": {"executed_lines": [240, 241, 244, 246, 247, 248, 249, 251], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimelineGenerator._generate_milestones": {"executed_lines": [255, 258, 266, 267, 268, 269, 272, 280, 281, 282, 284, 287, 288, 290, 297, 298, 301, 309], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimelineGenerator._calculate_timeline_confidence": {"executed_lines": [313, 314, 317, 320, 322, 323, 324, 328, 332, 333, 336], "summary": {"covered_lines": 11, "num_statements": 14, "percent_covered": 78.57142857142857, "percent_covered_display": "79", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [325, 329, 334], "excluded_lines": []}, "TimelineGenerator._hours_to_days": {"executed_lines": [340], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimelineGenerator._topological_sort": {"executed_lines": [345, 346, 349, 350, 351, 352, 355, 356, 358, 360, 361, 364, 365, 366, 367, 368, 369, 372, 373, 374, 376], "summary": {"covered_lines": 21, "num_statements": 21, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimelineGenerator._calculate_earliest_times": {"executed_lines": [380, 383, 385, 386, 389, 390, 391, 392, 393, 395, 397, 402], "summary": {"covered_lines": 12, "num_statements": 12, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TimelineGenerator._calculate_latest_times": {"executed_lines": [411, 414, 417, 419, 420, 423, 424, 425, 426, 427, 428, 430, 432, 437], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 18, 21, 22, 24, 49, 103, 133, 164, 194, 238, 253, 311, 338, 342, 378, 404], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TimelineGenerator": {"executed_lines": [31, 32, 33, 36, 47, 67, 68, 70, 73, 76, 79, 82, 85, 96, 97, 98, 100, 101, 113, 115, 116, 119, 120, 123, 124, 125, 128, 130, 131, 142, 145, 148, 151, 154, 155, 156, 157, 159, 161, 162, 166, 168, 170, 190, 192, 199, 200, 201, 204, 206, 208, 210, 212, 213, 214, 215, 216, 217, 218, 221, 225, 228, 231, 232, 234, 236, 240, 241, 244, 246, 247, 248, 249, 251, 255, 258, 266, 267, 268, 269, 272, 280, 281, 282, 284, 287, 288, 290, 297, 298, 301, 309, 313, 314, 317, 320, 322, 323, 324, 328, 332, 333, 336, 340, 345, 346, 349, 350, 351, 352, 355, 356, 358, 360, 361, 364, 365, 366, 367, 368, 369, 372, 373, 374, 376, 380, 383, 385, 386, 389, 390, 391, 392, 393, 395, 397, 402, 411, 414, 417, 419, 420, 423, 424, 425, 426, 427, 428, 430, 432, 437], "summary": {"covered_lines": 151, "num_statements": 157, "percent_covered": 96.17834394904459, "percent_covered_display": "96", "missing_lines": 6, "excluded_lines": 0}, "missing_lines": [126, 222, 223, 325, 329, 334], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 11, 18, 21, 22, 24, 49, 103, 133, 164, 194, 238, 253, 311, 338, 342, 378, 404], "summary": {"covered_lines": 18, "num_statements": 18, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/technical_architect/__init__.py": {"executed_lines": [1, 7, 8, 15, 22, 29, 36], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": [], "functions": {"": {"executed_lines": [1, 7, 8, 15, 22, 29, 36], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"": {"executed_lines": [1, 7, 8, 15, 22, 29, 36], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/technical_architect/agent.py": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 26, 27, 29, 36, 37, 38, 39, 42, 43, 44, 45, 48, 49, 51, 53, 55, 58, 71, 83, 95, 96, 98, 100, 101, 102, 104, 105, 116, 118, 120, 121, 124, 127, 130, 133, 136, 137, 140, 141, 144, 145, 148, 149, 153, 158, 160, 163, 181, 183, 184, 186, 187, 188, 190, 191, 197, 199, 201, 202, 204, 205, 207, 208, 210, 211, 213, 215, 217, 219, 220, 221, 224, 227, 230, 231, 234, 238, 239, 245, 250, 251, 253, 254, 256, 257, 259, 260, 263, 264, 266, 268, 273, 290, 320, 324, 326, 331, 334, 337, 345, 362, 384, 388, 390, 395, 396, 397, 398, 399, 402, 405, 422, 453, 457, 459, 462, 465, 482, 513, 517, 519, 524, 541, 562, 566, 568, 570, 572, 573, 574, 575, 576, 577, 578, 580, 583, 586, 588, 589, 591, 592, 594, 595, 597, 599, 601, 602, 603, 604, 607, 608, 610, 613, 616, 619, 621, 623, 625, 626, 628, 629, 630, 631, 633, 635, 637, 638, 639, 641, 643, 644, 645, 647, 649, 650, 652, 653, 654, 655, 656, 657, 658, 660, 662, 664, 665, 674, 676, 677], "summary": {"covered_lines": 202, "num_statements": 222, "percent_covered": 90.990990990991, "percent_covered_display": "91", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [108, 111, 114, 241, 242, 243, 605, 606, 667, 668, 669, 670, 672, 679, 680, 681, 682, 683, 684, 686], "excluded_lines": [], "functions": {"TechnicalArchitectAgent.__init__": {"executed_lines": [36, 37, 38, 39, 42, 43, 44, 45, 48, 49, 51], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent.can_handle_query": {"executed_lines": [55, 58, 71, 83, 95, 96, 98, 100, 101, 102, 104, 105], "summary": {"covered_lines": 12, "num_statements": 15, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [108, 111, 114], "excluded_lines": []}, "TechnicalArchitectAgent.process_query": {"executed_lines": [118, 120, 121, 124, 127, 130, 133, 136, 137, 140, 141, 144, 145, 148, 149, 153, 158, 160, 163, 181, 183, 184, 186, 187, 188, 190, 191], "summary": {"covered_lines": 27, "num_statements": 27, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._classify_query_type": {"executed_lines": [199, 201, 202, 204, 205, 207, 208, 210, 211, 213], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._perform_search": {"executed_lines": [217, 219, 220, 221, 224, 227, 230, 231, 234, 238, 239], "summary": {"covered_lines": 11, "num_statements": 14, "percent_covered": 78.57142857142857, "percent_covered_display": "79", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [241, 242, 243], "excluded_lines": []}, "TechnicalArchitectAgent._enhance_query_for_search": {"executed_lines": [250, 251, 253, 254, 256, 257, 259, 260, 263, 264, 266], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._generate_analysis_response": {"executed_lines": [273, 290, 320, 324], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._generate_design_response": {"executed_lines": [331, 334, 337, 345, 362, 384, 388], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._generate_validation_response": {"executed_lines": [395, 396, 397, 398, 399, 402, 405, 422, 453, 457], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._generate_standards_response": {"executed_lines": [462, 465, 482, 513, 517], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._generate_general_response": {"executed_lines": [524, 541, 562, 566], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._determine_design_type": {"executed_lines": [570, 572, 573, 574, 575, 576, 577, 578], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._extract_requirements_from_query": {"executed_lines": [583, 586, 588, 589, 591, 592, 594, 595, 597], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._extract_sources": {"executed_lines": [601, 602, 603, 604, 607, 608], "summary": {"covered_lines": 6, "num_statements": 8, "percent_covered": 75.0, "percent_covered_display": "75", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [605, 606], "excluded_lines": []}, "TechnicalArchitectAgent._calculate_confidence": {"executed_lines": [613, 616, 619, 621], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._format_solid_scores": {"executed_lines": [625, 626, 628, 629, 630, 631, 633], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._format_list": {"executed_lines": [637, 638, 639], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._format_dict": {"executed_lines": [643, 644, 645], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._format_design_recommendations": {"executed_lines": [649, 650, 652, 653, 654, 655, 656, 657, 658, 660], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalArchitectAgent._format_principle_scores": {"executed_lines": [664, 665], "summary": {"covered_lines": 2, "num_statements": 7, "percent_covered": 28.571428571428573, "percent_covered_display": "29", "missing_lines": 5, "excluded_lines": 0}, "missing_lines": [667, 668, 669, 670, 672], "excluded_lines": []}, "TechnicalArchitectAgent._format_validation_issues": {"executed_lines": [676, 677], "summary": {"covered_lines": 2, "num_statements": 9, "percent_covered": 22.22222222222222, "percent_covered_display": "22", "missing_lines": 7, "excluded_lines": 0}, "missing_lines": [679, 680, 681, 682, 683, 684, 686], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 26, 27, 29, 53, 116, 197, 215, 245, 268, 326, 390, 459, 519, 568, 580, 599, 610, 623, 635, 641, 647, 662, 674], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"TechnicalArchitectAgent": {"executed_lines": [36, 37, 38, 39, 42, 43, 44, 45, 48, 49, 51, 55, 58, 71, 83, 95, 96, 98, 100, 101, 102, 104, 105, 118, 120, 121, 124, 127, 130, 133, 136, 137, 140, 141, 144, 145, 148, 149, 153, 158, 160, 163, 181, 183, 184, 186, 187, 188, 190, 191, 199, 201, 202, 204, 205, 207, 208, 210, 211, 213, 217, 219, 220, 221, 224, 227, 230, 231, 234, 238, 239, 250, 251, 253, 254, 256, 257, 259, 260, 263, 264, 266, 273, 290, 320, 324, 331, 334, 337, 345, 362, 384, 388, 395, 396, 397, 398, 399, 402, 405, 422, 453, 457, 462, 465, 482, 513, 517, 524, 541, 562, 566, 570, 572, 573, 574, 575, 576, 577, 578, 583, 586, 588, 589, 591, 592, 594, 595, 597, 601, 602, 603, 604, 607, 608, 613, 616, 619, 621, 625, 626, 628, 629, 630, 631, 633, 637, 638, 639, 643, 644, 645, 649, 650, 652, 653, 654, 655, 656, 657, 658, 660, 664, 665, 676, 677], "summary": {"covered_lines": 166, "num_statements": 186, "percent_covered": 89.24731182795699, "percent_covered_display": "89", "missing_lines": 20, "excluded_lines": 0}, "missing_lines": [108, 111, 114, 241, 242, 243, 605, 606, 667, 668, 669, 670, 672, 679, 680, 681, 682, 683, 684, 686], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 23, 26, 27, 29, 53, 116, 197, 215, 245, 268, 326, 390, 459, 519, 568, 580, 599, 610, 623, 635, 641, 647, 662, 674], "summary": {"covered_lines": 36, "num_statements": 36, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/technical_architect/analyzer.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 34, 35, 37, 38, 39, 40, 41, 44, 45, 46, 48, 49, 50, 51, 52, 53, 54, 57, 58, 59, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 74, 76, 85, 93, 95, 97, 99, 102, 103, 104, 105, 106, 109, 112, 115, 118, 121, 122, 125, 127, 128, 130, 132, 133, 134, 137, 138, 141, 148, 151, 154, 157, 160, 162, 172, 174, 175, 176, 177, 178, 179, 180, 181, 186, 189, 190, 191, 194, 195, 196, 199, 201, 203, 206, 207, 209, 211, 212, 213, 214, 217, 218, 219, 220, 221, 223, 225, 227, 230, 235, 236, 237, 240, 241, 242, 243, 245, 247, 249, 251, 252, 253, 255, 257, 259, 265, 267, 269, 274, 276, 278, 283, 285, 288, 289, 290, 291, 293, 294, 295, 298, 300, 302, 308, 310, 312, 319, 321, 323, 326, 327, 330, 331, 334, 337, 339, 342, 343, 346, 347, 350, 357, 358, 359, 360, 362, 364, 367, 368, 370, 373, 374, 377, 381, 383, 386, 387, 390, 391, 392, 393, 394, 397, 407, 409, 411, 413, 414, 415, 418, 420, 422, 424, 425, 426, 427, 428, 429, 431, 432, 434, 436, 438, 441, 442, 444, 445, 446, 447, 452, 455, 456, 457, 459, 460, 461, 462, 463, 465, 468, 469, 470, 472, 475, 477, 478, 479, 480, 483, 485, 486, 488, 490, 491, 492, 494, 496, 497, 499, 500, 502, 503, 504, 506, 514, 516, 519, 520, 521, 524, 525, 527, 528, 531, 532, 535, 537, 539, 542, 543, 547, 548, 551, 552, 555, 557, 559, 562, 566, 567, 568, 571, 572, 573, 575, 577, 579, 580, 583, 586, 587, 589], "summary": {"covered_lines": 286, "num_statements": 307, "percent_covered": 93.15960912052117, "percent_covered_display": "93", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [139, 164, 165, 166, 167, 168, 169, 170, 182, 183, 184, 335, 448, 449, 450, 481, 489, 533, 544, 553, 563], "excluded_lines": [], "functions": {"ArchitectureAnalyzer.__init__": {"executed_lines": [76, 85, 93], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer.analyze_codebase": {"executed_lines": [97, 99, 102, 103, 104, 105, 106, 109, 112, 115, 118, 121, 122, 125, 127, 128], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._analyze_component": {"executed_lines": [132, 133, 134, 137, 138, 141, 148, 151, 154, 157, 160, 162], "summary": {"covered_lines": 12, "num_statements": 20, "percent_covered": 60.0, "percent_covered_display": "60", "missing_lines": 8, "excluded_lines": 0}, "missing_lines": [139, 164, 165, 166, 167, 168, 169, 170], "excluded_lines": []}, "ArchitectureAnalyzer._determine_component_type": {"executed_lines": [174, 175, 176, 177, 178, 179, 180, 181], "summary": {"covered_lines": 8, "num_statements": 11, "percent_covered": 72.**************, "percent_covered_display": "73", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [182, 183, 184], "excluded_lines": []}, "ArchitectureAnalyzer._extract_component_name": {"executed_lines": [189, 190, 191, 194, 195, 196, 199], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._extract_responsibilities": {"executed_lines": [203, 206, 207, 209, 211, 212, 213, 214, 217, 218, 219, 220, 221, 223], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._extract_dependencies": {"executed_lines": [227, 230, 235, 236, 237, 240, 241, 242, 243, 245], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._detect_component_patterns": {"executed_lines": [249, 251, 252, 253, 255], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._detect_factory_pattern": {"executed_lines": [259, 265], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._detect_singleton_pattern": {"executed_lines": [269, 274], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._detect_strategy_pattern": {"executed_lines": [278, 283], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._detect_dependency_injection": {"executed_lines": [288, 289, 290, 291, 293, 294, 295, 298], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._detect_repository_pattern": {"executed_lines": [302, 308], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._detect_rag_pipeline_pattern": {"executed_lines": [312, 319], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._check_solid_violations": {"executed_lines": [323, 326, 327, 330, 331, 334, 337], "summary": {"covered_lines": 7, "num_statements": 8, "percent_covered": 87.5, "percent_covered_display": "88", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [335], "excluded_lines": []}, "ArchitectureAnalyzer._has_srp_violation": {"executed_lines": [342, 343, 346, 347, 350, 357, 358, 359, 360, 362], "summary": {"covered_lines": 10, "num_statements": 10, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._has_ocp_violation": {"executed_lines": [367, 368], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._has_dip_violation": {"executed_lines": [373, 374, 377, 381], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._calculate_complexity": {"executed_lines": [386, 387, 390, 391, 392, 393, 394, 397, 407], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._detect_patterns": {"executed_lines": [411, 413, 414, 415, 418], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._analyze_solid_compliance": {"executed_lines": [422, 424, 425, 426, 427, 428, 429, 431, 432, 434, 436], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._analyze_single_responsibility": {"executed_lines": [441, 442, 444, 445, 446, 447], "summary": {"covered_lines": 6, "num_statements": 9, "percent_covered": 66.66666666666667, "percent_covered_display": "67", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [448, 449, 450], "excluded_lines": []}, "ArchitectureAnalyzer._analyze_open_closed": {"executed_lines": [455, 456, 457, 459, 460, 461, 462, 463], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._analyze_liskov_substitution": {"executed_lines": [468, 469, 470], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._analyze_interface_segregation": {"executed_lines": [475, 477, 478, 479, 480], "summary": {"covered_lines": 5, "num_statements": 6, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [481], "excluded_lines": []}, "ArchitectureAnalyzer._analyze_dependency_inversion": {"executed_lines": [485, 486, 488, 490, 491, 492], "summary": {"covered_lines": 6, "num_statements": 7, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [489], "excluded_lines": []}, "ArchitectureAnalyzer._calculate_quality_metrics": {"executed_lines": [496, 497, 499, 500, 502, 503, 504, 506], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer._generate_recommendations": {"executed_lines": [516, 519, 520, 521, 524, 525, 527, 528, 531, 532, 535], "summary": {"covered_lines": 11, "num_statements": 12, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [533], "excluded_lines": []}, "ArchitectureAnalyzer._identify_concerns": {"executed_lines": [539, 542, 543, 547, 548, 551, 552, 555], "summary": {"covered_lines": 8, "num_statements": 10, "percent_covered": 80.0, "percent_covered_display": "80", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [544, 553], "excluded_lines": []}, "ArchitectureAnalyzer._identify_strengths": {"executed_lines": [559, 562, 566, 567, 568, 571, 572, 573, 575], "summary": {"covered_lines": 9, "num_statements": 10, "percent_covered": 90.0, "percent_covered_display": "90", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [563], "excluded_lines": []}, "ArchitectureAnalyzer._calculate_confidence": {"executed_lines": [579, 580, 583, 586, 587, 589], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 34, 35, 37, 38, 39, 40, 41, 44, 45, 46, 48, 49, 50, 51, 52, 53, 54, 57, 58, 59, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 74, 95, 130, 172, 186, 201, 225, 247, 257, 267, 276, 285, 300, 310, 321, 339, 364, 370, 383, 409, 420, 438, 452, 465, 472, 483, 494, 514, 537, 557, 577], "summary": {"covered_lines": 75, "num_statements": 75, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ArchitecturePattern": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOLIDPrinciple": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ComponentAnalysis": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ArchitectureAnalyzer": {"executed_lines": [76, 85, 93, 97, 99, 102, 103, 104, 105, 106, 109, 112, 115, 118, 121, 122, 125, 127, 128, 132, 133, 134, 137, 138, 141, 148, 151, 154, 157, 160, 162, 174, 175, 176, 177, 178, 179, 180, 181, 189, 190, 191, 194, 195, 196, 199, 203, 206, 207, 209, 211, 212, 213, 214, 217, 218, 219, 220, 221, 223, 227, 230, 235, 236, 237, 240, 241, 242, 243, 245, 249, 251, 252, 253, 255, 259, 265, 269, 274, 278, 283, 288, 289, 290, 291, 293, 294, 295, 298, 302, 308, 312, 319, 323, 326, 327, 330, 331, 334, 337, 342, 343, 346, 347, 350, 357, 358, 359, 360, 362, 367, 368, 373, 374, 377, 381, 386, 387, 390, 391, 392, 393, 394, 397, 407, 411, 413, 414, 415, 418, 422, 424, 425, 426, 427, 428, 429, 431, 432, 434, 436, 441, 442, 444, 445, 446, 447, 455, 456, 457, 459, 460, 461, 462, 463, 468, 469, 470, 475, 477, 478, 479, 480, 485, 486, 488, 490, 491, 492, 496, 497, 499, 500, 502, 503, 504, 506, 516, 519, 520, 521, 524, 525, 527, 528, 531, 532, 535, 539, 542, 543, 547, 548, 551, 552, 555, 559, 562, 566, 567, 568, 571, 572, 573, 575, 579, 580, 583, 586, 587, 589], "summary": {"covered_lines": 211, "num_statements": 232, "percent_covered": 90.94827586206897, "percent_covered_display": "91", "missing_lines": 21, "excluded_lines": 0}, "missing_lines": [139, 164, 165, 166, 167, 168, 169, 170, 182, 183, 184, 335, 448, 449, 450, 481, 489, 533, 544, 553, 563], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 14, 16, 19, 20, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 34, 35, 37, 38, 39, 40, 41, 44, 45, 46, 48, 49, 50, 51, 52, 53, 54, 57, 58, 59, 61, 62, 63, 64, 65, 66, 67, 68, 71, 72, 74, 95, 130, 172, 186, 201, 225, 247, 257, 267, 276, 285, 300, 310, 321, 339, 364, 370, 383, 409, 420, 438, 452, 465, 472, 483, 494, 514, 537, 557, 577], "summary": {"covered_lines": 75, "num_statements": 75, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/technical_architect/designer.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 18, 19, 21, 22, 23, 24, 25, 26, 29, 30, 32, 33, 34, 35, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 58, 59, 60, 61, 62, 63, 64, 65, 66, 69, 70, 72, 74, 82, 89, 91, 99, 101, 102, 105, 110, 113, 115, 116, 118, 125, 132, 135, 136, 137, 138, 139, 142, 143, 144, 145, 148, 149, 151, 154, 156, 158, 165, 171, 174, 175, 176, 177, 178, 181, 185, 186, 188, 195, 202, 233, 234, 236, 243, 249, 252, 253, 270, 271, 272, 288, 289, 291, 295, 337, 338, 339, 341, 351, 355, 357, 371, 373, 394, 396, 415, 417, 436, 438, 454, 456, 459, 493, 496, 523, 525, 527, 531, 534, 537, 538, 539, 541, 542, 544, 545, 548, 549, 552, 553, 555, 557, 562, 565, 568, 569, 571], "summary": {"covered_lines": 143, "num_statements": 147, "percent_covered": 97.27891156462584, "percent_covered_display": "97", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [179, 180, 182, 183], "excluded_lines": [], "functions": {"TechnicalDesigner.__init__": {"executed_lines": [74, 82, 89], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner.generate_design": {"executed_lines": [99, 101, 102, 105, 110, 113, 115, 116], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._generate_architecture_overview": {"executed_lines": [125, 132, 135, 136, 137, 138, 139, 142, 143, 144, 145, 148, 149, 151, 154, 156], "summary": {"covered_lines": 16, "num_statements": 16, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._generate_component_design": {"executed_lines": [165, 171, 174, 175, 176, 177, 178, 181, 185, 186], "summary": {"covered_lines": 10, "num_statements": 14, "percent_covered": 71.42857142857143, "percent_covered_display": "71", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [179, 180, 182, 183], "excluded_lines": []}, "TechnicalDesigner._generate_api_design": {"executed_lines": [195, 202, 233, 234], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._generate_refactoring_plan": {"executed_lines": [243, 249, 252, 253, 270, 271, 272, 288, 289], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._create_pattern_recommendation": {"executed_lines": [295, 337, 338, 339, 341], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._create_solid_improvement_recommendation": {"executed_lines": [355, 357], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._create_layered_architecture_recommendation": {"executed_lines": [373], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._create_agent_component_recommendation": {"executed_lines": [396], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._create_pipeline_component_recommendation": {"executed_lines": [417], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._create_api_component_recommendation": {"executed_lines": [438], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._generate_architecture_diagrams": {"executed_lines": [456, 459, 493, 496, 523, 525], "summary": {"covered_lines": 6, "num_statements": 6, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._generate_compliance_notes": {"executed_lines": [531, 534, 537, 538, 539, 541, 542, 544, 545, 548, 549, 552, 553, 555], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner._calculate_design_confidence": {"executed_lines": [562, 565, 568, 569, 571], "summary": {"covered_lines": 5, "num_statements": 5, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 18, 19, 21, 22, 23, 24, 25, 26, 29, 30, 32, 33, 34, 35, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 58, 59, 60, 61, 62, 63, 64, 65, 66, 69, 70, 72, 91, 118, 158, 188, 236, 291, 351, 371, 394, 415, 436, 454, 527, 557], "summary": {"covered_lines": 57, "num_statements": 57, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DesignType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DesignPriority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DesignRecommendation": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "DesignDocument": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "TechnicalDesigner": {"executed_lines": [74, 82, 89, 99, 101, 102, 105, 110, 113, 115, 116, 125, 132, 135, 136, 137, 138, 139, 142, 143, 144, 145, 148, 149, 151, 154, 156, 165, 171, 174, 175, 176, 177, 178, 181, 185, 186, 195, 202, 233, 234, 243, 249, 252, 253, 270, 271, 272, 288, 289, 295, 337, 338, 339, 341, 355, 357, 373, 396, 417, 438, 456, 459, 493, 496, 523, 525, 531, 534, 537, 538, 539, 541, 542, 544, 545, 548, 549, 552, 553, 555, 562, 565, 568, 569, 571], "summary": {"covered_lines": 86, "num_statements": 90, "percent_covered": 95.55555555555556, "percent_covered_display": "96", "missing_lines": 4, "excluded_lines": 0}, "missing_lines": [179, 180, 182, 183], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 18, 19, 21, 22, 23, 24, 25, 26, 29, 30, 32, 33, 34, 35, 38, 39, 40, 42, 43, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 58, 59, 60, 61, 62, 63, 64, 65, 66, 69, 70, 72, 91, 118, 158, 188, 236, 291, 351, 371, 394, 415, 436, 454, 527, 557], "summary": {"covered_lines": 57, "num_statements": 57, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/technical_architect/standards.py": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 32, 33, 35, 36, 37, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 58, 59, 60, 61, 62, 63, 66, 67, 69, 71, 72, 83, 84, 86, 88, 89, 91, 93, 95, 96, 98, 100, 101, 103, 105, 106, 114, 115, 119, 120, 121, 122, 129, 130, 133, 134, 138, 144, 146, 148, 150, 152, 155, 156, 157, 158, 161, 162, 163, 164, 165, 167, 168, 170, 172, 173, 174, 176, 177, 180, 182, 184, 187, 189, 195, 197, 198, 199, 201, 202, 204, 206, 207, 210, 211, 213, 216, 217, 219, 221, 225, 228, 234, 235, 236, 237, 238, 240, 241, 242, 243, 244, 245, 255, 258, 259, 261, 262, 263, 265, 267, 268, 269, 270, 271, 273, 282, 284, 286, 288, 289, 292, 303, 304, 305, 307, 309, 311, 312, 315, 323, 324, 325, 327, 329, 331, 333, 334, 335, 338, 346, 347, 348, 350, 352, 354, 356, 357, 359, 362, 363, 364, 365, 366, 367, 368, 370, 371, 372, 373, 374, 375, 377, 379, 381, 395, 396, 399, 400, 401, 402, 403, 404, 405, 406, 409, 410, 411, 413, 415, 417, 425, 426, 428, 429, 430, 432, 433, 434, 435, 437, 440, 442, 444, 446, 447, 448, 451, 459, 460, 461, 463, 465, 467, 468, 474, 475, 477, 478, 479, 481, 482, 484, 488, 489, 490, 493, 494, 495, 498, 499, 502, 503, 506, 509, 511, 513, 515], "summary": {"covered_lines": 250, "num_statements": 259, "percent_covered": 96.52509652509653, "percent_covered_display": "97", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [116, 191, 192, 193, 369, 436, 438, 485, 486], "excluded_lines": [], "functions": {"ProjectStandardsManager.__init__": {"executed_lines": [71, 72, 83, 84], "summary": {"covered_lines": 4, "num_statements": 4, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager.get_standards_context": {"executed_lines": [88, 89, 91], "summary": {"covered_lines": 3, "num_statements": 3, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager.get_rules_for_category": {"executed_lines": [95, 96], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager.get_mandatory_rules": {"executed_lines": [100, 101], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager.validate_against_standards": {"executed_lines": [105, 106, 114, 115, 119, 120, 121, 122, 129, 130, 133, 134, 138, 144, 146], "summary": {"covered_lines": 15, "num_statements": 16, "percent_covered": 93.75, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [116], "excluded_lines": []}, "ProjectStandardsManager._load_standards_context": {"executed_lines": [150, 152, 155, 156, 157, 158, 161, 162, 163, 164, 165, 167, 168], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager._parse_document": {"executed_lines": [172, 173, 174, 176, 177, 180, 182, 184, 187, 189], "summary": {"covered_lines": 10, "num_statements": 13, "percent_covered": 76.92307692307692, "percent_covered_display": "77", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [191, 192, 193], "excluded_lines": []}, "ProjectStandardsManager._split_into_sections": {"executed_lines": [197, 198, 199, 201, 202, 204, 206, 207, 210, 211, 213, 216, 217, 219], "summary": {"covered_lines": 14, "num_statements": 14, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager._extract_rules_from_section": {"executed_lines": [225, 228, 234, 235, 236, 237, 238, 240, 241, 242, 243, 244, 245, 255, 258, 259, 261, 262, 263, 265, 267, 268, 269, 270, 271, 273, 282, 284], "summary": {"covered_lines": 28, "num_statements": 28, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager._categorize_rule": {"executed_lines": [288, 289, 292, 303, 304, 305, 307], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager._extract_tags": {"executed_lines": [311, 312, 315, 323, 324, 325, 327], "summary": {"covered_lines": 7, "num_statements": 7, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager._extract_design_principles": {"executed_lines": [331, 333, 334, 335, 338, 346, 347, 348, 350], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager._extract_technology_stack": {"executed_lines": [354, 356, 357, 359, 362, 363, 364, 365, 366, 367, 368, 370, 371, 372, 373, 374, 375, 377], "summary": {"covered_lines": 18, "num_statements": 19, "percent_covered": 94.73684210526316, "percent_covered_display": "95", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [369], "excluded_lines": []}, "ProjectStandardsManager._extract_coding_standards": {"executed_lines": [381, 395, 396, 399, 400, 401, 402, 403, 404, 405, 406, 409, 410, 411, 413], "summary": {"covered_lines": 15, "num_statements": 15, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager._extract_testing_requirements": {"executed_lines": [417, 425, 426, 428, 429, 430, 432, 433, 434, 435, 437, 440], "summary": {"covered_lines": 12, "num_statements": 14, "percent_covered": 85.71428571428571, "percent_covered_display": "86", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [436, 438], "excluded_lines": []}, "ProjectStandardsManager._extract_architectural_constraints": {"executed_lines": [444, 446, 447, 448, 451, 459, 460, 461, 463], "summary": {"covered_lines": 9, "num_statements": 9, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager._check_rule_compliance": {"executed_lines": [467, 468, 474, 475, 477, 478, 479, 481, 482, 484, 488, 489, 490, 493, 494, 495, 498, 499, 502, 503, 506, 509], "summary": {"covered_lines": 22, "num_statements": 24, "percent_covered": 91.66666666666667, "percent_covered_display": "92", "missing_lines": 2, "excluded_lines": 0}, "missing_lines": [485, 486], "excluded_lines": []}, "ProjectStandardsManager.get_standards_summary": {"executed_lines": [513, 515], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 32, 33, 35, 36, 37, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 58, 59, 60, 61, 62, 63, 66, 67, 69, 86, 93, 98, 103, 148, 170, 195, 221, 286, 309, 329, 352, 379, 415, 442, 465, 511], "summary": {"covered_lines": 58, "num_statements": 58, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"DocumentType": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StandardsPriority": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StandardsRule": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "StandardsContext": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ProjectStandardsManager": {"executed_lines": [71, 72, 83, 84, 88, 89, 91, 95, 96, 100, 101, 105, 106, 114, 115, 119, 120, 121, 122, 129, 130, 133, 134, 138, 144, 146, 150, 152, 155, 156, 157, 158, 161, 162, 163, 164, 165, 167, 168, 172, 173, 174, 176, 177, 180, 182, 184, 187, 189, 197, 198, 199, 201, 202, 204, 206, 207, 210, 211, 213, 216, 217, 219, 225, 228, 234, 235, 236, 237, 238, 240, 241, 242, 243, 244, 245, 255, 258, 259, 261, 262, 263, 265, 267, 268, 269, 270, 271, 273, 282, 284, 288, 289, 292, 303, 304, 305, 307, 311, 312, 315, 323, 324, 325, 327, 331, 333, 334, 335, 338, 346, 347, 348, 350, 354, 356, 357, 359, 362, 363, 364, 365, 366, 367, 368, 370, 371, 372, 373, 374, 375, 377, 381, 395, 396, 399, 400, 401, 402, 403, 404, 405, 406, 409, 410, 411, 413, 417, 425, 426, 428, 429, 430, 432, 433, 434, 435, 437, 440, 444, 446, 447, 448, 451, 459, 460, 461, 463, 467, 468, 474, 475, 477, 478, 479, 481, 482, 484, 488, 489, 490, 493, 494, 495, 498, 499, 502, 503, 506, 509, 513, 515], "summary": {"covered_lines": 192, "num_statements": 201, "percent_covered": 95.5223880597015, "percent_covered_display": "96", "missing_lines": 9, "excluded_lines": 0}, "missing_lines": [116, 191, 192, 193, 369, 436, 438, 485, 486], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 12, 13, 15, 18, 19, 21, 22, 23, 24, 25, 26, 27, 28, 29, 32, 33, 35, 36, 37, 40, 41, 42, 44, 45, 46, 47, 48, 49, 50, 51, 54, 55, 56, 58, 59, 60, 61, 62, 63, 66, 67, 69, 86, 93, 98, 103, 148, 170, 195, 221, 286, 309, 329, 352, 379, 415, 442, 465, 511], "summary": {"covered_lines": 58, "num_statements": 58, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}, "src/technical_architect/validator.py": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 18, 19, 21, 22, 23, 24, 25, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 56, 57, 59, 61, 69, 71, 73, 75, 78, 79, 81, 82, 83, 84, 86, 87, 90, 91, 94, 97, 100, 102, 103, 105, 107, 108, 110, 111, 112, 115, 116, 125, 126, 129, 130, 144, 145, 154, 155, 157, 158, 160, 161, 163, 165, 166, 168, 169, 170, 173, 179, 193, 194, 203, 204, 206, 207, 209, 210, 212, 214, 215, 217, 218, 221, 226, 227, 229, 231, 233, 234, 236, 238, 239, 241, 242, 243, 246, 247, 256, 257, 262, 263, 265, 266, 268, 270, 271, 273, 274, 275, 278, 280, 281, 290, 291, 294, 307, 308, 310, 311, 313, 315, 318, 328, 329, 330, 331, 332, 334, 336, 338, 341, 342, 343, 344, 347, 348, 350, 351, 353, 354, 357, 358, 360, 363, 365, 369, 371, 372, 374, 375, 376, 378, 380, 382, 384], "summary": {"covered_lines": 168, "num_statements": 178, "percent_covered": 94.38202247191012, "percent_covered_display": "94", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [131, 140, 141, 180, 189, 190, 295, 304, 305, 361], "excluded_lines": [], "functions": {"SOLIDValidator.__init__": {"executed_lines": [61, 69], "summary": {"covered_lines": 2, "num_statements": 2, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOLIDValidator.validate_architecture": {"executed_lines": [73, 75, 78, 79, 81, 82, 83, 84, 86, 87, 90, 91, 94, 97, 100, 102, 103], "summary": {"covered_lines": 17, "num_statements": 17, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOLIDValidator._validate_srp": {"executed_lines": [107, 108, 110, 111, 112, 115, 116, 125, 126, 129, 130, 144, 145, 154, 155, 157, 158, 160, 161], "summary": {"covered_lines": 19, "num_statements": 22, "percent_covered": 86.36363636363636, "percent_covered_display": "86", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [131, 140, 141], "excluded_lines": []}, "SOLIDValidator._validate_ocp": {"executed_lines": [165, 166, 168, 169, 170, 173, 179, 193, 194, 203, 204, 206, 207, 209, 210], "summary": {"covered_lines": 15, "num_statements": 18, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [180, 189, 190], "excluded_lines": []}, "SOLIDValidator._validate_lsp": {"executed_lines": [214, 215, 217, 218, 221, 226, 227, 229, 231, 233, 234], "summary": {"covered_lines": 11, "num_statements": 11, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOLIDValidator._validate_isp": {"executed_lines": [238, 239, 241, 242, 243, 246, 247, 256, 257, 262, 263, 265, 266], "summary": {"covered_lines": 13, "num_statements": 13, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOLIDValidator._validate_dip": {"executed_lines": [270, 271, 273, 274, 275, 278, 280, 281, 290, 291, 294, 307, 308, 310, 311], "summary": {"covered_lines": 15, "num_statements": 18, "percent_covered": 83.**************, "percent_covered_display": "83", "missing_lines": 3, "excluded_lines": 0}, "missing_lines": [295, 304, 305], "excluded_lines": []}, "SOLIDValidator._analyze_method_concerns": {"executed_lines": [315, 318, 328, 329, 330, 331, 332, 334], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOLIDValidator._generate_recommendations": {"executed_lines": [338, 341, 342, 343, 344, 347, 348, 350, 351, 353, 354, 357, 358, 360, 363], "summary": {"covered_lines": 15, "num_statements": 16, "percent_covered": 93.75, "percent_covered_display": "94", "missing_lines": 1, "excluded_lines": 0}, "missing_lines": [361], "excluded_lines": []}, "SOLIDValidator._categorize_components": {"executed_lines": [369, 371, 372, 374, 375, 376, 378, 380], "summary": {"covered_lines": 8, "num_statements": 8, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOLIDValidator._generate_summary": {"executed_lines": [384], "summary": {"covered_lines": 1, "num_statements": 1, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 18, 19, 21, 22, 23, 24, 25, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 56, 57, 59, 71, 105, 163, 212, 236, 268, 313, 336, 365, 382], "summary": {"covered_lines": 44, "num_statements": 44, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}, "classes": {"ValidationSeverity": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ValidationIssue": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "ValidationReport": {"executed_lines": [], "summary": {"covered_lines": 0, "num_statements": 0, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}, "SOLIDValidator": {"executed_lines": [61, 69, 73, 75, 78, 79, 81, 82, 83, 84, 86, 87, 90, 91, 94, 97, 100, 102, 103, 107, 108, 110, 111, 112, 115, 116, 125, 126, 129, 130, 144, 145, 154, 155, 157, 158, 160, 161, 165, 166, 168, 169, 170, 173, 179, 193, 194, 203, 204, 206, 207, 209, 210, 214, 215, 217, 218, 221, 226, 227, 229, 231, 233, 234, 238, 239, 241, 242, 243, 246, 247, 256, 257, 262, 263, 265, 266, 270, 271, 273, 274, 275, 278, 280, 281, 290, 291, 294, 307, 308, 310, 311, 315, 318, 328, 329, 330, 331, 332, 334, 338, 341, 342, 343, 344, 347, 348, 350, 351, 353, 354, 357, 358, 360, 363, 369, 371, 372, 374, 375, 376, 378, 380, 384], "summary": {"covered_lines": 124, "num_statements": 134, "percent_covered": 92.53731343283582, "percent_covered_display": "93", "missing_lines": 10, "excluded_lines": 0}, "missing_lines": [131, 140, 141, 180, 189, 190, 295, 304, 305, 361], "excluded_lines": []}, "": {"executed_lines": [1, 8, 9, 10, 11, 13, 15, 18, 19, 21, 22, 23, 24, 25, 28, 29, 30, 32, 33, 34, 35, 36, 37, 38, 39, 40, 43, 44, 45, 47, 48, 49, 50, 51, 52, 53, 56, 57, 59, 71, 105, 163, 212, 236, 268, 313, 336, 365, 382], "summary": {"covered_lines": 44, "num_statements": 44, "percent_covered": 100.0, "percent_covered_display": "100", "missing_lines": 0, "excluded_lines": 0}, "missing_lines": [], "excluded_lines": []}}}}, "totals": {"covered_lines": 5156, "num_statements": 6706, "percent_covered": 76.88637041455414, "percent_covered_display": "77", "missing_lines": 1550, "excluded_lines": 233}}